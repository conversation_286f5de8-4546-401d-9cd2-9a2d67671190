{"template": "flux-ai-template", "theme": "dark", "header": {"logo": "哔哩工具箱", "nav": {"introduce": "功能特点", "benefit": "产品优势", "showcase": "案例展示", "stats": "数据统计", "pricing": "价格", "testimonial": "用户评价", "faq": "常见问题"}, "cta": {"login": "登录", "signup": "开始使用"}, "userMenu": {"myOrders": "我的订单", "signOut": "退出登录"}, "search": {"placeholder": "搜索哔哩工具箱(Bili Tool)..."}}, "hero": {"title": "哔哩工具箱(<PERSON><PERSON>l) - 您的一站式在线工具平台", "subtitle": "使用哔哩工具箱高效完成各类日常任务", "description": "领先的在线工具集合平台，提供图像处理、文本编辑、格式转换等多种实用功能，助您提高工作效率", "cta": {"primary": "免费开始", "secondary": "浏览工具"}}, "branding": {"title": "全球用户的信赖之选", "subtitle": "加入数千名已在使用哔哩工具箱(Bili Tool)的满意用户", "brands": [{"name": "公司 1", "logo": "/brands/1.svg"}, {"name": "公司 2", "logo": "/brands/2.svg"}, {"name": "公司 3", "logo": "/brands/3.svg"}]}, "introduce": {"title": "先进的在线工具集合", "subtitle": "为日常任务提供强大功能", "description": "哔哩工具箱(bili-tool.com)提供全面的在线实用工具，旨在简化您的工作流程并提高生产力", "image": "/features/intro.jpg", "features": [{"title": "图像处理工具", "description": "强大的图像编辑、转换和优化工具，满足您所有视觉内容需求", "icon": "sparkles"}, {"title": "文本与数据处理", "description": "高效的文本编辑、格式化和数据转换工具，助力内容管理", "icon": "wand"}, {"title": "开发者实用工具", "description": "为程序员提供格式化、验证和代码转换等基本开发工具", "icon": "palette"}]}, "benefit": {"title": "为什么选择我们", "subtitle": "与众不同的优势", "benefits": [{"title": "提升生产力", "description": "通过自动化工作流程更快地构建和部署", "icon": "speed"}, {"title": "更好的协作", "description": "使用内置协作工具无缝协作", "icon": "team"}, {"title": "增强安全性", "description": "企业级安全保护您的代码和数据", "icon": "shield"}]}, "usage": {"title": "使用方法", "subtitle": "三个简单步骤开始使用", "steps": [{"title": "创建您的项目", "description": "使用我们直观的项目创建向导设置您的项目", "image": "/usage/step1.png"}, {"title": "配置设置", "description": "根据您的需求自定义项目设置", "image": "/usage/step2.png"}, {"title": "开始开发", "description": "使用我们强大的开发工具开始编码", "image": "/usage/step3.png"}]}, "feature": {"title": "可用模型", "subtitle": "选择最适合您需求的模型", "items": [{"title": "Flux Pro", "description": "为高端商业用途优化的旗舰模型", "icon": "star"}, {"title": "Flux 1.1 Pro Ultra", "description": "针对高速生成优化的4K分辨率生成器", "icon": "bolt"}, {"title": "Flux Schnell", "description": "最快图像生成的涡轮模式", "icon": "rocket"}]}, "showcase": {"title": "实际效果", "subtitle": "使用我们平台构建的真实项目", "gallery": [{"title": "项目 1", "description": "现代网络应用", "image": "/gallery/1.jpg"}, {"title": "项目 2", "description": "移动应用开发", "image": "/gallery/2.jpg"}, {"title": "项目 3", "description": "企业解决方案", "image": "/gallery/3.jpg"}]}, "stats": {"title": "数据说话", "subtitle": "我们在开发者社区的影响力", "stats": [{"value": "10M+", "label": "下载量", "description": "正在使用我们平台的活跃开发者"}, {"value": "50K+", "label": "项目", "description": "成功完成的项目"}, {"value": "99.9%", "label": "运行时间", "description": "平台可靠性"}, {"value": "24/7", "label": "支持", "description": "随时为您服务"}]}, "pricing": {"title": "选择您的方案", "subtitle": "立即开始创建惊艳图像", "perMonth": "/月", "contactUs": "联系我们", "getStarted": "开始使用", "buyNow": "立即购买", "pleaseLogin": "请先登录后再进行购买", "plans": [{"name": "基础版", "price": "$9.99", "amount": 9.99, "description": "个人用户的完美选择", "features": ["每月100张图片", "基础图像生成", "邮件支持", "HD分辨率 (1024x1024)", "访问Flux基础模型"]}, {"name": "专业版", "price": "$29.99", "amount": 29.99, "description": "适合专业创作者", "features": ["每月1000张图片", "高级图像生成", "优先支持", "4K超高清分辨率", "访问所有Flux模型", "API访问"]}, {"name": "企业版", "price": "联系我们", "description": "为大型团队定制", "features": ["无限图片生成", "企业级功能", "专属客户经理", "自定义API集成", "SLA保障", "高级安全特性"]}]}, "testimonial": {"title": "用户评价", "subtitle": "听听他们怎么说", "testimonials": [{"content": "这个平台彻底改变了我们创建内容的方式。生产力提升令人难以置信。", "author": {"name": "张三", "title": "技术总监", "company": "科技公司", "image": "/testimonials/1.jpg"}}]}, "faq": {"title": "常见问题", "subtitle": "找到常见问题的答案", "faqs": [{"question": "如何开始使用？", "answer": "注册一个免费账户并按照我们的快速入门指南操作。几分钟内即可开始使用。"}, {"question": "提供什么样的支持？", "answer": "我们提供多种支持选项，包括社区论坛、文档以及付费方案的专属支持。"}, {"question": "可以与现有工具集成吗？", "answer": "是的！我们的平台可以与大多数流行的开发工具和服务集成。"}]}, "auth": {"signInTitle": "登录您的账户", "signInWithGoogle": "使用 Google 账号登录", "signInWithGithub": "使用 GitHub 账号登录", "signInWithWechat": "使用微信扫码登录", "signingIn": "正在登录...", "oauthError": "登录过程中发生错误，请重试。", "authError": "认证错误，请重试。"}, "cta": {"title": "准备开始了吗？", "subtitle": "加入已经在使用我们平台的数千名开发者", "cta": {"primary": "免费试用", "secondary": "联系销售"}}, "footer": {"copyright": "保留所有权利", "about": {"title": "关于", "about": "关于我们", "blog": "博客"}, "support": {"title": "支持", "helpCenter": "帮助中心", "contactUs": "联系我们"}, "legal": {"title": "法律", "privacy": "隐私政策", "terms": "服务条款"}, "language": {"title": "语言", "english": "English", "chinese": "中文", "japanese": "日本語"}, "social": {"title": "关注我们", "github": "GitHub", "twitter": "Twitter", "linkedin": "LinkedIn"}}, "orders": {"title": "我的订单", "description": "查看您的订单历史和支付状态", "noOrders": "您还没有任何订单", "orderDetails": {"purchase": "购买", "orderId": "订单号", "amount": "金额", "orderDate": "下单时间", "paidDate": "支付时间"}}, "tools": {"common": {"allTools": "全部工具", "favorites": "收藏夹", "recentUsed": "最近使用", "searchPlaceholder": "搜索工具...", "noToolsFound": "未找到工具", "tryDifferentKeywords": "尝试使用不同的关键词或选择其他分类", "toolsCount": "{{count}}个工具"}, "ui": {"categoryTitle": "工具分类", "noCategories": "暂无分类", "favorite": "收藏", "addToFavorites": "添加到收藏夹", "removeFromFavorites": "从收藏夹移除", "toolDetails": "工具详情", "back": "返回", "share": "分享", "copy": "复制", "copied": "已复制！", "loading": "加载中..."}, "categories": {"commonTools": "常用工具", "jsonTools": "JSON工具", "yamlTools": "YAML工具", "securityTools": "安全工具", "domainIp": "域名/IP", "devTools": "开发工具", "utilityTools": "实用工具", "designTools": "设计工具", "textProcessing": "文字处理", "encodingTools": "编解码工具", "imageProcessing": "图片加工", "videoAudio": "视频音频"}, "subcategories": {"hashTools": "哈希计算", "encryptionTools": "加解密工具", "passwordTools": "密码工具", "securityCheck": "安全检测", "textEncode": "文本编解码", "mediaEncode": "媒体编解码", "devEncode": "开发编解码", "serverEncode": "服务器工具"}, "whois": {"name": "Whois查询", "description": "查询域名注册信息，包括所有者、注册商、注册日期和到期日期等详细信息"}, "ip-location": {"name": "IP定位", "description": "通过IP地址查询地理位置，包括国家、城市、经纬度和ISP提供商信息"}, "dns-lookup": {"name": "DNS解析", "description": "查询域名的DNS记录，包括A、AAAA、MX、TXT、NS等类型记录"}, "domain-info": {"name": "域名信息", "description": "全面分析域名信息，包括服务器类型、HTTP头、SSL证书详情等"}, "json-formatter": {"name": "JSON格式化", "description": "格式化和美化JSON数据，提高可读性，支持缩进和语法高亮"}, "json-js-convert": {"name": "JSON与JS互转", "description": "JSON与JavaScript对象互相转换工具，支持格式化和语法高亮"}, "qrcode": {"title": "二维码生成器", "toolDescription": {"title": "工具说明", "content": "本工具可以生成二维码，支持自定义大小、颜色和边距。生成的二维码可以直接下载为PNG图片。所有处理都在浏览器本地完成，不会上传您的数据。"}, "configOptions": {"title": "配置选项", "inputText": {"label": "输入文本", "placeholder": "输入要生成二维码的文本..."}, "size": {"label": "二维码大小", "pixels": "像素"}, "color": {"foreground": "前景色", "background": "背景色"}, "includeMargin": "包含边距"}, "preview": {"title": "二维码预览", "placeholder": "二维码将显示在这里..."}, "buttons": {"loadExample": "加载示例", "clear": "清空", "download": "下载二维码"}}, "imgCompress": {"title": "图片压缩工具", "toolDescription": {"title": "工具说明", "content": "这是一个强大的在线图片压缩工具，可以帮助你压缩图片文件大小，同时保持图片质量。支持多种图片格式，并提供灵活的压缩选项。"}, "compressionSettings": {"title": "压缩设置", "quality": "压缩质量", "lowQuality": "低质量", "highQuality": "高质量", "maxWidth": "最大宽度", "maxHeight": "最大高度", "format": "输出格式", "maintainAspectRatio": "保持原始宽高比"}, "uploadArea": {"title": "上传图片", "supportedFormats": "支持 JPG、PNG、WebP 等常见图片格式"}, "fileList": {"title": "文件列表", "removeAll": "清空列表"}, "buttons": {"startCompression": "开始压缩", "processing": "处理中...", "downloadCompressed": "下载压缩后的图片"}, "results": {"title": "压缩结果", "originalSize": "原始大小", "compressedSize": "压缩后大小", "compressionRatio": "压缩比例", "qualitySetting": "质量设置", "compressedImageAlt": "压缩后的图片 {filename}"}, "errors": {"invalidFiles": "请上传有效的图片文件", "noFiles": "请先上传图片文件", "compressionFailed": "压缩失败，请重试", "imageLoadFailed": "图片加载失败", "fileReadFailed": "文件读取失败", "canvasContextFailed": "画布上下文创建失败", "compressionError": "压缩过程中出现错误"}}, "aesEncryption": {"title": "AES加解密", "description": "在线AES算法加解密工具，支持多种密钥长度和加密模式", "navigation": {"back": "返回工具列表"}, "modes": {"encrypt": "加密", "decrypt": "解密"}, "settings": {"title": "AES设置", "keySize": {"label": "密钥大小", "128bit": "128位", "192bit": "192位", "256bit": "256位"}, "mode": {"label": "加密模式"}, "padding": {"label": "填充模式", "noPadding": "无填充"}}, "input": {"encryptTitle": "待加密文本", "decryptTitle": "待解密文本", "encryptPlaceholder": "在此输入需要加密的文本...", "decryptPlaceholder": "在此输入需要解密的文本..."}, "output": {"encryptTitle": "加密结果", "decryptTitle": "解密结果", "encryptEmpty": "加密结果将显示在这里", "decryptEmpty": "解密结果将显示在这里", "clickButton": "点击下方按钮开始{action}操作"}, "key": {"title": "密钥 (Hex格式)", "placeholder": "输入16进制格式的密钥...", "requiredLength": "{size}位密钥需要{chars}个十六进制字符"}, "iv": {"title": "初始化向量 IV (Hex格式)", "placeholder": "输入16进制格式的初始化向量...", "requiredLength": "IV需要32个十六进制字符 (16字节)"}, "buttons": {"clear": "清空", "loadExample": "加载示例", "copy": "复制", "copyResult": "复制结果", "generateKey": "生成随机密钥", "generateIv": "生成随机IV"}, "processing": {"encrypting": "加密中...", "decrypting": "解密中..."}, "errors": {"emptyEncryptText": "请输入需要加密的文本", "emptyDecryptText": "请输入需要解密的文本", "emptyKey": "请输入密钥", "emptyIv": "当前模式需要初始化向量(IV)", "encryptError": "加密错误", "decryptError": "解密错误", "unknownEncryptError": "加密过程中出现未知错误", "unknownDecryptError": "解密过程中出现未知错误"}, "examples": {"encryptText": "这是一段需要加密的敏感信息，请妥善保管密钥！", "decryptText": "aXcFkL9V1rxIB5xAEJHZCvpVH40D3+9x7wKGZ9lwpeI="}, "info": {"title": "关于AES加密", "features": {"title": "AES算法特点", "items": "高级加密标准(Advanced Encryption Standard)|美国国家标准，取代DES成为全球对称加密标准|支持128位、192位和256位密钥长度|对称加密算法，加密和解密使用相同密钥|高度安全，至今未有实际可行的破解方法|高效执行，适合大量数据加密"}, "modes": {"title": "加密模式说明", "items": "ECB (电子密码本): 最简单模式，不需IV，但安全性较低|CBC (密码块链接): 需要IV，每个块与前一块加密结果相关|CFB (密码反馈): 需要IV，将块密码转换为流密码|OFB (输出反馈): 需要IV，生成密钥流进行加密|CTR (计数器): 需要IV，将块密码转换为流密码的计数器模式"}, "tips": {"title": "安全使用提示：", "items": "使用足够长度(256位)的随机密钥提高安全性|对于CBC等模式，请确保使用唯一的初始化向量(IV)|避免使用ECB模式加密重要数据|密钥管理至关重要，丢失密钥将无法解密数据|对于实际应用，推荐使用GCM或CCM等可验证加密模式|记住，对称加密面临的主要挑战是密钥分发|使用安全通道传输密钥和IV"}}}, "desEncryption": {"title": "DES加解密", "description": "在线DES算法加解密工具，支持多种加密模式和填充方式", "navigation": {"back": "返回工具列表"}, "modes": {"encrypt": "加密", "decrypt": "解密"}, "settings": {"title": "DES设置", "variant": {"label": "DES算法变体", "des": "DES (56位)", "des3": "3DES (168位)"}, "mode": {"label": "加密模式"}, "padding": {"label": "填充模式", "noPadding": "无填充"}}, "input": {"encryptTitle": "待加密文本", "decryptTitle": "待解密文本", "encryptPlaceholder": "在此输入需要加密的文本...", "decryptPlaceholder": "在此输入需要解密的文本..."}, "output": {"encryptTitle": "加密结果", "decryptTitle": "解密结果", "encryptEmpty": "加密结果将显示在这里", "decryptEmpty": "解密结果将显示在这里", "clickButton": "点击下方按钮开始{action}操作"}, "key": {"title": "密钥 (Hex格式)", "placeholder": "输入16进制格式的密钥...", "requiredLength": {"des": "DES需要16个十六进制字符 (8字节)", "des3": "3DES需要48个十六进制字符 (24字节)"}}, "iv": {"title": "初始化向量 IV (Hex格式)", "placeholder": "输入16进制格式的初始化向量...", "requiredLength": "IV需要16个十六进制字符 (8字节)"}, "buttons": {"clear": "清空", "loadExample": "加载示例", "copy": "复制", "copyResult": "复制结果", "generateKey": "生成随机密钥", "generateIv": "生成随机IV"}, "processing": {"encrypting": "加密中...", "decrypting": "解密中..."}, "errors": {"emptyEncryptText": "请输入需要加密的文本", "emptyDecryptText": "请输入需要解密的文本", "emptyKey": "请输入密钥", "emptyIv": "当前模式需要初始化向量(IV)", "encryptError": "加密错误", "decryptError": "解密错误", "unknownEncryptError": "加密过程中出现未知错误", "unknownDecryptError": "解密过程中出现未知错误"}, "examples": {"encryptText": "这是一段需要加密的敏感信息，请使用DES加密！", "decryptText": "U2FsdGVkX1+XrNkJpK9+5zfI0AEPC+rfYVYI0n4KRz0jKJJ7PL19Qw=="}, "info": {"title": "关于DES加密", "features": {"title": "DES算法特点", "items": "数据加密标准(Data Encryption Standard)|1977年由美国联邦信息处理标准(FIPS)提出|使用56位密钥长度|由于密钥长度较短，现已不推荐用于高安全性需求|3DES(Triple DES)提供了增强的安全性"}, "modes": {"title": "加密模式说明", "items": "ECB (电子密码本): 简单但安全性较低|CBC (密码块链接): 每个块与前一块加密结果相关|CFB (密码反馈): 将块密码转换为流密码|OFB (输出反馈): 生成密钥流进行加密"}, "security": {"title": "安全考虑", "items": "DES因为密钥长度短(56位)，现已被视为不安全|对于需要高安全性的应用，推荐使用3DES或AES|3DES虽然比DES安全，但速度较慢|现代应用中，AES已经成为首选的对称加密算法"}}}, "ipLocation": {"title": "IP定位", "description": "通过IP地址查询地理位置，包括国家、城市、经纬度和ISP提供商信息", "navigation": {"back": "返回工具列表"}, "form": {"ipAddress": "IP地址", "placeholder": "输入IPv4或IPv6地址，如: *******", "submit": "查询", "searching": "查询中..."}, "errors": {"enterIpAddress": "请输入IP地址", "invalidIpAddress": "请输入有效的IP地址 (IPv4或IPv6)", "invalidIpv4Range": "IPv4地址格式不正确，数值范围应为0-255", "queryFailed": "查询失败", "retryLater": "查询失败，请稍后重试"}, "results": {"title": "查询结果", "actions": {"refresh": "刷新", "copy": "复制到剪贴板", "download": "下载结果"}, "geoLocation": {"title": "地理位置", "country": "国家", "registeredCountry": "注册国家", "region": "地区", "latitude": "纬度", "longitude": "经度", "timezone": "时区"}, "chinaIpInfo": {"title": "中国IP查询结果", "isp": "ISP", "networkType": "网络类型", "province": "省份", "city": "城市", "district": "区县", "areaCode": "区域编码"}, "basicInfo": {"title": "基本信息", "ipAddress": "IP地址", "subnet": "网段", "networkType": "网络类型"}, "ispInfo": {"title": "ISP信息", "provider": "运营商", "asn": "ASN", "organization": "组织"}, "disclaimer": "注意：IP地理位置信息仅供参考，精确度可能会受到多种因素影响。"}, "instructions": {"title": "使用说明", "steps": ["在输入框中输入您想要查询的IP地址（如：*******）", "点击\"查询\"按钮获取IP地址的地理位置信息", "查询结果将显示IP所在国家/地区、城市、ISP提供商等信息", "您可以使用工具栏上的按钮复制或下载查询结果"], "tip": "提示：本工具支持IPv4和IPv6地址查询。由于IP地址分配和使用的复杂性，某些IP地址的位置信息可能不准确或仅显示大致区域。"}}, "img-resize": {"title": "图片调整大小", "description": "支持按像素或百分比调整图片大小，保持图片质量", "dragAndDrop": "拖放图片到这里", "orClickToSelect": "或点击选择图片", "supportedFormats": "支持的格式：PNG、JPG、JPEG、GIF、WebP", "resizeByPixel": "按像素调整", "resizeByPercentage": "按百分比调整", "presetSize": "预设尺寸", "width": "宽度", "height": "高度", "percentage": "百分比", "maintainAspectRatio": "保持长宽比", "resize": "调整大小", "originalSize": "原始尺寸", "newSize": "新尺寸", "error": {"invalidFile": "无效的文件", "pleaseUploadImage": "请上传图片文件"}, "success": {"title": "调整成功", "description": "图片已调整大小，请点击下载按钮保存"}, "download": {"button": "下载图片", "success": {"title": "下载成功", "description": "图片已保存到本地"}}, "preview": {"title": "调整后预览"}, "presetSizes": {"4:3": "4:3 横屏", "3:4": "3:4 竖屏", "16:9": "16:9 宽屏", "9:16": "9:16 竖屏", "custom": "自定义尺寸"}, "presetSizeNote": "选择预设尺寸后，图片将按比例调整到对应尺寸"}}, "privacy": {"title": "隐私政策", "lastUpdated": "最后更新时间：2023年12月1日", "backToHome": "返回首页", "content": {"welcome": "欢迎访问在线工具箱（以下简称\"我们\"、\"本网站\"）。我们非常重视您的隐私和个人信息保护。本隐私政策旨在帮助您了解我们如何收集、使用、存储和保护您的个人信息，以及您享有的相关权利。在使用我们的服务前，请仔细阅读本隐私政策。", "sections": {"collection": {"title": "1. 信息收集", "intro": "在您使用我们的工具和服务时，我们可能会收集以下类型的信息：", "items": ["您主动提供的信息：包括您在注册账户、使用特定工具、联系我们或参与调查时提供的个人信息，如姓名、电子邮件地址等。", "自动收集的信息：当您访问或使用我们的网站时，我们会自动收集一些技术数据，如IP地址、浏览器类型、设备信息、访问时间等。", "工具使用数据：我们记录您使用工具的方式，如查询的域名、IP地址或其他相关数据，以改进我们的服务。"]}, "usage": {"title": "2. 信息使用", "intro": "我们使用收集的信息主要用于以下目的：", "items": ["提供、维护和改进我们的工具和服务", "处理您的请求和回应您的问询", "发送重要通知，如服务条款变更或政策更新", "在您同意的情况下，向您发送营销和推广信息", "防止欺诈和滥用，确保网站安全", "进行数据分析和研究，以改善用户体验"]}, "sharing": {"title": "3. 信息共享", "intro": "我们尊重您的隐私，不会出售、交易或出租您的个人信息给第三方。但在下列情况下，我们可能会分享您的信息：", "items": ["经您明确同意后的分享", "与我们的服务提供商和合作伙伴共享，以协助我们提供服务", "法律要求或政府机构的合法请求", "保护我们的权利、财产或安全，以及我们用户或公众的权利、财产或安全"]}, "security": {"title": "4. 数据安全", "content": "我们采取适当的技术和组织措施来保护您的个人信息，防止未经授权的访问、使用、泄露或损坏。尽管我们努力保护您的个人信息，但互联网传输和电子存储并非绝对安全，我们无法保证信息的绝对安全。"}, "cookies": {"title": "5. <PERSON><PERSON>和类似技术", "content": "我们使用Cookie和类似技术来收集使用情况信息，改善用户体验，并记住您的偏好设置。您可以通过浏览器设置来控制Cookie，但这可能会影响某些网站功能的可用性。"}, "rights": {"title": "6. 您的权利", "intro": "根据适用的数据保护法律，您可能拥有以下权利：", "items": ["访问权：了解我们处理的有关您的个人信息", "更正权：更正不准确或不完整的个人信息", "删除权：在特定情况下要求删除您的个人信息", "限制处理权：在特定情况下限制我们处理您的个人信息", "数据可携权：在技术可行的情况下以结构化、常用和机器可读的格式接收您的个人信息", "反对权：反对特定类型的处理活动"], "outro": "如需行使这些权利，请通过本政策末尾提供的联系方式与我们联系。"}, "children": {"title": "7. 儿童隐私", "content": "我们的服务不针对13岁以下的儿童。如果我们发现误收集了13岁以下儿童的个人信息，将立即删除相关数据。如果您是父母或监护人，且发现您的孩子向我们提供了个人信息，请与我们联系。"}, "thirdParty": {"title": "8. 第三方链接", "content": "我们的网站可能包含指向第三方网站的链接。我们对这些网站的隐私政策和内容不负责任。建议您在使用第三方网站前查阅其隐私政策。"}, "updates": {"title": "9. 政策更新", "content": "我们可能会不时更新本隐私政策，以反映我们的做法变更或法律要求的变化。更新后的政策将在网站上发布，重大变更会通过适当方式通知您。建议您定期查看本政策以了解最新信息。"}, "contact": {"title": "10. 联系我们", "intro": "如果您对本隐私政策有任何疑问、意见或投诉，请通过以下方式联系我们：", "email": "电子邮件：<EMAIL>", "address": "邮寄地址：北京市海淀区中关村大街1号", "phone": "联系电话：+86-10-12345678", "outro": "我们将在收到您的请求后30天内回复。"}}}}, "blog": {"title": "博客文章", "subtitle": "探索我们的技术教程、产品更新和行业见解", "backToHome": "返回首页", "search": {"placeholder": "搜索文章..."}, "categories": {"all": "全部", "tutorials": "技术教程", "updates": "产品更新", "news": "行业资讯", "tips": "使用技巧"}, "featured": {"label": "精选文章"}, "post": {"readMore": "阅读全文", "readTime": "阅读时间", "author": "作者", "date": "发布日期", "noResults": {"title": "未找到相关文章", "description": "尝试使用不同的搜索词或浏览其他分类", "action": "查看所有文章"}}}}