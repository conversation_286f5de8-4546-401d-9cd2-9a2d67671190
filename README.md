# ToolHub - Professional Tool Aggregation Platform

<div align="center">

![ToolHub Logo](public/logo.jpg)

**A comprehensive web-based tool aggregation platform for developers, system administrators, and technical professionals**

[![Next.js](https://img.shields.io/badge/Next.js-15.2.3-black?style=flat-square&logo=next.js)](https://nextjs.org/)
[![React](https://img.shields.io/badge/React-18.2.0-blue?style=flat-square&logo=react)](https://reactjs.org/)
[![TypeScript](https://img.shields.io/badge/TypeScript-5.x-blue?style=flat-square&logo=typescript)](https://www.typescriptlang.org/)
[![License](https://img.shields.io/badge/License-MIT-green?style=flat-square)](LICENSE)

[🌐 Live Demo](https://toolbox-web.vercel.app) | [📖 Documentation](docs/) | [🐛 Report Bug](https://github.com/wenhaofree/toolbox-web/issues) | [✨ Request Feature](https://github.com/wenhaofree/toolbox-web/issues)

</div>

## 🚀 About ToolHub

ToolHub is a modern, comprehensive tool aggregation platform that brings together **70+ professional tools** in one unified interface. Built with Next.js 15 and the latest React ecosystem, it provides developers, system administrators, and technical professionals with a one-stop solution for their daily workflow needs.

### 🎯 Why ToolHub?

- **🔧 70+ Professional Tools**: From domain/IP utilities to development tools, security utilities, and image processing
- **🌍 Multilingual Support**: Full internationalization with Chinese and English support
- **🎨 Modern UI/UX**: Clean, responsive design built with Radix UI and Tailwind CSS
- **⚡ High Performance**: Server-side rendering with Next.js 15 and Turbopack
- **🔐 Secure Authentication**: OAuth integration with Google and GitHub
- **💳 Premium Features**: Stripe integration for advanced functionalities
- **📱 Mobile-First**: Responsive design that works seamlessly across all devices

## 🛠️ Tool Categories

### 🌐 Domain & IP Tools
- **Whois Lookup**: Query domain registration information
- **IP Geolocation**: Locate IP addresses with detailed geographic data
- **DNS Lookup**: Comprehensive DNS record analysis
- **Domain Information**: Complete domain analysis and monitoring

### 💻 Development Tools
- **JSON Formatter**: Format, validate, and beautify JSON data
- **Code Compressor**: Minify JavaScript, CSS, and HTML
- **Regex Tester**: Test and debug regular expressions
- **Base64 Encoder/Decoder**: Encode and decode Base64 strings
- **JWT Decoder**: Decode and analyze JSON Web Tokens
- **Text Diff**: Compare text files and highlight differences

### 🔒 Security Tools
- **Hash Calculator**: Generate MD5, SHA-1, SHA-256, and other hashes
- **AES/DES Encryption**: Encrypt and decrypt data with various algorithms
- **Password Generator**: Generate secure random passwords
- **Random IP Generator**: Create random IP addresses for testing

### 🖼️ Image Processing
- **Image Compression**: Reduce image file sizes while maintaining quality
- **Image Resize**: Resize images to specific dimensions
- **Watermark Tool**: Add text or image watermarks
- **Color Extractor**: Extract color palettes from images

### 📊 Data Conversion
- **JSON to YAML**: Convert between JSON and YAML formats
- **CSV to JSON**: Transform CSV data to JSON and vice versa
- **YAML Formatter**: Format and validate YAML files
- **Color Converter**: Convert between different color formats (HEX, RGB, HSL)

### 🔧 Utility Tools
- **QR Code Generator**: Create customizable QR codes
- **Word Counter**: Count words, characters, and paragraphs
- **Case Converter**: Convert text between different cases
- **URL Encoder/Decoder**: Encode and decode URLs

## ✨ Key Features

- 🚀 **Next.js 15** with React 18 and TypeScript
- ⚡ **Turbopack** support for lightning-fast development
- 🎨 **Modern UI** built with Radix UI and Tailwind CSS
- 🌐 **Internationalization** with next-intl (Chinese/English)
- 🔐 **Authentication** via NextAuth.js with OAuth providers
- 💳 **Payment Integration** with Stripe for premium features
- 📊 **Database** management with Prisma ORM and PostgreSQL
- 🔔 **Toast Notifications** with Sonner
- 📱 **Responsive Design** optimized for all devices
- 🎯 **Tool Favorites** and usage history tracking
- � **Advanced Search** and filtering capabilities

## 技术栈

### 核心框架
- Next.js 15.0.3
- React 19.0.0-rc
- TypeScript 5.x

### UI框架
- Tailwind CSS 3.4.1
- Radix UI Components
  - Accordion
  - Dialog
  - Dropdown Menu
  - Slot
- Lucide React (图标)

### 状态管理与工具
- next-intl 3.26.3 (国际化)
- next-auth 4.24.11 (认证)
- Stripe 17.5.0 (支付)
- date-fns 4.1.0 (日期处理)
- UUID 11.0.4

### 数据库
- Prisma 6.1.0
- Prisma Client

## 环境要求

- Node.js 18.17 或更高版本
- pnpm 8.0 或更高版本（推荐）
- MySQL 8.0 或更高版本（推荐）

## 快速开始

### 1. 克隆项目

```bash
git clone https://github.com/your-username/NextSphere.git
cd NextSphere
```

### 2. 安装依赖

```bash
pnpm install
```

### 3. 环境变量配置

```bash
cp .env.example .env.local
```

配置以下环境变量：

| 变量名 | 说明 | 示例 |
|-------|------|------|
| DATABASE_URL | 数据库连接URL | mysql://user:pass@host:3306/db |
| NEXTAUTH_SECRET | NextAuth.js 密钥 | your-secret-key |
| AUTH_GOOGLE_ID | Google OAuth ID | google-oauth-id |
| AUTH_GOOGLE_SECRET | Google OAuth Secret | google-oauth-secret |
| AUTH_GITHUB_ID | GitHub OAuth ID | github-oauth-id |
| AUTH_GITHUB_SECRET | GitHub OAuth Secret | github-oauth-secret |
| STRIPE_PUBLIC_KEY | Stripe 公钥 | pk_test_xxx |
| STRIPE_PRIVATE_KEY | Stripe 私钥 | sk_test_xxx |
| STRIPE_WEBHOOK_SECRET | Stripe Webhook 密钥 | whsec_xxx |

### 4. 数据库初始化

```bash
# 拉取数据库架构
pnpm db:pull

# 推送架构变更
pnpm db:push

# 生成Prisma Client
pnpm db:generate

# 或者一键同步
pnpm db:sync
```

### 5. 启动开发服务器

```bash
pnpm dev
```

访问 [http://localhost:3000](http://localhost:3000) 查看应用

## 可用的脚本命令

```bash
# 开发环境启动（使用Turbopack）
pnpm dev

# 生产环境构建
pnpm build

# 生产环境启动
pnpm start

# ESLint 代码检查
pnpm lint

# Prisma 数据库操作
pnpm db:push     # 推送数据库变更
pnpm db:pull     # 拉取数据库架构
pnpm db:generate # 生成Prisma Client
pnpm db:studio   # 启动Prisma Studio
pnpm db:sync     # 同步数据库架构
```

## 部署

### Vercel 部署

[![Deploy with Vercel](https://vercel.com/button)](https://vercel.com/new/clone?repository-url=https%3A%2F%2Fgithub.com%2Fyour-username%2FNextSphere&env=DATABASE_URL,NEXTAUTH_SECRET,AUTH_GOOGLE_ID,AUTH_GOOGLE_SECRET,AUTH_GITHUB_ID,AUTH_GITHUB_SECRET,STRIPE_PUBLIC_KEY,STRIPE_PRIVATE_KEY,STRIPE_WEBHOOK_SECRET&project-name=nextsphere&repository-name=nextsphere)

1. Fork 本项目
2. 在 Vercel 创建新项目
3. 导入你的 GitHub 仓库
4. 配置环境变量
5. 部署

## 项目结构

```
NextSphere/
├── app/                # Next.js 应用目录
│   ├── api/           # API 路由
│   ├── [locale]/      # 国际化路由
│   └── layout.tsx     # 根布局
├── components/         # React 组件
│   ├── ui/            # UI 组件
│   └── shared/        # 共享组件
├── lib/               # 工具函数
├── prisma/            # Prisma 配置
├── public/            # 静态资源
└── styles/            # 样式文件
```

## 贡献指南

1. Fork 项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 提交 Pull Request

## 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 联系方式

- 项目作者：[WenHaoFree]
- Email：[<EMAIL>]
- GitHub：[https://github.com/wenhaofree]
