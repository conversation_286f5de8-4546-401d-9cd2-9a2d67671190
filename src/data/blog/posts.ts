import { BlogPost } from './types';

export const blogPosts: BlogPost[] = [
  {
    id: 'whois-domain-tutorial',
    title: {
      zh: '如何使用WHOIS查询域名信息：完整指南',
      en: 'How to Use WHOIS Domain Lookup: A Complete Guide'
    },
    excerpt: {
      zh: '了解如何使用我们的域名信息工具来查询域名的WHOIS信息，包括注册状态、到期日期等重要数据。本教程将指导您完成整个过程，同时解释结果中各项数据的含义。',
      en: 'Learn how to use our domain information tool to query WHOIS information, including registration status, expiration date, and other important data. This tutorial will guide you through the entire process while explaining the meaning of each data point in the results.'
    },
    date: '2023-12-05',
    author: {
      zh: '张伟',
      en: 'Zhang Wei'
    },
    readTime: {
      zh: '5分钟',
      en: '5 min read'
    },
    category: 'tutorials',
    imageUrl: 'https://images.unsplash.com/photo-1486312338219-ce68d2c6f44d',
    featured: true
  },
  {
    id: 'tools-december-update',
    title: {
      zh: '12月产品更新：新工具及功能增强',
      en: 'December Product Update: New Tools and Enhanced Features'
    },
    excerpt: {
      zh: '我们很高兴地宣布我们的工具箱在12月迎来了重大更新，包括全新的DNS查询工具、改进的用户界面以及更快的查询速度。查看本文了解所有新功能。',
      en: 'We are excited to announce a major update to our toolbox in December, including a new DNS lookup tool, improved user interface, and faster query speeds. Read this article to learn about all the new features.'
    },
    date: '2023-12-01',
    author: {
      zh: '李明',
      en: 'Li Ming'
    },
    readTime: {
      zh: '3分钟',
      en: '3 min read'
    },
    category: 'updates',
    imageUrl: 'https://images.unsplash.com/photo-1561736778-92e52a7769ef'
  },
  {
    id: 'domain-market-trends-2023',
    title: {
      zh: '2023年域名市场趋势分析',
      en: 'Domain Market Trends Analysis 2023'
    },
    excerpt: {
      zh: '随着互联网的持续扩张，域名市场正经历着显著变化。本文分析了2023年域名市场的主要趋势，包括新顶级域名的兴起、域名投资策略以及价格走势。',
      en: 'As the internet continues to expand, the domain market is undergoing significant changes. This article analyzes the main trends in the domain market for 2023, including the rise of new TLDs, domain investment strategies, and price trends.'
    },
    date: '2023-11-20',
    author: {
      zh: '王芳',
      en: 'Wang Fang'
    },
    readTime: {
      zh: '7分钟',
      en: '7 min read'
    },
    category: 'news',
    imageUrl: 'https://images.unsplash.com/photo-1460925895917-afdab827c52f'
  },
  {
    id: 'dns-optimization-guide',
    title: {
      zh: 'DNS优化技巧：提升网站加载速度',
      en: 'DNS Optimization Tips: Improve Website Loading Speed'
    },
    excerpt: {
      zh: '正确配置DNS可以显著提高网站的加载速度和用户体验。本文介绍了几种实用的DNS优化技巧，帮助您的网站更快地响应用户请求。',
      en: 'Proper DNS configuration can significantly improve website loading speed and user experience. This article introduces several practical DNS optimization tips to help your website respond faster to user requests.'
    },
    date: '2023-11-15',
    author: {
      zh: '赵强',
      en: 'Zhao Qiang'
    },
    readTime: {
      zh: '6分钟',
      en: '6 min read'
    },
    category: 'tips',
    imageUrl: 'https://images.unsplash.com/photo-1558494949-ef010cbdcc31'
  },
  {
    id: 'ip-geolocation-applications',
    title: {
      zh: 'IP地理位置信息的10个实用应用场景',
      en: '10 Practical Applications of IP Geolocation Information'
    },
    excerpt: {
      zh: 'IP地理位置信息不仅用于分析网站流量，还有许多其他实用应用。本文探讨了IP地理位置数据在内容本地化、安全验证等方面的10个应用场景。',
      en: 'IP geolocation information is not just for analyzing website traffic, but has many other practical applications. This article explores 10 applications of IP geolocation data in content localization, security verification, and more.'
    },
    date: '2023-11-10',
    author: {
      zh: '陈静',
      en: 'Chen Jing'
    },
    readTime: {
      zh: '4分钟',
      en: '4 min read'
    },
    category: 'tips',
    imageUrl: 'https://images.unsplash.com/photo-1551808525-51a94da548ce'
  },
  {
    id: 'secure-domain-ownership',
    title: {
      zh: '保护域名所有权：预防域名劫持的措施',
      en: 'Protecting Domain Ownership: Measures to Prevent Domain Hijacking'
    },
    excerpt: {
      zh: '域名劫持可能导致严重的业务损失和声誉损害。了解如何通过域名锁定、双因素认证等措施保护您的域名资产，预防未授权的域名转移和修改。',
      en: 'Domain hijacking can lead to serious business losses and reputation damage. Learn how to protect your domain assets through domain locking, two-factor authentication, and other measures to prevent unauthorized domain transfers and modifications.'
    },
    date: '2023-11-05',
    author: {
      zh: '李明',
      en: 'Li Ming'
    },
    readTime: {
      zh: '5分钟',
      en: '5 min read'
    },
    category: 'tutorials',
    imageUrl: 'https://images.unsplash.com/photo-1563013544-824ae1b704d3'
  }
]; 