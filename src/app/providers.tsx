'use client';

import { SessionProvider } from "next-auth/react";
import { useEffect } from "react";
import { ThemeProvider } from "next-themes";

export function Providers({ children }: { children: React.ReactNode }) {
  useEffect(() => {
    if (typeof document !== 'undefined') {
      const bodyElement = document.body;
      if (bodyElement.hasAttribute('inmaintabuse')) {
        bodyElement.removeAttribute('inmaintabuse');
      }
    }
  }, []);

  return (
    <SessionProvider>
      <ThemeProvider attribute="class" defaultTheme="system" enableSystem>
        {children}
      </ThemeProvider>
    </SessionProvider>
  );
}
