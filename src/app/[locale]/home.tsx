'use client';

import React, { useState, useEffect, useRef, createRef, useMemo, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useTheme } from 'next-themes';
import { useRouter, usePathname } from 'next/navigation';
import dynamic from 'next/dynamic';
import { 
  ChevronLeft, 
  ChevronRight, 
  ChevronDown,
  Star, 
  Clock, 
  Grid, 
  List, 
  Search,
  Filter,
  Plus,
  Heart,
  Share2,
  MoreVertical
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';
import {
  Tool,
  Notification,
  tools,
  sidebarMenu,
  getFilteredToolsByCategory,
  toggleToolFavorite,
  isToolDeveloped,
  createNotification,
  getSubMenuItems,
  getToolsForSubCategory,
  getToolsBySubCategory,
  getLocalizedToolName,
  getLocalizedToolDescription,
  getLocaleFromPathname,
  getLocalizedCategoryName,
  getLocalizedMenuItemName
} from '@/lib/tools';
import {
  getCachedTools,
  cacheTools,
  getCachedFavorites,
  updateCachedFavorites,
  getCachedRecentTools,
  updateCachedRecentTools
} from '@/lib/tools/cache';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger
} from '@/components/ui/tooltip';

// 动态导入较大的组件
const QuickNavMenu = dynamic(() => import('@/components/QuickNavMenu'), {
  loading: () => <div className="w-12 h-12 rounded-full bg-primary/20 animate-pulse" />
});

// 工具卡片组件
const ToolCard = React.memo(({ 
  tool, 
  index, 
  isHighlighted, 
  currentLocale,
  onToolClick,
  onToggleFavorite 
}: {
  tool: Tool;
  index: number;
  isHighlighted: boolean;
  currentLocale: string;
  onToolClick: (toolId: string, event: React.MouseEvent) => void;
  onToggleFavorite: (toolId: string) => void;
}) => {
  const animationDelay = `${index * 0.05}s`;
  const localizedName = getLocalizedToolName(tool, currentLocale);
  const localizedDescription = getLocalizedToolDescription(tool, currentLocale);
  
  return (
    <motion.div
      key={tool.id}
      id={tool.id}
      className={cn(
        "flex flex-col rounded-xl bg-card text-card-foreground p-5 shadow-sm hover:shadow-md transition-all duration-300 border border-border",
        "animate-fade-up"
      )}
      style={{ animationDelay }}
      animate={{
        scale: isHighlighted ? [1, 1.05, 1] : 1,
        transition: {
          duration: isHighlighted ? 0.5 : 0.2
        }
      }}
      whileHover={{ scale: 1.02 }}
      transition={{ duration: 0.2 }}
      onClick={(e) => onToolClick(tool.id, e)}
      layout
    >
      {isHighlighted && (
        <motion.div 
          className="absolute inset-0 flex items-center justify-center z-10 pointer-events-none"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
        >
          <div className="bg-primary text-primary-foreground px-4 py-2 rounded-full shadow-lg animate-pulse">
            {currentLocale === 'en' ? 'Located' : '已定位到此工具'}
          </div>
        </motion.div>
      )}
      <div className="mb-3 flex h-12 w-12 items-center justify-center rounded-lg bg-primary/5 text-primary">
        {tool.icon}
      </div>
      
      <h3 className="mb-2 text-lg font-medium">{localizedName}</h3>
      
      <p className="text-sm text-muted-foreground line-clamp-3">{localizedDescription}</p>
      
      <div className="absolute top-2 right-2 flex items-center gap-1 opacity-0 group-hover:opacity-100 transition-opacity tool-actions">
        <button
          onClick={(e) => {
            e.stopPropagation();
            onToggleFavorite(tool.id);
          }}
          className={`p-1 rounded ${
            tool.isFavorite ? 'text-red-500' : 'text-muted-foreground hover:text-red-500'
          }`}
        >
          <Heart size={16} fill={tool.isFavorite ? 'currentColor' : 'none'} />
        </button>
        <button 
          className="p-1 rounded text-muted-foreground hover:text-foreground"
          onClick={(e) => e.stopPropagation()}
        >
          <Share2 size={16} />
        </button>
        <button 
          className="p-1 rounded text-muted-foreground hover:text-foreground"
          onClick={(e) => e.stopPropagation()}
        >
          <MoreVertical size={16} />
        </button>
      </div>
    </motion.div>
  );
});

ToolCard.displayName = 'ToolCard';

export default function Home() {
  // 状态管理
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [searchQuery, setSearchQuery] = useState('');
  const [highlightedTool, setHighlightedTool] = useState<string | null>(null);
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [activeTool, setActiveTool] = useState<string | null>(null);
  const [hoveredCategory, setHoveredCategory] = useState<string | null>(null);
  const [activeCategory, setActiveCategory] = useState<string | null>(null);
  const [isQuickNavVisible, setIsQuickNavVisible] = useState(false);
  const [mounted, setMounted] = useState(false);
  
  // Hooks
  const { theme, setTheme } = useTheme();
  const router = useRouter();
  const pathname = usePathname();
  const currentLocale = getLocaleFromPathname(pathname);
  const highlightTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  
  // 工具和分类引用
  const toolRefs = useRef<Record<string, { ref: React.RefObject<any> }>>({});
  const categoryRefs = useRef<Record<string, { ref: React.RefObject<HTMLDivElement> }>>({});
  
  // 初始化缓存
  useEffect(() => {
    const cachedTools = getCachedTools();
    if (cachedTools) {
      // 使用缓存的工具数据
      tools.splice(0, tools.length, ...cachedTools);
    } else {
      // 缓存当前工具数据
      cacheTools(tools);
    }
    
    // 初始化收藏状态
    const favorites = getCachedFavorites();
    tools.forEach(tool => {
      tool.isFavorite = favorites.includes(tool.id);
    });
  }, []);

  // 初始化引用
  useEffect(() => {
    tools.forEach(tool => {
      if (!toolRefs.current[tool.id]) {
        toolRefs.current[tool.id] = { ref: createRef() };
      }
    });
    
    sidebarMenu.forEach(section => {
      if (!categoryRefs.current[section.category]) {
        categoryRefs.current[section.category] = { ref: createRef() };
      }
    });
  }, []);

  // 记忆化的工具过滤结果
  const filteredToolsByCategory = useMemo(() => 
    getFilteredToolsByCategory(searchQuery), 
    [searchQuery]
  );

  // 记忆化的分类列表
  const categories = useMemo(() => 
    Object.keys(filteredToolsByCategory),
    [filteredToolsByCategory]
  );

  // 通知处理
  const addNotification = useCallback((notification: Notification) => {
    setNotifications(prev => [...prev, notification]);
    setTimeout(() => {
      setNotifications(prev => prev.filter(n => n.id !== notification.id));
    }, 3000);
  }, []);

  // 收藏处理
  const handleToggleFavorite = useCallback((toolId: string) => {
    const tool = toggleToolFavorite(toolId);
    if (tool) {
      updateCachedFavorites(toolId, tool.isFavorite);
      addNotification(
        createNotification(
          tool.isFavorite 
            ? (currentLocale === 'en' ? 'Added to Favorites' : '已添加到收藏夹')
            : (currentLocale === 'en' ? 'Removed from Favorites' : '已从收藏夹移除'),
          getLocalizedToolName(tool, currentLocale),
          'success'
        )
      );
    }
  }, [currentLocale, addNotification]);

  // 工具导航
  const navigateToTool = useCallback((toolId: string) => {
    if (['all', 'favorites', 'recent'].includes(toolId)) {
      if (toolId === 'all') {
        router.push(`/${currentLocale}/tools`);
      } else {
        router.push(`/${currentLocale}/tools?filter=${toolId}`);
      }
    } else {
      if (isToolDeveloped(toolId)) {
        updateCachedRecentTools(toolId);
        router.push(`/${currentLocale}/tools/${toolId}`);
      } else {
        const tool = tools.find(t => t.id === toolId);
        if (tool) {
          addNotification(
            createNotification(
              '功能提示',
              `${tool.name} 功能正在开发中`,
              'info'
            )
          );
        }
      }
    }
  }, [currentLocale, router, addNotification]);

  // 工具点击处理
  const handleToolClick = useCallback((toolId: string, event: React.MouseEvent) => {
    if (event.currentTarget.closest('#quick-nav-menu')) {
      const toolElement = toolRefs.current[toolId]?.ref?.current;
      if (toolElement) {
        toolElement.scrollIntoView({ 
          behavior: 'smooth', 
          block: 'center',
          inline: 'nearest'
        });
        
        setHighlightedTool(toolId);
        
        if (highlightTimeoutRef.current) {
          clearTimeout(highlightTimeoutRef.current);
        }
        
        highlightTimeoutRef.current = setTimeout(() => {
          setHighlightedTool(null);
        }, 3000);
        
        setIsQuickNavVisible(false);
        
        addNotification(
          createNotification(
            currentLocale === 'en' ? 'Tool Located' : '已定位到工具',
            currentLocale === 'en' 
              ? `Located to ${getLocalizedToolName(tools.find(t => t.id === toolId)!, currentLocale)}`
              : `已定位到 ${getLocalizedToolName(tools.find(t => t.id === toolId)!, currentLocale)}`,
            'info'
          )
        );
        
        return;
      }
    }
    
    if (!(event.target as HTMLElement).closest('.tool-actions')) {
      navigateToTool(toolId);
    }
  }, [currentLocale, navigateToTool, addNotification]);

  // 分类点击处理
  const handleCategoryClick = useCallback((category: string) => {
    setActiveCategory(category);
    const element = categoryRefs.current[category]?.ref.current;
    if (element && typeof window !== 'undefined') {
      const headerHeight = 80;
      const elementPosition = element.getBoundingClientRect().top;
      const offsetPosition = elementPosition + window.scrollY - headerHeight;
      
      window.scrollTo({
        top: offsetPosition,
        behavior: 'smooth'
      });
    }
  }, []);

  // 清理
  useEffect(() => {
    return () => {
      if (highlightTimeoutRef.current) {
        clearTimeout(highlightTimeoutRef.current);
      }
    };
  }, []);

  // 挂载完成
  useEffect(() => {
    setMounted(true);
  }, []);

  // 快速导航提示
  useEffect(() => {
    if (mounted) {
      const timer = setTimeout(() => {
        addNotification(
          createNotification(
            '快速导航',
            '点击右下角按钮可快速跳转到任意工具类别',
            'info'
          )
        );
      }, 1000);

      return () => clearTimeout(timer);
    }
  }, [mounted, addNotification]);

  // 渲染工具卡片
  const renderToolCard = useCallback((tool: Tool, index: number) => (
    <ToolCard
      key={tool.id}
      tool={tool}
      index={index}
      isHighlighted={highlightedTool === tool.id}
      currentLocale={currentLocale}
      onToolClick={handleToolClick}
      onToggleFavorite={handleToggleFavorite}
    />
  ), [highlightedTool, currentLocale, handleToolClick, handleToggleFavorite]);

  return (
    <div className="flex min-h-screen flex-col">
      <main className="flex-1">
        <div className="animate-fade-in">
          <section className="section-container pt-16 md:pt-24">
            {/* 标题部分 */}
            <div className="mb-8 text-center">
              <h1 className="text-3xl font-bold md:text-4xl">
                {currentLocale === 'en' ? 'All Tools' : '所有工具'}
              </h1>
              <p className="mt-4 text-base text-muted-foreground">
                {currentLocale === 'en' 
                  ? 'Explore all online tools we provide to boost your productivity' 
                  : '探索我们提供的全部在线工具，助您提高工作效率'}
              </p>
            </div>
            
            {/* 分类导航 */}
            <div className="mb-8">
              <div className="bg-secondary dark:bg-secondary rounded-full px-3 py-2">
                <div className="flex flex-wrap items-center justify-center gap-2">
                  <button
                    onClick={() => handleCategoryClick("all")}
                    className={`rounded-full px-4 py-1 text-sm transition-all ${
                      activeCategory === "all" || !activeCategory
                        ? "bg-primary text-primary-foreground"
                        : "hover:bg-accent hover:text-accent-foreground"
                    }`}
                  >
                    {currentLocale === 'en' ? 'All' : '全部'}
                  </button>
                  
                  {categories.map((category) => {
                    const menuCategory = sidebarMenu.find(c => c.category === category);
                    const categoryName = menuCategory 
                      ? getLocalizedCategoryName(menuCategory, currentLocale)
                      : category;
                      
                    return (
                      <button
                        key={category}
                        onClick={() => handleCategoryClick(category)}
                        className={`rounded-full px-4 py-1 text-sm transition-all ${
                          activeCategory === category
                            ? "bg-primary text-primary-foreground"
                            : "hover:bg-accent hover:text-accent-foreground"
                        }`}
                      >
                        {categoryName}
                      </button>
                    );
                  })}
                </div>
              </div>
            </div>
            
            {/* 工具卡片区域 */}
            <AnimatePresence mode="wait">
              {Object.keys(filteredToolsByCategory).length === 0 ? (
                <motion.div 
                  key="empty"
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  exit={{ opacity: 0 }}
                  className="my-20 text-center"
                >
                  <h3 className="text-xl font-medium">未找到工具</h3>
                  <p className="mt-2 text-muted-foreground">
                    尝试使用不同的关键词或选择其他分类
                  </p>
                </motion.div>
              ) : (
                <motion.div
                  key="content"
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  exit={{ opacity: 0 }}
                >
                  {!activeCategory || activeCategory === "all" ? (
                    <div className="space-y-12">
                      {Object.entries(filteredToolsByCategory).map(([category, categoryTools]) => {
                        const menuCategory = sidebarMenu.find(c => c.category === category);
                        const categoryName = menuCategory 
                          ? getLocalizedCategoryName(menuCategory, currentLocale)
                          : category;
                        
                        return (
                          <div key={category} ref={categoryRefs.current[category]?.ref} className="scroll-mt-24">
                            <h2 className="text-xl font-semibold mb-6">{categoryName}</h2>
                            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
                              {categoryTools.map((tool, index) => renderToolCard(tool, index))}
                            </div>
                          </div>
                        );
                      })}
                    </div>
                  ) : (
                    <div className="space-y-12">
                      {activeCategory && filteredToolsByCategory[activeCategory] && (
                        <div ref={categoryRefs.current[activeCategory]?.ref} className="scroll-mt-24">
                          <h2 className="text-xl font-semibold mb-6">
                            {(() => {
                              const menuCategory = sidebarMenu.find(c => c.category === activeCategory);
                              return menuCategory 
                                ? getLocalizedCategoryName(menuCategory, currentLocale)
                                : activeCategory;
                            })()}
                          </h2>
                          <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4">
                            {filteredToolsByCategory[activeCategory].map((tool, index) => renderToolCard(tool, index))}
                          </div>
                        </div>
                      )}
                    </div>
                  )}
                </motion.div>
              )}
            </AnimatePresence>
          </section>
        </div>
      </main>

      {/* 快速导航按钮 */}
      {mounted && (
        <QuickNavMenu
          isVisible={isQuickNavVisible}
          onToggle={() => setIsQuickNavVisible(!isQuickNavVisible)}
          categories={categories}
          tools={tools}
          currentLocale={currentLocale}
          filteredToolsByCategory={filteredToolsByCategory}
          onCategoryClick={handleCategoryClick}
          onToolClick={handleToolClick}
          hoveredCategory={hoveredCategory}
          setHoveredCategory={setHoveredCategory}
        />
      )}

      {/* 通知区域 */}
      <AnimatePresence>
        {notifications.map((notification) => (
          <motion.div
            key={notification.id}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: 20 }}
            className={`fixed bottom-4 right-4 p-4 rounded-lg shadow-lg z-50 ${
              notification.type === 'success' ? 'bg-green-500' :
              notification.type === 'error' ? 'bg-red-500' :
              'bg-blue-500'
            } text-white dark:text-white`}
          >
            <h4 className="font-medium">{notification.title}</h4>
            <p className="text-sm">{notification.message}</p>
          </motion.div>
        ))}
      </AnimatePresence>
    </div>
  );
}
