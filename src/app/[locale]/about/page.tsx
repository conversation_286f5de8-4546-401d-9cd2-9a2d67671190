'use client';

import { ArrowLeft, Mail } from 'lucide-react';
import { useRouter, usePathname } from 'next/navigation';
import Link from 'next/link';

export default function AboutPage() {
  const router = useRouter();
  const pathname = usePathname();
  const locale = pathname.split('/')[1]; // 获取当前locale

  return (
    <div className="min-h-screen bg-background p-6">
      <div className="max-w-4xl mx-auto">
        {/* 导航 */}
        <nav aria-label="返回导航">
          <button 
            onClick={() => router.push(`/${locale}`)}
            className="mb-6 flex items-center gap-2 text-primary hover:underline"
            aria-label="返回首页"
          >
            <ArrowLeft size={16} />
            <span>返回首页</span>
          </button>
        </nav>
        
        {/* 页面标题 */}
        <header className="mb-10 text-center">
          <h1 className="text-3xl font-bold">关于我们</h1>
          <p className="text-muted-foreground mt-2">了解我们的使命、团队和价值观</p>
        </header>
        
        {/* 使命与介绍 */}
        <section className="mb-12">
          <div className="bg-card border border-border rounded-lg p-8">
            <h2 className="text-2xl font-semibold mb-4">我们的使命</h2>
            <p className="text-muted-foreground mb-6">
              我们致力于创建简单、高效且可靠的在线工具，帮助用户解决日常网络和开发问题。
              我们相信，通过提供易于使用的工具，可以让技术变得更加平民化，使更多人能够从中受益。
            </p>
            <p className="text-muted-foreground">
              无论您是资深开发者、网站管理员，还是刚刚接触互联网的新手，我们的工具都旨在为您提供价值，
              帮助您更有效地完成任务，提高工作效率。
            </p>
          </div>
        </section>
        
        {/* 团队介绍 */}
        <section className="mb-12">
          <h2 className="text-2xl font-semibold mb-6 text-center">我们的团队</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="bg-card border border-border rounded-lg p-6 text-center">
              <div className="w-24 h-24 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4">
                <svg xmlns="http://www.w3.org/2000/svg" width="40" height="40" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-primary">
                  <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path>
                  <circle cx="12" cy="7" r="4"></circle>
                </svg>
              </div>
              <h3 className="text-lg font-medium mb-2">张伟</h3>
              <p className="text-primary mb-1">创始人 & 首席执行官</p>
              <p className="text-sm text-muted-foreground mb-4">
                拥有10年互联网产品开发经验，致力于创造简单易用的工具，解决实际问题。
              </p>
            </div>
            
            <div className="bg-card border border-border rounded-lg p-6 text-center">
              <div className="w-24 h-24 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4">
                <svg xmlns="http://www.w3.org/2000/svg" width="40" height="40" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-primary">
                  <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path>
                  <circle cx="12" cy="7" r="4"></circle>
                </svg>
              </div>
              <h3 className="text-lg font-medium mb-2">李明</h3>
              <p className="text-primary mb-1">技术总监</p>
              <p className="text-sm text-muted-foreground mb-4">
                全栈开发者，专注于构建高性能、可靠的应用程序和API服务。
              </p>
            </div>
            
            <div className="bg-card border border-border rounded-lg p-6 text-center">
              <div className="w-24 h-24 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4">
                <svg xmlns="http://www.w3.org/2000/svg" width="40" height="40" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-primary">
                  <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path>
                  <circle cx="12" cy="7" r="4"></circle>
                </svg>
              </div>
              <h3 className="text-lg font-medium mb-2">王芳</h3>
              <p className="text-primary mb-1">产品设计师</p>
              <p className="text-sm text-muted-foreground mb-4">
                用户体验专家，致力于打造直观、美观且易用的产品界面。
              </p>
            </div>
          </div>
        </section>
        
        {/* 公司价值观 */}
        <section className="mb-12">
          <h2 className="text-2xl font-semibold mb-6 text-center">我们的价值观</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="bg-card border border-border rounded-lg p-6">
              <div className="bg-primary/10 w-12 h-12 rounded-full flex items-center justify-center mb-4">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-primary">
                  <circle cx="12" cy="12" r="10"></circle>
                  <line x1="12" y1="8" x2="12" y2="16"></line>
                  <line x1="8" y1="12" x2="16" y2="12"></line>
                </svg>
              </div>
              <h3 className="text-lg font-medium mb-2">简单实用</h3>
              <p className="text-muted-foreground">
                我们相信最好的工具应该是简单而实用的。我们专注于为用户提供直观的界面和高效的功能，
                避免不必要的复杂性，确保每个人都能轻松使用我们的产品。
              </p>
            </div>
            
            <div className="bg-card border border-border rounded-lg p-6">
              <div className="bg-primary/10 w-12 h-12 rounded-full flex items-center justify-center mb-4">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-primary">
                  <polyline points="20 6 9 17 4 12"></polyline>
                </svg>
              </div>
              <h3 className="text-lg font-medium mb-2">质量优先</h3>
              <p className="text-muted-foreground">
                我们坚信工具的质量和可靠性是最重要的。我们投入大量时间进行测试和优化，
                确保我们的产品能够提供准确的结果和稳定的性能。
              </p>
            </div>
            
            <div className="bg-card border border-border rounded-lg p-6">
              <div className="bg-primary/10 w-12 h-12 rounded-full flex items-center justify-center mb-4">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-primary">
                  <path d="M18 8A6 6 0 0 0 6 8c0 7-3 9-3 9h18s-3-2-3-9"></path>
                  <path d="M13.73 21a2 2 0 0 1-3.46 0"></path>
                </svg>
              </div>
              <h3 className="text-lg font-medium mb-2">用户至上</h3>
              <p className="text-muted-foreground">
                用户的需求和反馈是我们产品开发的核心。我们积极听取用户意见，不断改进我们的工具，
                以满足不断变化的需求和期望。
              </p>
            </div>
            
            <div className="bg-card border border-border rounded-lg p-6">
              <div className="bg-primary/10 w-12 h-12 rounded-full flex items-center justify-center mb-4">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-primary">
                  <path d="M22 12h-4l-3 9L9 3l-3 9H2"></path>
                </svg>
              </div>
              <h3 className="text-lg font-medium mb-2">持续创新</h3>
              <p className="text-muted-foreground">
                我们不断探索新技术和方法，以提升我们的产品。通过持续学习和创新，
                我们确保我们的工具能够跟上行业发展，为用户提供最佳解决方案。
              </p>
            </div>
          </div>
        </section>
        
        {/* 联系我们 */}
        <section className="text-center">
          <h2 className="text-2xl font-semibold mb-4">联系我们</h2>
          <p className="text-muted-foreground mb-6">
            如果您有任何问题、建议或合作意向，欢迎随时与我们联系
          </p>
          <Link
            href={`/${locale}/contact`}
            className="inline-flex items-center gap-2 px-6 py-3 bg-primary text-primary-foreground rounded-lg hover:bg-primary/90"
          >
            <Mail size={18} />
            <span>联系我们</span>
          </Link>
        </section>
      </div>
    </div>
  );
} 