
import { getLandingPage } from "@/app/actions";
import { unstable_setRequestLocale } from 'next-intl/server';
import {routing} from '@/i18n/routing';
import Home from "@/app/[locale]/home";
import Script from 'next/script';

type Locale = (typeof routing.locales)[number];

export default async function LandingPage({
  params,
}: {
  params: Promise<{ locale: string }>;
}) {
  // 使用 await 获取 locale
  const { locale } = await params;

  // 设置请求的 locale
  unstable_setRequestLocale(locale);

  // 获取页面数据
  const page = await getLandingPage(locale);

  // 定义结构化数据
  const structuredData = {
    "@context": "https://schema.org",
    "@type": "WebApplication",
    "name": locale === 'zh' ? "哔哩工具箱 (Bili Tool)" : "Bili Tool",
    "url": "https://bili-tool.com",
    "applicationCategory": "UtilitiesApplication",
    "operatingSystem": "Web",
    "offers": {
      "@type": "Offer",
      "price": "0",
      "priceCurrency": "USD"
    },
    "description": locale === 'zh' 
      ? "哔哩工具箱提供全面的在线工具集合，包括图像处理、文本编辑、数据转换等多种实用功能，助您提高工作效率。"
      : "Bili Tool offers a comprehensive collection of online tools, including image processing, text editing, data conversion, and more practical functions to improve your work efficiency.",
    "creator": {
      "@type": "Organization",
      "name": "Bili Tool",
      "url": "https://bili-tool.com"
    }
  };

  return (
    <>
      <Script
        id="structured-data"
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(structuredData) }}
      />
      <Home />
    </>
  );
}
