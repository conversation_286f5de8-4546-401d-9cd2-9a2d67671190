'use client';

import { ArrowLeft } from 'lucide-react';
import { useRouter, usePathname } from 'next/navigation';
import { useTranslations } from 'next-intl';

export default function PrivacyPage() {
  const router = useRouter();
  const pathname = usePathname();
  const locale = pathname.split('/')[1]; // 获取当前locale
  const t = useTranslations('privacy');

  return (
    <div className="min-h-screen bg-background p-6">
      <div className="max-w-4xl mx-auto">
        {/* 导航 */}
        <nav aria-label={t('backToHome')}>
          <button 
            onClick={() => router.push(`/${locale}`)}
            className="mb-6 flex items-center gap-2 text-primary hover:underline"
            aria-label={t('backToHome')}
          >
            <ArrowLeft size={16} />
            <span>{t('backToHome')}</span>
          </button>
        </nav>

        {/* 页面标题 */}
        <header className="mb-8">
          <h1 className="text-3xl font-bold">{t('title')}</h1>
          <p className="text-muted-foreground mt-2">
            {t('lastUpdated')}
          </p>
        </header>

        {/* 隐私政策内容 */}
        <div className="bg-card border border-border rounded-lg p-8">
          <div className="prose prose-headings:font-semibold prose-headings:text-foreground prose-p:text-muted-foreground max-w-none">
            <p>{t('content.welcome')}</p>

            <h2 className="text-xl mt-8 mb-4">{t('content.sections.collection.title')}</h2>
            <p>{t('content.sections.collection.intro')}</p>
            <ul className="list-disc list-inside mb-4">
              {t.raw('content.sections.collection.items').map((item: string, index: number) => (
                <li key={index}>{item}</li>
              ))}
            </ul>

            <h2 className="text-xl mt-8 mb-4">{t('content.sections.usage.title')}</h2>
            <p>{t('content.sections.usage.intro')}</p>
            <ul className="list-disc list-inside mb-4">
              {t.raw('content.sections.usage.items').map((item: string, index: number) => (
                <li key={index}>{item}</li>
              ))}
            </ul>

            <h2 className="text-xl mt-8 mb-4">{t('content.sections.sharing.title')}</h2>
            <p>{t('content.sections.sharing.intro')}</p>
            <ul className="list-disc list-inside mb-4">
              {t.raw('content.sections.sharing.items').map((item: string, index: number) => (
                <li key={index}>{item}</li>
              ))}
            </ul>

            <h2 className="text-xl mt-8 mb-4">{t('content.sections.security.title')}</h2>
            <p>{t('content.sections.security.content')}</p>

            <h2 className="text-xl mt-8 mb-4">{t('content.sections.cookies.title')}</h2>
            <p>{t('content.sections.cookies.content')}</p>

            <h2 className="text-xl mt-8 mb-4">{t('content.sections.rights.title')}</h2>
            <p>{t('content.sections.rights.intro')}</p>
            <ul className="list-disc list-inside mb-4">
              {t.raw('content.sections.rights.items').map((item: string, index: number) => (
                <li key={index}>{item}</li>
              ))}
            </ul>
            <p>{t('content.sections.rights.outro')}</p>

            <h2 className="text-xl mt-8 mb-4">{t('content.sections.children.title')}</h2>
            <p>{t('content.sections.children.content')}</p>

            <h2 className="text-xl mt-8 mb-4">{t('content.sections.thirdParty.title')}</h2>
            <p>{t('content.sections.thirdParty.content')}</p>

            <h2 className="text-xl mt-8 mb-4">{t('content.sections.updates.title')}</h2>
            <p>{t('content.sections.updates.content')}</p>

            <h2 className="text-xl mt-8 mb-4">{t('content.sections.contact.title')}</h2>
            <p>{t('content.sections.contact.intro')}</p>
            <p className="mb-4">
              {t('content.sections.contact.email')}<br />
              {t('content.sections.contact.address')}<br />
              {t('content.sections.contact.phone')}
            </p>
            <p>{t('content.sections.contact.outro')}</p>
          </div>
        </div>
      </div>
    </div>
  );
} 