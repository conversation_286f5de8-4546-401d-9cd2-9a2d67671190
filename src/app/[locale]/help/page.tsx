'use client';

import { useState } from 'react';
import { ArrowLeft, Search, Plus, Minus } from 'lucide-react';
import { useRouter, usePathname } from 'next/navigation';
import Link from 'next/link';

// 常见问题数据
const faqs = [
  {
    id: 1,
    question: '如何使用域名信息查询工具？',
    answer: '在域名信息查询工具页面，输入您想要查询的域名关键词（不含后缀），然后点击"查询"按钮。系统将检查多个常见顶级域名的可注册状态。绿色表示域名可能可注册，红色表示域名可能已被注册。您可以点击每个域名后的"Whois查询"链接查看更详细的注册信息。',
    category: '工具使用'
  },
  {
    id: 2,
    question: '如何使用IP地理位置查询工具？',
    answer: '在IP地理位置查询工具页面，输入有效的IP地址，然后点击"查询"按钮。系统将显示该IP地址的地理位置信息，包括国家、地区、城市等详细信息。',
    category: '工具使用'
  },
  {
    id: 3,
    question: 'DNS查询工具支持哪些记录类型？',
    answer: 'DNS查询工具支持多种记录类型，包括A记录、AAAA记录、CNAME记录、MX记录、TXT记录、NS记录等。您可以在工具页面选择需要查询的记录类型，输入域名后点击查询按钮获取结果。',
    category: '工具使用'
  },
  {
    id: 4,
    question: '网站是否免费使用？',
    answer: '是的，我们的基础工具功能完全免费使用，无需注册。我们可能会在未来推出一些高级功能，这些功能可能需要付费使用，但核心工具将始终保持免费。',
    category: '账户与付费'
  },
  {
    id: 5,
    question: '我需要注册账户才能使用工具吗？',
    answer: '大多数工具无需注册即可使用。但是，注册账户可以帮助您保存查询历史、自定义设置，并获得更好的使用体验。注册过程简单，只需要一个有效的电子邮件地址。',
    category: '账户与付费'
  },
  {
    id: 6,
    question: '如何举报网站问题或功能错误？',
    answer: '您可以通过"联系我们"页面提交问题报告，或发送邮件至**********************描述您遇到的问题。请尽可能详细地描述问题情况、复现步骤以及您使用的设备和浏览器环境，这将帮助我们更快地解决问题。',
    category: '技术支持'
  },
  {
    id: 7,
    question: '网站数据安全吗？我输入的信息会被保存吗？',
    answer: '我们非常重视用户数据安全。您使用工具输入的信息仅用于提供查询结果，不会被用于其他商业目的。我们不会长期存储敏感数据，也不会与第三方共享您的个人信息。详细信息请参阅我们的隐私政策。',
    category: '隐私安全'
  },
  {
    id: 8,
    question: '工具查询结果的准确性如何？',
    answer: '我们尽力提供准确的查询结果，但由于互联网数据的实时变化性，结果仅供参考。特别是域名查询、IP查询等工具，其结果可能受到网络状况、数据更新频率等因素影响。对于重要决策，建议核实多个来源的信息。',
    category: '工具使用'
  }
];

// 帮助分类
const categories = ['全部', '工具使用', '账户与付费', '技术支持', '隐私安全'];

export default function HelpPage() {
  const router = useRouter();
  const pathname = usePathname();
  const locale = pathname.split('/')[1]; // 获取当前locale
  const [activeCategory, setActiveCategory] = useState('全部');
  const [searchQuery, setSearchQuery] = useState('');
  const [expandedFaqId, setExpandedFaqId] = useState<number | null>(null);

  // 切换FAQ的展开/折叠状态
  const toggleFaq = (id: number) => {
    if (expandedFaqId === id) {
      setExpandedFaqId(null);
    } else {
      setExpandedFaqId(id);
    }
  };

  // 根据分类和搜索关键词筛选FAQ
  const filteredFaqs = faqs.filter(faq => {
    const matchesCategory = activeCategory === '全部' || faq.category === activeCategory;
    const matchesSearch = !searchQuery || 
      faq.question.toLowerCase().includes(searchQuery.toLowerCase()) || 
      faq.answer.toLowerCase().includes(searchQuery.toLowerCase());
    return matchesCategory && matchesSearch;
  });

  return (
    <div className="min-h-screen bg-background p-6">
      <div className="max-w-4xl mx-auto">
        {/* 导航 */}
        <nav aria-label="返回导航">
          <button 
            onClick={() => router.back()}
            className="mb-6 flex items-center gap-2 text-primary hover:underline"
            aria-label="返回首页"
          >
            <ArrowLeft size={16} />
            <span>返回首页</span>
          </button>
        </nav>

        {/* 页面标题 */}
        <header className="mb-8 text-center">
          <h1 className="text-3xl font-bold">帮助中心</h1>
          <p className="text-muted-foreground mt-2">
            寻找问题的答案，了解如何更好地使用我们的工具
          </p>
        </header>

        {/* 搜索框 */}
        <div className="mb-8">
          <div className="relative max-w-2xl mx-auto">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <Search size={18} className="text-muted-foreground" />
            </div>
            <input
              type="text"
              placeholder="搜索常见问题..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full pl-10 pr-4 py-3 rounded-lg bg-accent border border-border focus:outline-none focus:ring-2 focus:ring-primary"
            />
          </div>
        </div>

        {/* 分类导航 */}
        <div className="mb-8">
          <div className="bg-gray-50 rounded-full px-3 py-2">
            <div className="flex flex-wrap items-center justify-center gap-2">
              {categories.map((category) => (
                <button
                  key={category}
                  onClick={() => setActiveCategory(category)}
                  className={`rounded-full px-4 py-1 text-sm transition-all ${
                    activeCategory === category
                      ? "bg-primary text-primary-foreground"
                      : "hover:bg-secondary"
                  }`}
                >
                  {category}
                </button>
              ))}
            </div>
          </div>
        </div>

        {/* 常见问题列表 */}
        <div className="space-y-4">
          {filteredFaqs.map((faq) => (
            <div key={faq.id} className="bg-card border border-border rounded-lg">
              <button
                onClick={() => toggleFaq(faq.id)}
                className="w-full flex items-center justify-between px-6 py-4"
              >
                <h3 className="font-medium text-left">{faq.question}</h3>
                <span className="text-primary ml-2">
                  {expandedFaqId === faq.id ? <Minus size={16} /> : <Plus size={16} />}
                </span>
              </button>
              {expandedFaqId === faq.id && (
                <div className="px-6 pb-4">
                  <p className="text-muted-foreground">{faq.answer}</p>
                </div>
              )}
            </div>
          ))}
        </div>
        
        {/* 没有找到答案的提示 */}
        {filteredFaqs.length === 0 && (
          <div className="text-center py-12">
            <h3 className="text-xl font-medium">没有找到相关问题</h3>
            <p className="mt-2 text-muted-foreground mb-4">
              尝试使用不同的搜索词，或者直接联系我们
            </p>
            <Link href={`/${locale}/contact`} className="text-primary hover:underline">
              联系客服 →
            </Link>
          </div>
        )}

        {/* 联系支持 */}
        <div className="mt-12 text-center">
          <h2 className="text-xl font-semibold mb-4">没有找到您的问题？</h2>
          <p className="text-muted-foreground mb-6">
            如果您有任何其他问题或需要进一步帮助，请随时联系我们的客服团队
          </p>
          <Link 
            href={`/${locale}/contact`}
            className="px-6 py-2 bg-primary text-primary-foreground rounded-lg hover:bg-primary/90"
          >
            联系我们
          </Link>
        </div>
      </div>
    </div>
  );
} 