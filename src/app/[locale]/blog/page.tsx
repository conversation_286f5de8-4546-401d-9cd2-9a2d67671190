'use client';

import { useState } from 'react';
import { ArrowLeft, Search, Calendar, User, Clock, ChevronRight } from 'lucide-react';
import { useRouter, usePathname } from 'next/navigation';
import Link from 'next/link';
import { useTranslations } from 'next-intl';
import { blogPosts } from '@/data/blog/posts';
import { categories, BlogCategory } from '@/data/blog/types';

type Locale = 'zh' | 'en';

export default function BlogPage() {
  const router = useRouter();
  const pathname = usePathname();
  const locale = pathname.split('/')[1] as Locale; // 获取当前locale
  const t = useTranslations('blog');
  const [searchQuery, setSearchQuery] = useState('');
  const [activeCategory, setActiveCategory] = useState('all');

  // 过滤博客文章
  const filteredPosts = blogPosts.filter(post => {
    const matchesSearch = post.title[locale].toLowerCase().includes(searchQuery.toLowerCase()) || 
                          post.excerpt[locale].toLowerCase().includes(searchQuery.toLowerCase());
    const matchesCategory = activeCategory === 'all' || post.category === activeCategory;
    return matchesSearch && matchesCategory;
  });

  // 找出特色文章
  const featuredPost = blogPosts.find(post => post.featured);

  // 格式化日期
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return `${date.getFullYear()}年${date.getMonth() + 1}月${date.getDate()}日`;
  };

  return (
    <div className="min-h-screen bg-background p-6">
      <div className="max-w-6xl mx-auto">
        {/* 导航 */}
        <nav aria-label={t('backToHome')}>
          <button 
            onClick={() => router.push(`/${locale}`)}
            className="mb-6 flex items-center gap-2 text-primary hover:underline"
            aria-label={t('backToHome')}
          >
            <ArrowLeft size={16} />
            <span>{t('backToHome')}</span>
          </button>
        </nav>
        
        {/* 页面标题 */}
        <header className="mb-10 text-center">
          <h1 className="text-3xl font-bold">{t('title')}</h1>
          <p className="text-muted-foreground mt-2">
            {t('subtitle')}
          </p>
        </header>
        
        {/* 搜索和分类 */}
        <div className="mb-10 space-y-6">
          {/* 搜索框 */}
          <div className="relative max-w-2xl mx-auto">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <Search size={18} className="text-muted-foreground" />
            </div>
            <input
              type="text"
              placeholder={t('search.placeholder')}
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full pl-10 pr-4 py-3 rounded-lg bg-accent border border-border focus:outline-none focus:ring-2 focus:ring-primary"
            />
          </div>
          
          {/* 分类导航 */}
          <div className="bg-gray-50 rounded-full px-3 py-2">
            <div className="flex flex-wrap items-center justify-center gap-2">
              {categories.map((category: BlogCategory) => (
                <button
                  key={category.id}
                  onClick={() => setActiveCategory(category.id)}
                  className={`rounded-full px-4 py-1 text-sm transition-all ${
                    activeCategory === category.id
                      ? "bg-primary text-primary-foreground"
                      : "hover:bg-secondary"
                  }`}
                >
                  {category.name[locale]}
                </button>
              ))}
            </div>
          </div>
        </div>
        
        {/* 特色文章 */}
        {featuredPost && activeCategory === 'all' && !searchQuery && (
          <div className="mb-12 rounded-xl overflow-hidden border border-border">
            <div className="grid md:grid-cols-2">
              <div 
                className="h-64 md:h-auto bg-cover bg-center" 
                style={{ backgroundImage: `url(${featuredPost.imageUrl})` }}
              ></div>
              <div className="p-6 md:p-8 flex flex-col justify-center">
                <div className="mb-2">
                  <span className="px-2.5 py-0.5 text-xs font-medium rounded-full bg-primary/10 text-primary">
                    {t('featured.label')}
                  </span>
                  <span className="ml-2 px-2.5 py-0.5 text-xs font-medium rounded-full bg-gray-100">
                    {categories.find((c: BlogCategory) => c.id === featuredPost.category)?.name[locale]}
                  </span>
                </div>
                <h2 className="text-2xl font-bold mb-3">{featuredPost.title[locale]}</h2>
                <p className="text-muted-foreground mb-4">{featuredPost.excerpt[locale]}</p>
                <div className="flex items-center text-sm text-muted-foreground space-x-4 mb-4">
                  <div className="flex items-center">
                    <Calendar size={14} className="mr-1" />
                    <span>{formatDate(featuredPost.date)}</span>
                  </div>
                  <div className="flex items-center">
                    <User size={14} className="mr-1" />
                    <span>{featuredPost.author[locale]}</span>
                  </div>
                  <div className="flex items-center">
                    <Clock size={14} className="mr-1" />
                    <span>{featuredPost.readTime[locale]}</span>
                  </div>
                </div>
                <Link 
                  href={`/${locale}/blog/${featuredPost.id}`}
                  className="self-start flex items-center text-primary hover:underline"
                >
                  {t('post.readMore')} <ChevronRight size={16} className="ml-1" />
                </Link>
              </div>
            </div>
          </div>
        )}
        
        {/* 文章列表 */}
        {filteredPosts.length > 0 ? (
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
            {filteredPosts.map((post) => (
              <article 
                key={post.id} 
                className="border border-border rounded-xl overflow-hidden h-full flex flex-col group"
              >
                <div 
                  className="h-48 bg-cover bg-center transition-transform group-hover:scale-105"
                  style={{ backgroundImage: `url(${post.imageUrl})` }}
                ></div>
                <div className="p-5 flex-grow flex flex-col">
                  <div className="mb-2">
                    <span className="px-2.5 py-0.5 text-xs font-medium rounded-full bg-gray-100">
                      {categories.find((c: BlogCategory) => c.id === post.category)?.name[locale]}
                    </span>
                  </div>
                  <h2 className="text-xl font-semibold mb-2 line-clamp-2">{post.title[locale]}</h2>
                  <p className="text-muted-foreground text-sm mb-4 line-clamp-3 flex-grow">
                    {post.excerpt[locale]}
                  </p>
                  <div className="mt-auto">
                    <div className="flex items-center text-xs text-muted-foreground space-x-3 mb-3">
                      <div className="flex items-center">
                        <Calendar size={12} className="mr-1" />
                        <span>{formatDate(post.date)}</span>
                      </div>
                      <div className="flex items-center">
                        <Clock size={12} className="mr-1" />
                        <span>{post.readTime[locale]}</span>
                      </div>
                    </div>
                    <Link 
                      href={`/${locale}/blog/${post.id}`}
                      className="inline-flex items-center text-primary text-sm hover:underline"
                    >
                      {t('post.readMore')} <ChevronRight size={14} className="ml-1" />
                    </Link>
                  </div>
                </div>
              </article>
            ))}
          </div>
        ) : (
          <div className="text-center py-12">
            <h3 className="text-xl font-medium">{t('post.noResults.title')}</h3>
            <p className="text-muted-foreground mt-2 mb-4">
              {t('post.noResults.description')}
            </p>
            <button
              onClick={() => {
                setSearchQuery('');
                setActiveCategory('all');
              }}
              className="text-primary hover:underline"
            >
              {t('post.noResults.action')}
            </button>
          </div>
        )}
      </div>
    </div>
  );
} 