'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { ArrowLeft, Search, ExternalLink, Check, X, RefreshCw } from 'lucide-react';
import Script from 'next/script';

// 常见顶级域名列表
const commonTLDs = [
  '.com', '.net', '.org', '.io', '.co', '.app', 
  '.info', '.biz', '.me', '.tech', '.site', '.online', 
  '.store', '.club', '.xyz', '.dev', '.ai'
];

interface DomainCheckResult {
  domain: string;
  available: boolean;
  tld: string;
  error?: boolean;
  errorMessage?: string;
  source?: 'WHOIS' | 'DNS'; // 添加数据来源字段
}

export default function DomainInfoPage() {
  const [keyword, setKeyword] = useState('');
  const [loading, setLoading] = useState(false);
  const [results, setResults] = useState<DomainCheckResult[]>([]);
  const [error, setError] = useState<string | null>(null);
  const router = useRouter();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!keyword) {
      setError('请输入关键词');
      return;
    }
    
    // 简单的关键词验证
    const keywordRegex = /^[a-zA-Z0-9][a-zA-Z0-9-]{0,50}$/;
    if (!keywordRegex.test(keyword)) {
      setError('关键词只能包含字母、数字和连字符');
      return;
    }
    
    setLoading(true);
    setError(null);
    setResults([]);
    
    try {
      // 为每个顶级域名创建检查请求
      const checkResults = await Promise.all(
        commonTLDs.map(async (tld) => {
          const domain = `${keyword}${tld}`;
          try {
            const response = await fetch('/api/domain-info/check', {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
              },
              body: JSON.stringify({ domain }),
            });
            
            if (!response.ok) {
              const errorData = await response.json();
              throw new Error(errorData.error || '查询失败');
            }
            
            const data = await response.json();
            
            // 如果API返回error字段，表示查询出错（如超时）
            if (data.error) {
              return {
                domain,
                available: false, // 保留available字段，但前端会根据error字段判断显示
                tld,
                error: true,
                errorMessage: data.errorMessage || '查询超时'
              };
            }
            
            return {
              domain,
              available: data.available,
              tld,
              error: false,
              source: data.source || 'WHOIS'
            };
          } catch (error) {
            console.error(`查询域名 ${domain} 失败:`, error);
            // 单个域名检查失败，不中断整体流程
            return {
              domain,
              available: false,
              tld,
              error: true
            };
          }
        })
      );
      
      setResults(checkResults);
    } catch (err) {
      console.error('查询过程中出错:', err);
      setError(err instanceof Error ? err.message : '查询失败，请稍后重试');
    } finally {
      setLoading(false);
    }
  };

  const refreshQuery = () => {
    if (keyword) {
      handleSubmit({ preventDefault: () => {} } as React.FormEvent);
    }
  };
  
  // 检查单个域名
  const checkSingleDomain = async (domain: string, tld: string) => {
    try {
      const response = await fetch('/api/domain-info/check', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ domain: `${domain}${tld}` }),
      });
      
      const data = await response.json();
      
      // 更新结果列表中的对应项
      setResults(prev => 
        prev.map(item => {
          if (item.tld === tld) {
            if (data.error) {
              // 如果API返回error字段，保持error状态为true
              return {
                ...item,
                available: false,
                error: true,
                errorMessage: data.errorMessage || '查询超时'
              };
            } else {
              // 查询成功的情况
              return {
                ...item,
                available: data.available,
                source: data.source || 'WHOIS',
                error: false
              };
            }
          }
          return item;
        })
      );
    } catch (error) {
      console.error("检查单个域名出错:", error);
    }
  };

  return (
    <div className="min-h-screen bg-background p-6">
      <Script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify({
            "@context": "https://schema.org",
            "@type": "WebApplication",
            "name": "域名信息查询工具",
            "url": "https://wenhaofree.com/tools/domain-info",
            "description": "免费在线查询域名注册状态，快速检测多种顶级域名(.com .net .org等)是否可注册，支持WHOIS查询和DNS验证。",
            "applicationCategory": "WebApplication",
            "operatingSystem": "All",
            "offers": {
              "@type": "Offer",
              "price": "0",
              "priceCurrency": "CNY"
            },
            "author": {
              "@type": "Organization",
              "name": "在线工具箱",
              "url": "https://wenhaofree.com"
            },
            "keywords": "域名查询,域名检测,域名注册状态,WHOIS查询,DNS查询,域名可用性",
            "thumbnailUrl": "https://wenhaofree.com/images/domain-info-thumbnail.jpg",
            "screenshot": "https://wenhaofree.com/images/domain-info-screenshot.jpg",
            "potentialAction": {
              "@type": "SearchAction",
              "target": {
                "@type": "EntryPoint",
                "urlTemplate": "https://wenhaofree.com/tools/domain-info?keyword={search_term}"
              },
              "query-input": "required name=search_term"
            }
          })
        }}
      />
      <div className="max-w-5xl mx-auto">
        {/* 导航 */}
        <nav aria-label="返回导航">
          <button 
            onClick={() => router.back()}
            className="mb-6 flex items-center gap-2 text-primary hover:underline"
            aria-label="返回工具列表"
          >
            <ArrowLeft size={16} />
            <span>返回工具列表</span>
          </button>
        </nav>

        {/* 工具标题 */}
        <header className="mb-8">
          <h1 className="text-3xl font-bold">域名信息查询工具 | 域名注册状态检测</h1>
          <p className="text-muted-foreground mt-2">
            免费在线查询域名是否可以注册，快速检测多种顶级域名(如.com/.net/.org)的可用状态，支持WHOIS查询和DNS验证
          </p>
        </header>

        {/* 查询表单 */}
        <section aria-labelledby="search-form-heading" className="bg-card border border-border rounded-lg p-6 mb-6">
          <h2 id="search-form-heading" className="sr-only">域名查询表单</h2>
          <form onSubmit={handleSubmit} className="flex flex-col sm:flex-row gap-4" role="search">
            <div className="flex-1">
              <label htmlFor="keyword" className="block text-sm font-medium mb-2">
                域名关键词
              </label>
              <div className="relative">
                <input
                  id="keyword"
                  type="text"
                  placeholder="输入域名关键词（不含后缀）"
                  value={keyword}
                  onChange={(e) => setKeyword(e.target.value.toLowerCase())}
                  className="w-full h-10 pl-4 pr-10 rounded-lg bg-accent border border-border focus:outline-none focus:ring-2 focus:ring-primary"
                  aria-required="true"
                  aria-invalid={error ? "true" : "false"}
                  aria-describedby={error ? "keyword-error" : undefined}
                />
              </div>
              {error && <p id="keyword-error" className="text-red-500 text-sm mt-1" role="alert">{error}</p>}
            </div>
            <div className="flex items-end">
              <button
                type="submit"
                disabled={loading}
                className="h-10 px-6 rounded-lg bg-primary text-primary-foreground hover:bg-primary/90 flex items-center gap-2 disabled:opacity-70"
                aria-label={loading ? "查询中" : "查询域名"}
              >
                {loading ? (
                  <>
                    <span className="animate-spin h-4 w-4 border-2 border-white border-t-transparent rounded-full" aria-hidden="true"></span>
                    <span>查询中...</span>
                  </>
                ) : (
                  <>
                    <Search size={16} aria-hidden="true" />
                    <span>查询</span>
                  </>
                )}
              </button>
            </div>
          </form>
        </section>

        {/* 结果显示 */}
        {results.length > 0 && (
          <section aria-labelledby="results-heading" className="bg-card border border-border rounded-lg animate-fadeIn">
            <header className="p-4 border-b border-border flex items-center justify-between">
              <h2 id="results-heading" className="font-medium">域名可注册状态</h2>
              <div className="flex items-center gap-2">
                <button 
                  onClick={refreshQuery}
                  className="p-2 rounded hover:bg-accent"
                  title="刷新查询结果"
                  aria-label="刷新查询结果"
                >
                  <RefreshCw size={16} aria-hidden="true" />
                </button>
              </div>
            </header>
            <div className="p-4">
              <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4" role="list">
                {results.map((result, index) => (
                  <div 
                    key={result.domain} 
                    className={`p-4 rounded-lg border transition-all duration-300 animate-fadeIn flex items-center justify-between hover:shadow-md`}
                    style={{ 
                      animationDelay: `${index * 50}ms`,
                      borderColor: result.error 
                        ? 'rgb(59, 130, 246)' // 蓝色边框表示错误状态
                        : result.available 
                          ? 'rgb(34, 197, 94)' 
                          : 'rgb(239, 68, 68)',
                      backgroundColor: result.error
                        ? 'rgba(219, 234, 254, 0.8)' // 蓝色背景表示错误状态
                        : result.available 
                          ? 'rgba(240, 253, 244, 0.8)' 
                          : 'rgba(254, 242, 242, 0.8)'
                    }}
                    role="listitem"
                    aria-label={`域名 ${result.domain} ${
                      result.error 
                        ? '查询失败' 
                        : (result.available ? '可注册' : '已被注册')
                    }`}
                  >
                    <div className="flex items-center gap-3">
                      <span className={`flex items-center justify-center w-6 h-6 rounded-full ${
                        result.error
                          ? 'bg-blue-100 text-blue-600 dark:bg-blue-800/50 dark:text-blue-400'
                          : result.available 
                            ? 'bg-green-100 text-green-600 dark:bg-green-800/50 dark:text-green-400' 
                            : 'bg-red-100 text-red-600 dark:bg-red-800/50 dark:text-red-400'
                      }`}
                      aria-hidden="true"
                      >
                        {result.error ? <RefreshCw size={14} /> : result.available ? <Check size={14} /> : <X size={14} />}
                      </span>
                      <div>
                        <span className="font-medium">{result.domain}</span>
                        <p className={`text-xs mt-0.5 ${
                          result.error
                            ? 'text-blue-600 dark:text-blue-400'
                            : result.available 
                              ? 'text-green-600 dark:text-green-400' 
                              : 'text-red-600 dark:text-red-400'
                        }`}>
                          {result.error 
                            ? (result.errorMessage?.includes('Timeout') ? '查询超时' : '查询失败') 
                            : (result.available ? '可注册' : '已被注册')}
                          {!result.error && result.source === 'DNS' && 
                            <span className="ml-1 px-1 py-0.5 bg-blue-100 dark:bg-blue-800 text-blue-600 dark:text-blue-300 rounded text-[10px]">
                              DNS
                            </span>
                          }
                        </p>
                      </div>
                    </div>
                    {result.error ? (
                      <button
                        onClick={() => checkSingleDomain(keyword, result.tld)}
                        className="text-xs text-blue-600 dark:text-blue-400 hover:underline flex items-center gap-1"
                        aria-label={`重试查询 ${result.domain}`}
                      >
                        <RefreshCw size={12} aria-hidden="true" />
                        <span>重试</span>
                      </button>
                    ) : (
                      <a
                        href={`https://whois.com/whois/${result.domain}`}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-xs text-blue-600 dark:text-blue-400 hover:underline flex items-center gap-1 px-2 py-1 bg-white dark:bg-gray-800 rounded-md shadow-sm"
                        aria-label={`查看 ${result.domain} 的Whois信息`}
                      >
                        <span>Whois查询</span>
                        <ExternalLink size={10} aria-hidden="true" />
                      </a>
                    )}
                  </div>
                ))}
              </div>
              
              <div className="mt-6 p-4 bg-accent/50 rounded-lg">
                <p className="text-sm text-muted-foreground">
                  <strong>注意：</strong> 域名可注册状态仅作参考，最终注册结果以域名注册商为准。绿色表示可能可以注册，红色表示可能已被注册。
                </p>
                <div className="mt-3 grid grid-cols-1 sm:grid-cols-2 gap-3" aria-label="查询统计">
                  <div className="flex items-center gap-2">
                    <div className="w-3 h-3 rounded-full bg-green-500" aria-hidden="true"></div>
                    <span className="text-xs text-muted-foreground">可注册: {results.filter(r => r.available).length}个</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="w-3 h-3 rounded-full bg-red-500" aria-hidden="true"></div>
                    <span className="text-xs text-muted-foreground">已注册: {results.filter(r => !r.available && !r.error).length}个</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="w-3 h-3 rounded-full bg-blue-500" aria-hidden="true"></div>
                    <span className="text-xs text-muted-foreground">查询失败: {results.filter(r => r.error).length}个</span>
                  </div>
                </div>
              </div>
            </div>
          </section>
        )}

        {/* 使用说明 */}
        <section aria-labelledby="instructions-heading" className="mt-8">
          <h2 id="instructions-heading" className="text-xl font-semibold mb-4">使用说明</h2>
          <div className="bg-card border border-border rounded-lg p-6">
            <ol className="list-decimal list-inside space-y-2">
              <li>在输入框中输入您想要查询的域名关键词（不含后缀，如：mywebsite）</li>
              <li>点击"查询"按钮，系统将实时检查多个常见顶级域名的可注册状态</li>
              <li>绿色表示域名可能可以注册，红色表示域名可能已被注册，蓝色表示查询失败</li>
              <li>点击每个域名后的"Whois查询"链接，可以查看更详细的注册信息</li>
              <li>如果某个域名查询失败或超时，可以点击"重试"按钮单独重新查询该域名</li>
            </ol>
            <div className="mt-4 p-4 bg-accent/50 rounded-lg">
              <p className="text-sm text-muted-foreground">
                <strong>提示：</strong> 此工具优先使用WHOIS查询检查域名可用性，当WHOIS查询失败时会尝试通过DNS解析判断域名状态(显示为"DNS"标记)。所有结果仅供参考，即使显示为可用，实际注册时可能会有其他限制条件。建议联系域名注册商确认最终注册资格。
              </p>
            </div>
          </div>
        </section>
      </div>
    </div>
  );
} 