import { Metadata } from 'next';

// 动态生成不同语言的元数据
export function generateMetadata({ params }: { params: { locale: string } }): Metadata {
  // 根据locale切换元数据
  const metadata: Record<string, Metadata> = {
    'zh': {
      title: '域名信息查询 | 在线域名注册状态检测工具',
      description: '免费在线查询域名注册状态，快速检测多种顶级域名(.com .net .org等)是否可注册，支持WHOIS查询和DNS验证，帮助您找到理想域名。',
      keywords: '域名查询,域名检测,域名注册状态,WHOIS查询,DNS查询,域名可用性,域名搜索,顶级域名,域名工具,注册域名',
      alternates: {
        canonical: '/zh/tools/domain-info',
        languages: {
          'en': '/en/tools/domain-info',
          'zh': '/zh/tools/domain-info'
        }
      },
    },
    'en': {
      title: 'Domain Info | Online Domain Registration Status Checker',
      description: 'Free online domain registration status checker. Quickly test if common top-level domains (.com .net .org etc.) are available for registration with WHOIS and DNS verification.',
      keywords: 'domain lookup,domain checker,domain registration status,WHOIS lookup,DNS query,domain availability,domain search,top-level domains,domain tools,register domain',
      alternates: {
        canonical: '/en/tools/domain-info',
        languages: {
          'en': '/en/tools/domain-info',
          'zh': '/zh/tools/domain-info'
        }
      },
    }
  };

  // 使用参数中的locale获取对应元数据，如果没有则使用默认值
  const localeMetadata = metadata[params.locale] || metadata['zh'];
  
  // 构建通用元数据
  return {
    ...localeMetadata,
    openGraph: {
      title: localeMetadata.title as string,
      description: localeMetadata.description as string,
      url: `/tools/domain-info`,
      type: 'website',
      siteName: params.locale === 'en' ? 'Online Toolbox' : '在线工具箱',
      images: [
        {
          url: '/images/domain-info-og.jpg',
          width: 1200,
          height: 630,
          alt: params.locale === 'en' ? 'Domain Information Tool' : '域名信息查询工具',
        }
      ],
    },
    twitter: {
      card: 'summary_large_image',
      title: localeMetadata.title as string,
      description: localeMetadata.description as string,
      images: ['/images/domain-info-og.jpg'],
    },
    robots: {
      index: true,
      follow: true,
      googleBot: {
        index: true,
        follow: true,
      },
    },
    viewport: {
      width: 'device-width',
      initialScale: 1,
    },
    verification: {
      google: 'verification_token', // 替换为实际的验证令牌
    },
  };
} 