'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { ArrowLeft, Search, Download, Copy, RefreshCw, ArrowRightLeft, AlertTriangle } from 'lucide-react';
import * as yaml from 'js-yaml';

export default function YamlToJsonPage() {
  const [yamlInput, setYamlInput] = useState('');
  const [jsonOutput, setJsonOutput] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const router = useRouter();

  // 转换处理函数
  const handleConvert = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!yamlInput.trim()) {
      setError('请输入YAML数据');
      return;
    }
    
    setLoading(true);
    setError(null);
    
    try {
      // 解析YAML
      const jsonObj = yaml.load(yamlInput);
      
      // 转换为格式化的JSON
      const jsonStr = JSON.stringify(jsonObj, null, 2);
      
      setJsonOutput(jsonStr);
    } catch (err) {
      setError(err instanceof Error ? `YAML解析错误: ${err.message}` : '无效的YAML格式');
    } finally {
      setLoading(false);
    }
  };

  // 示例YAML数据
  const loadExample = () => {
    const exampleYaml = `# 人员信息
person:
  name: 张三
  age: 28
  email: <EMAIL>
  isActive: true
  address:
    city: 北京
    street: 朝阳区
    postcode: '100000'
  hobbies:
    - 读书
    - 旅行
    - 编程
  education:
    - degree: 学士
      major: 计算机科学
      year: 2018
    - degree: 硕士
      major: 人工智能
      year: 2020

# 公司信息
company:
  name: 科技有限公司
  employees: 250
  departments:
    - 研发
    - 市场
    - 销售
    - 人力资源

# 应用设置
settings:
  theme: dark
  notifications:
    email: true
    sms: false
    push: true`;
    
    setYamlInput(exampleYaml);
    setError(null);
  };

  // 复制到剪贴板
  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
  };

  // 下载JSON结果
  const downloadJson = () => {
    if (jsonOutput) {
      const blob = new Blob([jsonOutput], { type: 'application/json' });
      const url = URL.createObjectURL(blob);
      
      const a = document.createElement('a');
      a.href = url;
      a.download = 'converted.json';
      document.body.appendChild(a);
      a.click();
      
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
    }
  };

  // 清空输入
  const clearInput = () => {
    setYamlInput('');
    setJsonOutput('');
    setError(null);
  };

  return (
    <div className="min-h-screen bg-background p-6">
      <div className="max-w-6xl mx-auto">
        {/* 返回按钮 */}
        <button 
          onClick={() => router.back()}
          className="mb-6 flex items-center gap-2 text-primary hover:underline"
        >
          <ArrowLeft size={16} />
          <span>返回工具列表</span>
        </button>

        {/* 工具标题 */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold">YAML转JSON</h1>
          <p className="text-muted-foreground mt-2">
            将YAML数据格式转换为JSON格式，保持数据结构不变
          </p>
        </div>

        {/* 转换区域 */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
          {/* YAML输入区 */}
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <h2 className="font-medium">YAML输入</h2>
              <div className="flex items-center gap-2">
                <button 
                  onClick={clearInput}
                  className="text-xs px-2 py-1 rounded bg-accent hover:bg-accent/80 text-accent-foreground"
                >
                  清空
                </button>
                <button 
                  onClick={loadExample}
                  className="text-xs px-2 py-1 rounded bg-accent hover:bg-accent/80 text-accent-foreground"
                >
                  加载示例
                </button>
                <button 
                  onClick={() => copyToClipboard(yamlInput)}
                  className="p-1 rounded hover:bg-accent"
                  title="复制"
                >
                  <Copy size={14} />
                </button>
              </div>
            </div>
            <textarea
              value={yamlInput}
              onChange={(e) => setYamlInput(e.target.value)}
              placeholder="在此粘贴YAML数据..."
              className="w-full h-[500px] p-4 font-mono text-sm rounded-lg bg-accent border border-border focus:outline-none focus:ring-2 focus:ring-primary resize-none"
            ></textarea>
            {error && (
              <div className="flex items-center gap-2 text-red-500 text-sm">
                <AlertTriangle size={14} />
                <span>{error}</span>
              </div>
            )}
          </div>

          {/* JSON输出区 */}
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <h2 className="font-medium">JSON输出</h2>
              <div className="flex items-center gap-2">
                {jsonOutput && (
                  <>
                    <button 
                      onClick={() => copyToClipboard(jsonOutput)}
                      className="text-xs px-2 py-1 rounded bg-primary hover:bg-primary/90 text-primary-foreground flex items-center gap-1"
                    >
                      <Copy size={14} />
                      <span>复制</span>
                    </button>
                    <button 
                      onClick={downloadJson}
                      className="text-xs px-2 py-1 rounded bg-primary hover:bg-primary/90 text-primary-foreground flex items-center gap-1"
                    >
                      <Download size={14} />
                      <span>下载</span>
                    </button>
                  </>
                )}
              </div>
            </div>
            <div className="relative h-[500px]">
              <textarea
                value={jsonOutput}
                readOnly
                placeholder="JSON输出将显示在这里..."
                className="w-full h-full p-4 font-mono text-sm rounded-lg bg-accent border border-border focus:outline-none resize-none"
              ></textarea>
              {!jsonOutput && (
                <div className="absolute inset-0 flex items-center justify-center">
                  <div className="text-center text-muted-foreground p-4">
                    <ArrowRightLeft size={28} className="mx-auto mb-2 opacity-40" />
                    <p>转换后的JSON将显示在这里</p>
                    <p className="text-xs mt-1">点击下方按钮开始转换</p>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* 转换按钮 */}
        <div className="flex justify-center mb-8">
          <button
            onClick={handleConvert}
            disabled={loading || !yamlInput.trim()}
            className="h-12 px-8 rounded-lg bg-primary text-primary-foreground hover:bg-primary/90 flex items-center gap-3 disabled:opacity-70 disabled:cursor-not-allowed"
          >
            {loading ? (
              <>
                <span className="animate-spin h-5 w-5 border-2 border-white border-t-transparent rounded-full"></span>
                <span>转换中...</span>
              </>
            ) : (
              <>
                <ArrowRightLeft size={18} />
                <span>转换 YAML 到 JSON</span>
              </>
            )}
          </button>
        </div>

        {/* 信息说明 */}
        <div className="bg-card border border-border rounded-lg p-6">
          <h3 className="font-medium mb-4">关于YAML和JSON</h3>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h4 className="font-medium mb-2">YAML (YAML Ain't Markup Language)</h4>
              <ul className="list-disc list-inside mb-4 text-muted-foreground text-sm space-y-1">
                <li>人类友好的数据序列化格式</li>
                <li>使用缩进表示层次结构，更易读</li>
                <li>常用于配置文件和文档</li>
                <li>支持与JSON相同的数据类型，还额外支持更复杂的数据类型</li>
                <li>通常不需要引号或大括号等语法符号</li>
                <li>支持注释、锚点引用和多行字符串</li>
              </ul>
            </div>
            
            <div>
              <h4 className="font-medium mb-2">JSON (JavaScript Object Notation)</h4>
              <ul className="list-disc list-inside mb-4 text-muted-foreground text-sm space-y-1">
                <li>基于JavaScript的轻量级数据交换格式</li>
                <li>使用键值对结构，格式紧凑</li>
                <li>常用于Web API和配置文件</li>
                <li>支持字符串、数字、布尔值、对象、数组、null</li>
                <li>必须使用双引号表示键和字符串值</li>
              </ul>
            </div>
          </div>
          
          <div className="mt-4 text-sm text-muted-foreground">
            <p>转换提示：</p>
            <ul className="list-disc list-inside mt-2 space-y-1">
              <li>确保您的YAML格式有效，注意缩进和语法</li>
              <li>YAML中的注释在转换到JSON后会丢失</li>
              <li>YAML中的锚点和引用在JSON中会被展开为完整对象</li>
              <li>YAML支持多种数据类型，但JSON仅支持有限的数据类型</li>
              <li>数字、布尔值和null在转换过程中会保持其类型</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
}
