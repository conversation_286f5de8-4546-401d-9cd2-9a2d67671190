'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { ArrowLeft, Search, Download, Copy, RefreshCw, Key, Shield, Clock } from 'lucide-react';

interface JwtPayload {
  [key: string]: any;
}

interface DecodedJwt {
  header: {
    alg: string;
    typ: string;
    [key: string]: any;
  };
  payload: JwtPayload;
  signature: string;
  isExpired: boolean;
  expirationTime?: string;
}

export default function JwtDecoderPage() {
  const [jwt, setJwt] = useState('');
  const [loading, setLoading] = useState(false);
  const [result, setResult] = useState<DecodedJwt | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState<'header' | 'payload'>('payload');
  const router = useRouter();

  const decodeJwt = (token: string): DecodedJwt | null => {
    try {
      // Split the JWT into its three parts
      const parts = token.split('.');
      if (parts.length !== 3) {
        throw new Error('JWT格式无效，必须包含三个部分（头部.负载.签名）');
      }

      // Decode the header and payload
      const header = JSON.parse(atob(parts[0]));
      const payload = JSON.parse(atob(parts[1]));
      const signature = parts[2];

      // Check if token is expired
      const now = Math.floor(Date.now() / 1000);
      const isExpired = payload.exp ? now > payload.exp : false;
      
      // Format expiration time if it exists
      let expirationTime;
      if (payload.exp) {
        const expDate = new Date(payload.exp * 1000);
        expirationTime = expDate.toLocaleString();
      }

      return {
        header,
        payload,
        signature,
        isExpired,
        expirationTime
      };
    } catch (err) {
      if (err instanceof Error) {
        throw new Error(`解析JWT失败: ${err.message}`);
      }
      throw new Error('解析JWT时发生未知错误');
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!jwt) {
      setError('请输入JWT令牌');
      return;
    }
    
    setLoading(true);
    setError(null);
    
    try {
      const decoded = decodeJwt(jwt);
      if (decoded) {
        setResult(decoded);
      } else {
        throw new Error('无法解码JWT');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : '解析失败，请确认输入了有效的JWT令牌');
    } finally {
      setLoading(false);
    }
  };

  const copyToClipboard = (data: any) => {
    navigator.clipboard.writeText(JSON.stringify(data, null, 2));
  };

  const downloadResult = () => {
    if (result) {
      const jsonString = JSON.stringify(result, null, 2);
      const blob = new Blob([jsonString], { type: 'application/json' });
      const url = URL.createObjectURL(blob);
      
      const a = document.createElement('a');
      a.href = url;
      a.download = 'jwt-decoded.json';
      document.body.appendChild(a);
      a.click();
      
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
    }
  };

  const refreshDecoder = () => {
    if (jwt) {
      handleSubmit({ preventDefault: () => {} } as React.FormEvent);
    }
  };

  const formatTime = (timestamp: number): string => {
    return new Date(timestamp * 1000).toLocaleString();
  };

  return (
    <div className="min-h-screen bg-background p-6">
      <div className="max-w-5xl mx-auto">
        {/* 返回按钮 */}
        <button 
          onClick={() => router.back()}
          className="mb-6 flex items-center gap-2 text-primary hover:underline"
        >
          <ArrowLeft size={16} />
          <span>返回工具列表</span>
        </button>

        {/* 工具标题 */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold">JWT解析</h1>
          <p className="text-muted-foreground mt-2">
            解析JSON Web Token (JWT)，查看令牌的头部信息和有效负载，验证令牌的有效性
          </p>
        </div>

        {/* 查询表单 */}
        <div className="bg-card border border-border rounded-lg p-6 mb-6">
          <form onSubmit={handleSubmit} className="flex flex-col gap-4">
            <div className="flex-1">
              <label htmlFor="jwt" className="block text-sm font-medium mb-2">
                JWT令牌
              </label>
              <div className="relative">
                <textarea
                  id="jwt"
                  placeholder="输入需要解析的JWT令牌，如: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
                  value={jwt}
                  onChange={(e) => setJwt(e.target.value)}
                  className="w-full h-32 p-4 rounded-lg bg-accent border border-border focus:outline-none focus:ring-2 focus:ring-primary resize-none"
                />
              </div>
              {error && <p className="text-red-500 text-sm mt-1">{error}</p>}
            </div>
            <div className="flex items-center">
              <button
                type="submit"
                disabled={loading}
                className="h-10 px-6 rounded-lg bg-primary text-primary-foreground hover:bg-primary/90 flex items-center gap-2 disabled:opacity-70"
              >
                {loading ? (
                  <>
                    <span className="animate-spin h-4 w-4 border-2 border-white border-t-transparent rounded-full"></span>
                    <span>解析中...</span>
                  </>
                ) : (
                  <>
                    <Search size={16} />
                    <span>解析</span>
                  </>
                )}
              </button>
            </div>
          </form>
        </div>

        {/* 结果显示 */}
        {result && (
          <div className="bg-card border border-border rounded-lg mb-6">
            <div className="p-4 border-b border-border flex items-center justify-between">
              <h3 className="font-medium">解析结果</h3>
              <div className="flex items-center gap-2">
                <button 
                  onClick={refreshDecoder}
                  className="p-2 rounded hover:bg-accent"
                  title="刷新"
                >
                  <RefreshCw size={16} />
                </button>
                <button 
                  onClick={() => copyToClipboard(result)}
                  className="p-2 rounded hover:bg-accent"
                  title="复制到剪贴板"
                >
                  <Copy size={16} />
                </button>
                <button 
                  onClick={downloadResult}
                  className="p-2 rounded hover:bg-accent"
                  title="下载结果"
                >
                  <Download size={16} />
                </button>
              </div>
            </div>
            
            <div className="p-6">
              {/* 状态信息 */}
              <div className="mb-6 flex items-center gap-3">
                <div className={`p-2 rounded-full ${result.isExpired ? 'bg-red-100 text-red-500' : 'bg-green-100 text-green-500'}`}>
                  <Shield size={20} />
                </div>
                <div>
                  <p className="font-medium">
                    {result.isExpired ? '令牌已过期' : '令牌有效'}
                  </p>
                  {result.expirationTime && (
                    <p className="text-sm text-muted-foreground">
                      过期时间: {result.expirationTime}
                    </p>
                  )}
                </div>
              </div>

              {/* 选项卡导航 */}
              <div className="flex border-b border-border mb-6">
                <button
                  onClick={() => setActiveTab('header')}
                  className={`py-2 px-4 font-medium ${
                    activeTab === 'header'
                      ? 'border-b-2 border-primary text-primary'
                      : 'text-muted-foreground'
                  }`}
                >
                  <div className="flex items-center gap-2">
                    <Key size={16} />
                    <span>头部</span>
                  </div>
                </button>
                <button
                  onClick={() => setActiveTab('payload')}
                  className={`py-2 px-4 ml-4 font-medium ${
                    activeTab === 'payload'
                      ? 'border-b-2 border-primary text-primary'
                      : 'text-muted-foreground'
                  }`}
                >
                  <div className="flex items-center gap-2">
                    <Clock size={16} />
                    <span>负载</span>
                  </div>
                </button>
              </div>

              {/* 头部内容 */}
              {activeTab === 'header' && (
                <div>
                  <div className="flex justify-between mb-4">
                    <h4 className="font-medium">算法: {result.header.alg}</h4>
                    <button
                      onClick={() => copyToClipboard(result.header)}
                      className="text-sm text-primary hover:underline flex items-center gap-1"
                    >
                      <Copy size={14} />
                      <span>复制</span>
                    </button>
                  </div>

                  <pre className="bg-accent p-4 rounded-lg overflow-auto max-h-80">
                    <code>{JSON.stringify(result.header, null, 2)}</code>
                  </pre>
                </div>
              )}

              {/* 负载内容 */}
              {activeTab === 'payload' && (
                <div>
                  <div className="flex justify-between mb-4">
                    <h4 className="font-medium">
                      {result.payload.sub ? `主题: ${result.payload.sub}` : '负载数据'}
                    </h4>
                    <button
                      onClick={() => copyToClipboard(result.payload)}
                      className="text-sm text-primary hover:underline flex items-center gap-1"
                    >
                      <Copy size={14} />
                      <span>复制</span>
                    </button>
                  </div>

                  {/* 特殊字段显示 */}
                  {(result.payload.exp || result.payload.iat || result.payload.nbf) && (
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                      {result.payload.iat && (
                        <div className="bg-accent rounded-lg p-3">
                          <p className="text-sm text-muted-foreground">签发于</p>
                          <p className="font-medium">{formatTime(result.payload.iat)}</p>
                        </div>
                      )}
                      {result.payload.nbf && (
                        <div className="bg-accent rounded-lg p-3">
                          <p className="text-sm text-muted-foreground">生效于</p>
                          <p className="font-medium">{formatTime(result.payload.nbf)}</p>
                        </div>
                      )}
                      {result.payload.exp && (
                        <div className="bg-accent rounded-lg p-3">
                          <p className="text-sm text-muted-foreground">过期于</p>
                          <p className="font-medium">{formatTime(result.payload.exp)}</p>
                        </div>
                      )}
                    </div>
                  )}

                  <pre className="bg-accent p-4 rounded-lg overflow-auto max-h-80">
                    <code>{JSON.stringify(result.payload, null, 2)}</code>
                  </pre>
                </div>
              )}

              {/* 签名信息 */}
              <div className="mt-6">
                <div className="flex justify-between mb-2">
                  <h4 className="font-medium">签名</h4>
                  <button
                    onClick={() => copyToClipboard(result.signature)}
                    className="text-sm text-primary hover:underline flex items-center gap-1"
                  >
                    <Copy size={14} />
                    <span>复制</span>
                  </button>
                </div>
                <p className="bg-accent p-3 rounded-lg text-sm break-all">{result.signature}</p>
              </div>
            </div>
          </div>
        )}

        {/* 信息说明 */}
        <div className="bg-card border border-border rounded-lg p-6">
          <h3 className="font-medium mb-4">什么是JWT?</h3>
          <p className="mb-4 text-muted-foreground">
            JSON Web Token (JWT) 是一种开放标准 (RFC 7519)，它定义了一种紧凑且独立的方式，用于在各方之间作为 JSON 对象安全地传输信息。这些信息可以被验证和信任，因为它是数字签名的。
          </p>
          
          <h4 className="font-medium mb-2">JWT组成部分:</h4>
          <ul className="list-disc list-inside mb-4 text-muted-foreground">
            <li className="mb-1"><strong>Header (头部)</strong>: 通常由两部分组成：令牌的类型（JWT）和所使用的签名算法</li>
            <li className="mb-1"><strong>Payload (负载)</strong>: 包含声明（claims），声明是关于实体（通常是用户）和其他数据的声明</li>
            <li><strong>Signature (签名)</strong>: 用于验证消息在传输过程中没有被更改，对于有私钥的令牌，还可以验证JWT的发送方是否为它所称的发送方</li>
          </ul>
          
          <p className="text-muted-foreground">
            JWT通常用于认证系统中，一旦用户登录，后续的每个请求都会包含JWT，允许用户访问该令牌允许的路由、服务和资源。
          </p>
        </div>
      </div>
    </div>
  );
}
