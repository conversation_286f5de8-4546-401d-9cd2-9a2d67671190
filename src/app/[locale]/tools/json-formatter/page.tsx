'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { ArrowLeft, Code, Copy, Download, Trash, Upload, Check, RefreshCw, Settings } from 'lucide-react';

export default function JsonFormatterPage() {
  const [jsonInput, setJsonInput] = useState<string>('');
  const [formattedJson, setFormattedJson] = useState<string>('');
  const [error, setError] = useState<string | null>(null);
  const [indent, setIndent] = useState<number>(2);
  const [copySuccess, setCopySuccess] = useState(false);
  const [loading, setLoading] = useState(false);
  const router = useRouter();

  // 示例JSON数据
  const exampleJson = `{
  "name": "JSON格式化工具",
  "description": "一个简单易用的JSON美化和格式化工具",
  "features": [
    "自动格式化",
    "语法高亮",
    "可调整缩进",
    "错误提示"
  ],
  "version": 1.0,
  "isOpenSource": true,
  "metadata": {
    "createdAt": "2023-01-01",
    "updatedAt": "2023-03-15"
  }
}`;

  // 加载示例数据
  const loadExample = () => {
    setJsonInput(exampleJson);
  };

  // 清空输入
  const clearInput = () => {
    setJsonInput('');
    setFormattedJson('');
    setError(null);
  };

  // 复制到剪贴板
  const copyToClipboard = () => {
    if (formattedJson) {
      navigator.clipboard.writeText(formattedJson)
        .then(() => {
          setCopySuccess(true);
          setTimeout(() => setCopySuccess(false), 2000);
        })
        .catch(err => {
          console.error('复制失败:', err);
        });
    }
  };

  // 下载格式化后的JSON
  const downloadJson = () => {
    if (!formattedJson) return;
    
    const blob = new Blob([formattedJson], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'formatted-json.json';
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  // 处理文件上传
  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    const reader = new FileReader();
    reader.onload = (e) => {
      const content = e.target?.result as string;
      setJsonInput(content);
    };
    reader.readAsText(file);
  };

  // 格式化JSON
  const formatJson = () => {
    if (!jsonInput.trim()) {
      setError('请输入JSON数据');
      return;
    }

    setLoading(true);
    setError(null);

    try {
      // 尝试解析JSON
      const parsedJson = JSON.parse(jsonInput);
      // 格式化JSON
      const formatted = JSON.stringify(parsedJson, null, indent);
      setFormattedJson(formatted);
    } catch (err) {
      setError(`JSON解析错误: ${err instanceof Error ? err.message : '未知错误'}`);
      setFormattedJson('');
    } finally {
      setLoading(false);
    }
  };

  // 压缩JSON (移除所有空格)
  const compressJson = () => {
    if (!jsonInput.trim()) {
      setError('请输入JSON数据');
      return;
    }

    setLoading(true);
    setError(null);

    try {
      // 尝试解析JSON
      const parsedJson = JSON.parse(jsonInput);
      // 压缩JSON (无缩进)
      const compressed = JSON.stringify(parsedJson);
      setFormattedJson(compressed);
    } catch (err) {
      setError(`JSON解析错误: ${err instanceof Error ? err.message : '未知错误'}`);
      setFormattedJson('');
    } finally {
      setLoading(false);
    }
  };

  // 当JSON输入变化时自动格式化
  useEffect(() => {
    if (jsonInput.trim()) {
      const timer = setTimeout(() => {
        formatJson();
      }, 500);
      return () => clearTimeout(timer);
    }
  }, [jsonInput, indent]);

  return (
    <div className="container mx-auto p-6">
      {/* 头部 */}
      <div className="flex items-center mb-6">
        <button 
          onClick={() => router.back()}
          className="p-2 mr-4 rounded-full hover:bg-gray-100 dark:hover:bg-gray-800"
        >
          <ArrowLeft size={24} />
        </button>
        <h1 className="text-2xl font-bold">JSON格式化</h1>
      </div>

      {/* 工具说明 */}
      <div className="mb-8 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
        <h2 className="text-lg font-medium mb-2">工具说明</h2>
        <p className="text-gray-700 dark:text-gray-300">
          本工具可以帮助您格式化、美化和验证JSON数据，使其更易读和易于理解。
          支持自定义缩进、语法错误检测和复制下载功能。所有处理均在浏览器本地完成，不会上传至服务器。
        </p>
      </div>

      {/* 缩进设置 */}
      <div className="mb-6 p-4 border border-gray-200 dark:border-gray-700 rounded-lg">
        <div className="flex items-center mb-2">
          <Settings size={18} className="mr-2" />
          <h2 className="text-lg font-medium">格式化设置</h2>
        </div>
        
        <div className="mb-2">
          <label className="block mb-2 text-sm font-medium">
            缩进空格数: {indent}
          </label>
          <input
            type="range"
            min="0"
            max="8"
            value={indent}
            onChange={(e) => setIndent(parseInt(e.target.value))}
            className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer dark:bg-gray-700"
          />
          <div className="flex justify-between text-xs text-gray-500 mt-1">
            <span>无缩进(压缩)</span>
            <span>标准(2空格)</span>
            <span>最大(8空格)</span>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* 输入区域 */}
        <div className="space-y-4">
          <div className="flex justify-between items-center">
            <h2 className="text-lg font-medium">输入JSON</h2>
            <div className="flex space-x-2">
              <button
                onClick={loadExample}
                className="text-xs p-1 px-2 rounded bg-gray-100 hover:bg-gray-200 dark:bg-gray-800 dark:hover:bg-gray-700"
              >
                加载示例
              </button>
              <button
                onClick={clearInput}
                className="text-xs p-1 px-2 rounded bg-gray-100 hover:bg-gray-200 dark:bg-gray-800 dark:hover:bg-gray-700 flex items-center"
              >
                <Trash size={12} className="mr-1" />
                清空
              </button>
              <label className="text-xs p-1 px-2 rounded bg-gray-100 hover:bg-gray-200 dark:bg-gray-800 dark:hover:bg-gray-700 flex items-center cursor-pointer">
                <Upload size={12} className="mr-1" />
                上传
                <input
                  type="file"
                  accept=".json,.txt"
                  onChange={handleFileUpload}
                  className="hidden"
                />
              </label>
            </div>
          </div>
          
          <textarea
            value={jsonInput}
            onChange={(e) => setJsonInput(e.target.value)}
            placeholder="在此粘贴或输入JSON数据..."
            className="w-full h-[400px] p-4 rounded-lg border border-gray-300 dark:border-gray-700 bg-white dark:bg-gray-900 font-mono text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
        </div>

        {/* 输出区域 */}
        <div className="space-y-4">
          <div className="flex justify-between items-center">
            <h2 className="text-lg font-medium">格式化结果</h2>
            <div className="flex space-x-2">
              <button
                onClick={formatJson}
                disabled={loading}
                className="text-xs p-1 px-2 rounded bg-blue-500 hover:bg-blue-600 text-white flex items-center"
              >
                {loading ? <RefreshCw size={12} className="animate-spin mr-1" /> : <Code size={12} className="mr-1" />}
                格式化
              </button>
              <button
                onClick={compressJson}
                disabled={loading}
                className="text-xs p-1 px-2 rounded bg-green-500 hover:bg-green-600 text-white flex items-center"
              >
                <Code size={12} className="mr-1" />
                压缩
              </button>
              <button
                onClick={copyToClipboard}
                disabled={!formattedJson}
                className={`text-xs p-1 px-2 rounded ${
                  copySuccess 
                    ? 'bg-green-500 text-white' 
                    : 'bg-gray-100 hover:bg-gray-200 dark:bg-gray-800 dark:hover:bg-gray-700'
                } flex items-center`}
              >
                {copySuccess ? <Check size={12} className="mr-1" /> : <Copy size={12} className="mr-1" />}
                {copySuccess ? '已复制' : '复制'}
              </button>
              <button
                onClick={downloadJson}
                disabled={!formattedJson}
                className="text-xs p-1 px-2 rounded bg-gray-100 hover:bg-gray-200 dark:bg-gray-800 dark:hover:bg-gray-700 flex items-center"
              >
                <Download size={12} className="mr-1" />
                下载
              </button>
            </div>
          </div>
          
          {error ? (
            <div className="p-4 rounded-lg bg-red-50 dark:bg-red-900/20 text-red-600 dark:text-red-400 font-mono text-sm">
              {error}
            </div>
          ) : formattedJson ? (
            <pre className="w-full h-[400px] p-4 rounded-lg border border-gray-300 dark:border-gray-700 bg-white dark:bg-gray-900 font-mono text-sm overflow-auto">
              {formattedJson}
            </pre>
          ) : (
            <div className="w-full h-[400px] p-4 rounded-lg border border-gray-300 dark:border-gray-700 bg-white dark:bg-gray-900 flex items-center justify-center text-gray-400">
              <p>格式化后的结果将显示在这里</p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
} 