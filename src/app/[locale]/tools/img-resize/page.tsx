'use client';

import { useState, useCallback } from 'react';
import { useDropzone } from 'react-dropzone';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Checkbox } from '@/components/ui/checkbox';
import { useToast } from '@/hooks/use-toast';
import { useTranslations } from 'next-intl';

const PRESET_SIZES = {
  '4:3': { width: 1920, height: 1440 },
  '3:4': { width: 1440, height: 1920 },
  '16:9': { width: 1920, height: 1080 },
  '9:16': { width: 1080, height: 1920 },
  'custom': { width: 3945, height: 5909 }
};

export default function ImageResizePage() {
  const { toast } = useToast();
  const t = useTranslations('tools.img-resize');
  const [image, setImage] = useState<string | null>(null);
  const [resizeMode, setResizeMode] = useState<'pixel' | 'percentage' | 'preset'>('pixel');
  const [width, setWidth] = useState<number>(0);
  const [height, setHeight] = useState<number>(0);
  const [percentage, setPercentage] = useState<number>(100);
  const [originalSize, setOriginalSize] = useState<{ width: number; height: number } | null>(null);
  const [maintainAspectRatio, setMaintainAspectRatio] = useState<boolean>(true);
  const [selectedPreset, setSelectedPreset] = useState<keyof typeof PRESET_SIZES>('custom');
  const [resizedImage, setResizedImage] = useState<string | null>(null);
  const [newSize, setNewSize] = useState<{ width: number; height: number } | null>(null);

  const updateDimensions = (newWidth?: number, newHeight?: number) => {
    if (!originalSize || !maintainAspectRatio) {
      if (newWidth !== undefined) setWidth(newWidth);
      if (newHeight !== undefined) setHeight(newHeight);
      return;
    }

    const originalRatio = originalSize.width / originalSize.height;
    
    if (newWidth !== undefined) {
      setWidth(newWidth);
      setHeight(Math.round(newWidth / originalRatio));
    } else if (newHeight !== undefined) {
      setHeight(newHeight);
      setWidth(Math.round(newHeight * originalRatio));
    }
  };

  const onDrop = useCallback((acceptedFiles: File[]) => {
    const file = acceptedFiles[0];
    if (file) {
      if (!file.type.startsWith('image/')) {
        toast({
          title: t('error.invalidFile'),
          description: t('error.pleaseUploadImage'),
          variant: 'destructive',
        });
        return;
      }

      const reader = new FileReader();
      reader.onload = (e) => {
        const img = new Image();
        img.onload = () => {
          setOriginalSize({ width: img.width, height: img.height });
          setImage(e.target?.result as string);
          updateDimensions(img.width, img.height);
        };
        img.src = e.target?.result as string;
      };
      reader.readAsDataURL(file);
    }
  }, [toast, t]);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'image/*': ['.png', '.jpg', '.jpeg', '.gif', '.webp']
    },
    maxFiles: 1
  });

  const calculateDimensions = (originalWidth: number, originalHeight: number, targetWidth: number, targetHeight: number) => {
    if (!maintainAspectRatio) {
      return { width: targetWidth, height: targetHeight };
    }

    // 计算长宽比
    const originalRatio = originalWidth / originalHeight;
    const targetRatio = targetWidth / targetHeight;

    let newWidth, newHeight;

    if (originalRatio > targetRatio) {
      // 原图更宽，以高度为基准
      newHeight = targetHeight;
      newWidth = Math.round(newHeight * originalRatio);
    } else {
      // 原图更高，以宽度为基准
      newWidth = targetWidth;
      newHeight = Math.round(newWidth / originalRatio);
    }

    return { width: newWidth, height: newHeight };
  };

  const handleResize = () => {
    if (!image || !originalSize) return;

    const img = new Image();
    img.onload = () => {
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');
      if (!ctx) return;

      let newWidth, newHeight;
      if (resizeMode === 'preset') {
        const presetSize = PRESET_SIZES[selectedPreset];
        const dimensions = calculateDimensions(
          originalSize.width,
          originalSize.height,
          presetSize.width,
          presetSize.height
        );
        newWidth = dimensions.width;
        newHeight = dimensions.height;
      } else if (resizeMode === 'pixel') {
        const dimensions = calculateDimensions(
          originalSize.width,
          originalSize.height,
          width,
          height
        );
        newWidth = dimensions.width;
        newHeight = dimensions.height;
      } else {
        newWidth = Math.round(originalSize.width * (percentage / 100));
        newHeight = Math.round(originalSize.height * (percentage / 100));
      }

      canvas.width = newWidth;
      canvas.height = newHeight;
      ctx.drawImage(img, 0, 0, newWidth, newHeight);

      // 使用较低的质量来减小文件大小
      const resizedImageData = canvas.toDataURL('image/jpeg', 0.8);
      setResizedImage(resizedImageData);
      setNewSize({ width: newWidth, height: newHeight });

      toast({
        title: t('success.title'),
        description: t('success.description'),
      });
    };
    img.src = image;
  };

  const handleDownload = () => {
    if (!resizedImage) return;

    const link = document.createElement('a');
    link.href = resizedImage;
    link.download = 'resized-image.jpg';
    link.click();

    toast({
      title: t('download.success.title'),
      description: t('download.success.description'),
    });
  };

  return (
    <div className="container mx-auto p-4">
      <Card>
        <CardHeader>
          <CardTitle>{t('title')}</CardTitle>
          <CardDescription>{t('description')}</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* 左侧图片预览区域 */}
            <div className="space-y-4">
              <div
                {...getRootProps()}
                className={`border-2 border-dashed rounded-lg p-4 text-center cursor-pointer transition-colors
                  ${isDragActive ? 'border-primary bg-primary/5' : 'border-gray-300 hover:border-primary'}`}
              >
                <input {...getInputProps()} />
                {image ? (
                  <div className="space-y-4">
                    <img
                      src={image}
                      alt="Preview"
                      className="max-w-full h-auto mx-auto rounded-lg"
                    />
                    {originalSize && (
                      <div className="text-sm text-gray-500">
                        {t('originalSize')}: {originalSize.width} x {originalSize.height}
                      </div>
                    )}
                  </div>
                ) : (
                  <div className="space-y-2">
                    <p className="text-lg">{t('dragAndDrop')}</p>
                    <p className="text-sm text-gray-500">{t('orClickToSelect')}</p>
                    <p className="text-xs text-gray-400 mt-2">{t('supportedFormats')}</p>
                  </div>
                )}
              </div>

              {resizedImage && newSize && (
                <div className="border rounded-lg p-4 space-y-4">
                  <h3 className="font-medium">{t('preview.title')}</h3>
                  <img
                    src={resizedImage}
                    alt="Resized Preview"
                    className="max-w-full h-auto mx-auto rounded-lg"
                  />
                  <div className="text-sm text-gray-500">
                    {t('newSize')}: {newSize.width} x {newSize.height}
                  </div>
                  <Button 
                    onClick={handleDownload} 
                    className="w-full"
                  >
                    {t('download.button')}
                  </Button>
                </div>
              )}
            </div>

            {/* 右侧参数设置区域 */}
            <div className="space-y-6">
              <div className="space-y-4">
                <RadioGroup
                  value={resizeMode}
                  onValueChange={(value: 'pixel' | 'percentage' | 'preset') => setResizeMode(value)}
                  className="flex flex-col space-y-2"
                >
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="pixel" id="pixel" />
                    <Label htmlFor="pixel">{t('resizeByPixel')}</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="percentage" id="percentage" />
                    <Label htmlFor="percentage">{t('resizeByPercentage')}</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="preset" id="preset" />
                    <Label htmlFor="preset">{t('presetSize')}</Label>
                  </div>
                </RadioGroup>

                {resizeMode === 'pixel' ? (
                  <div className="space-y-4">
                    <div className="grid grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="width">{t('width')}</Label>
                        <Input
                          id="width"
                          type="number"
                          value={width}
                          onChange={(e) => updateDimensions(Number(e.target.value))}
                          min={1}
                          onClick={(e) => e.stopPropagation()}
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="height">{t('height')}</Label>
                        <Input
                          id="height"
                          type="number"
                          value={height}
                          onChange={(e) => updateDimensions(undefined, Number(e.target.value))}
                          min={1}
                          onClick={(e) => e.stopPropagation()}
                        />
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="maintainAspectRatio"
                        checked={maintainAspectRatio}
                        onCheckedChange={(checked) => {
                          setMaintainAspectRatio(checked as boolean);
                          if (checked) {
                            updateDimensions(width);
                          }
                        }}
                      />
                      <Label htmlFor="maintainAspectRatio">{t('maintainAspectRatio')}</Label>
                    </div>
                  </div>
                ) : resizeMode === 'percentage' ? (
                  <div className="space-y-2">
                    <Label htmlFor="percentage">{t('percentage')}</Label>
                    <Input
                      id="percentage"
                      type="number"
                      value={percentage}
                      onChange={(e) => setPercentage(Number(e.target.value))}
                      min={1}
                      max={200}
                      onClick={(e) => e.stopPropagation()}
                    />
                  </div>
                ) : (
                  <div className="space-y-4">
                    <div className="grid grid-cols-2 gap-4">
                      {Object.entries(PRESET_SIZES).map(([ratio, size]) => (
                        <div
                          key={ratio}
                          className={`p-4 border rounded-lg cursor-pointer transition-colors ${
                            selectedPreset === ratio
                              ? 'border-primary bg-primary/5'
                              : 'border-gray-300 hover:border-primary'
                          }`}
                          onClick={() => {
                            setSelectedPreset(ratio as keyof typeof PRESET_SIZES);
                            if (ratio !== 'custom') {
                              setWidth(size.width);
                              setHeight(size.height);
                            }
                          }}
                        >
                          <div className="text-sm font-medium">{t(`presetSizes.${ratio}`)}</div>
                          <div className="text-xs text-gray-500">
                            {size.width} x {size.height}
                          </div>
                        </div>
                      ))}
                    </div>
                    {selectedPreset === 'custom' && (
                      <div className="grid grid-cols-2 gap-4">
                        <div className="space-y-2">
                          <Label htmlFor="customWidth">{t('width')}</Label>
                          <Input
                            id="customWidth"
                            type="number"
                            value={width}
                            onChange={(e) => setWidth(Number(e.target.value))}
                            min={1}
                            onClick={(e) => e.stopPropagation()}
                          />
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor="customHeight">{t('height')}</Label>
                          <Input
                            id="customHeight"
                            type="number"
                            value={height}
                            onChange={(e) => setHeight(Number(e.target.value))}
                            min={1}
                            onClick={(e) => e.stopPropagation()}
                          />
                        </div>
                      </div>
                    )}
                    <div className="text-xs text-gray-400">
                      {t('presetSizeNote')}
                    </div>
                  </div>
                )}

                <Button 
                  onClick={handleResize} 
                  className="w-full"
                  disabled={!image}
                >
                  {t('resize')}
                </Button>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
} 