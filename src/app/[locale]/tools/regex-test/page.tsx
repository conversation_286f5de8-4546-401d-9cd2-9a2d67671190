'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { ArrowLeft, CheckCircle, Copy, Settings, Info, X, Check, RefreshCw } from 'lucide-react';

type RegexFlag = 'g' | 'i' | 'm' | 's' | 'u' | 'y';

export default function RegexTestPage() {
  const [pattern, setPattern] = useState<string>('');
  const [flags, setFlags] = useState<RegexFlag[]>(['g']);
  const [testString, setTestString] = useState<string>('');
  const [result, setResult] = useState<{
    matches: string[];
    isValid: boolean;
    errorMessage?: string;
  }>({ matches: [], isValid: true });
  const [matchIndices, setMatchIndices] = useState<Array<[number, number]>>([]);
  const [selectedLanguage, setSelectedLanguage] = useState<'javascript' | 'python' | 'php'>('javascript');
  const [copySuccess, setCopySuccess] = useState(false);
  const [loading, setLoading] = useState(false);
  
  const router = useRouter();

  // 示例正则
  const examples = {
    email: {
      pattern: '[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}',
      flags: ['g'] as RegexFlag[],
      testString: 'My <NAME_EMAIL> and work <NAME_EMAIL>',
      description: '匹配电子邮件地址'
    },
    phone: {
      pattern: '\\+?(?:\\d{1,3})?[-.\\s]?\\(?(?:\\d{1,3})\\)?[-.\\s]?\\d{1,4}[-.\\s]?\\d{4}',
      flags: ['g'] as RegexFlag[],
      testString: 'Call me at +86 123-4567-8901 or (010) 12345678',
      description: '匹配电话号码，支持多种格式'
    },
    url: {
      pattern: 'https?:\\/\\/(?:www\\.)?[-a-zA-Z0-9@:%._\\+~#=]{1,256}\\.[a-zA-Z0-9()]{1,6}\\b(?:[-a-zA-Z0-9()@:%_\\+.~#?&\\/=]*)',
      flags: ['g'] as RegexFlag[],
      testString: 'Visit our website at https://wenhaofree.com or http://www.example.co.uk',
      description: '匹配URL链接'
    },
    ipv4: {
      pattern: '(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)',
      flags: ['g'] as RegexFlag[],
      testString: 'Server IPs: ***********, ********, **********, *************',
      description: '匹配IPv4地址'
    },
    date: {
      pattern: '\\d{4}[-/.]\\d{1,2}[-/.]\\d{1,2}',
      flags: ['g'] as RegexFlag[],
      testString: 'Meeting scheduled on 2023-03-15 and deadline is 2023/12/31',
      description: '匹配日期格式 (YYYY-MM-DD)'
    }
  };

  // 切换正则表达式标志
  const toggleFlag = (flag: RegexFlag) => {
    if (flags.includes(flag)) {
      setFlags(flags.filter(f => f !== flag));
    } else {
      setFlags([...flags, flag]);
    }
  };

  // 将当前正则表达式复制为编程语言代码
  const getCodeForLanguage = () => {
    const flagsStr = flags.join('');
    
    switch (selectedLanguage) {
      case 'javascript':
        return `const regex = /${pattern}/${flagsStr};
const matches = myString.match(regex);
// 或者使用 exec 方法进行匹配
// let match;
// while ((match = regex.exec(myString)) !== null) {
//   console.log(\`Found \${match[0]} at \${match.index}\`);
// }`;
        
      case 'python':
        return `import re

pattern = r'${pattern}'
text = """${testString}"""

# 使用 re.findall 查找所有匹配
matches = re.findall(pattern, text, ${flags.includes('i') ? 're.IGNORECASE' : '0'}${flags.includes('m') ? ' | re.MULTILINE' : ''}${flags.includes('s') ? ' | re.DOTALL' : ''})

# 或者使用 re.search 查找第一个匹配
# match = re.search(pattern, text)
# if match:
#     print(f"Found {match.group(0)} at {match.start()}")`;
        
      case 'php':
        return `<?php
$pattern = '/${pattern}/${flagsStr}';
$text = "${testString.replace(/"/g, '\\"')}";

// 使用 preg_match_all 查找所有匹配
preg_match_all($pattern, $text, $matches);
print_r($matches[0]);

// 或者使用 preg_match 查找第一个匹配
// if (preg_match($pattern, $text, $match)) {
//     echo "Found " . $match[0];
// }
?>`;
        
      default:
        return '';
    }
  };

  // 复制代码到剪贴板
  const copyCode = () => {
    const code = getCodeForLanguage();
    navigator.clipboard.writeText(code)
      .then(() => {
        setCopySuccess(true);
        setTimeout(() => setCopySuccess(false), 2000);
      })
      .catch(err => {
        console.error('复制失败:', err);
      });
  };

  // 加载示例
  const loadExample = (exampleKey: keyof typeof examples) => {
    const example = examples[exampleKey];
    setPattern(example.pattern);
    setFlags(example.flags);
    setTestString(example.testString);
  };

  // 清空输入
  const clearAll = () => {
    setPattern('');
    setFlags(['g']);
    setTestString('');
    setResult({ matches: [], isValid: true });
    setMatchIndices([]);
  };

  // 测试正则表达式
  const testRegex = () => {
    if (!pattern) {
      setResult({
        matches: [],
        isValid: false,
        errorMessage: '请输入正则表达式'
      });
      return;
    }

    setLoading(true);
    
    try {
      // 创建正则表达式对象
      const flagsStr = flags.join('');
      const regex = new RegExp(pattern, flagsStr);
      
      // 查找所有匹配
      const matches: string[] = [];
      const indices: Array<[number, number]> = [];
      
      if (testString) {
        let match;
        while ((match = regex.exec(testString)) !== null) {
          matches.push(match[0]);
          indices.push([match.index, match.index + match[0].length]);
          
          // 如果没有全局标志，防止无限循环
          if (!flags.includes('g')) break;
        }
      }
      
      setResult({
        matches,
        isValid: true
      });
      setMatchIndices(indices);
    } catch (err) {
      setResult({
        matches: [],
        isValid: false,
        errorMessage: err instanceof Error ? err.message : '无效的正则表达式'
      });
      setMatchIndices([]);
    } finally {
      setLoading(false);
    }
  };

  // 当正则表达式或测试字符串变化时自动测试
  useEffect(() => {
    if (pattern) {
      const timer = setTimeout(() => {
        testRegex();
      }, 500);
      return () => clearTimeout(timer);
    }
  }, [pattern, flags.join(''), testString]);
  
  // 带有高亮匹配的测试字符串
  const highlightedText = () => {
    if (!testString || matchIndices.length === 0) {
      return <span>{testString}</span>;
    }
    
    const parts: JSX.Element[] = [];
    let lastIndex = 0;
    
    matchIndices.forEach(([start, end], i) => {
      // 添加匹配前的文本
      if (start > lastIndex) {
        parts.push(
          <span key={`text-${i}`}>{testString.substring(lastIndex, start)}</span>
        );
      }
      
      // 添加匹配的文本
      parts.push(
        <span key={`match-${i}`} className="bg-green-200 dark:bg-green-900">
          {testString.substring(start, end)}
        </span>
      );
      
      lastIndex = end;
    });
    
    // 添加最后一个匹配后的文本
    if (lastIndex < testString.length) {
      parts.push(
        <span key="text-last">{testString.substring(lastIndex)}</span>
      );
    }
    
    return <>{parts}</>;
  };

  return (
    <div className="container mx-auto p-6">
      {/* 头部 */}
      <div className="flex items-center mb-6">
        <button 
          onClick={() => router.back()}
          className="p-2 mr-4 rounded-full hover:bg-gray-100 dark:hover:bg-gray-800"
        >
          <ArrowLeft size={24} />
        </button>
        <h1 className="text-2xl font-bold">正则表达式测试</h1>
      </div>

      {/* 工具说明 */}
      <div className="mb-8 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
        <h2 className="text-lg font-medium mb-2">工具说明</h2>
        <p className="text-gray-700 dark:text-gray-300">
          本工具可以帮助您测试和验证正则表达式，实时显示匹配结果，支持多种正则标志和编程语言格式。
          您可以使用提供的示例快速开始，并将结果导出为不同编程语言的代码。
        </p>
      </div>

      {/* 工具控制区域 */}
      <div className="flex flex-col md:flex-row gap-6 mb-6">
        {/* 左侧控制面板 */}
        <div className="w-full md:w-1/3 space-y-6">
          {/* 正则表达式输入 */}
          <div className="p-4 border border-gray-200 dark:border-gray-700 rounded-lg">
            <label className="block text-sm font-medium mb-2">正则表达式</label>
            <div className="flex">
              <span className="bg-gray-100 dark:bg-gray-800 border border-r-0 border-gray-300 dark:border-gray-700 rounded-l-md px-3 flex items-center">/</span>
              <input
                type="text"
                value={pattern}
                onChange={e => setPattern(e.target.value)}
                placeholder="输入正则表达式..."
                className="flex-1 p-2 border border-gray-300 dark:border-gray-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
              <span className="bg-gray-100 dark:bg-gray-800 border border-l-0 border-gray-300 dark:border-gray-700 rounded-r-md px-3 flex items-center">/{flags.join('')}</span>
            </div>

            {!result.isValid && (
              <div className="mt-2 text-red-500 text-sm flex items-center">
                <X size={16} className="mr-1" />
                {result.errorMessage}
              </div>
            )}
          </div>

          {/* 正则标志 */}
          <div className="p-4 border border-gray-200 dark:border-gray-700 rounded-lg">
            <h3 className="text-sm font-medium mb-3 flex items-center">
              <Settings size={16} className="mr-2" />
              正则标志
            </h3>
            <div className="flex flex-wrap gap-2">
              {[
                { flag: 'g' as RegexFlag, name: '全局匹配', desc: '查找所有匹配项' },
                { flag: 'i' as RegexFlag, name: '忽略大小写', desc: '匹配时不区分大小写' },
                { flag: 'm' as RegexFlag, name: '多行模式', desc: '^ 和 $ 匹配每一行的开始和结束' },
                { flag: 's' as RegexFlag, name: '点号匹配换行', desc: '. 可以匹配换行符' },
                { flag: 'u' as RegexFlag, name: 'Unicode', desc: '启用Unicode匹配' },
                { flag: 'y' as RegexFlag, name: '粘性匹配', desc: '从正则表达式的lastIndex开始匹配' }
              ].map(({ flag, name, desc }) => (
                <button
                  key={flag}
                  onClick={() => toggleFlag(flag)}
                  className={`inline-flex items-center px-3 py-1 rounded text-sm ${
                    flags.includes(flag)
                      ? 'bg-blue-500 text-white'
                      : 'bg-gray-100 hover:bg-gray-200 dark:bg-gray-800 dark:hover:bg-gray-700'
                  }`}
                  title={desc}
                >
                  {flags.includes(flag) && <CheckCircle size={14} className="mr-1" />}
                  {flag}
                </button>
              ))}
            </div>
          </div>

          {/* 示例正则 */}
          <div className="p-4 border border-gray-200 dark:border-gray-700 rounded-lg">
            <h3 className="text-sm font-medium mb-3 flex items-center">
              <Info size={16} className="mr-2" />
              示例正则
            </h3>
            <div className="space-y-2">
              {Object.entries(examples).map(([key, example]) => (
                <button
                  key={key}
                  onClick={() => loadExample(key as keyof typeof examples)}
                  className="w-full text-left p-2 text-sm rounded hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors"
                >
                  <div className="font-medium">{example.description}</div>
                  <code className="text-xs text-gray-600 dark:text-gray-400">{example.pattern}</code>
                </button>
              ))}
            </div>
          </div>

          {/* 代码生成 */}
          <div className="p-4 border border-gray-200 dark:border-gray-700 rounded-lg">
            <div className="flex justify-between items-center mb-3">
              <h3 className="text-sm font-medium flex items-center">
                <Settings size={16} className="mr-2" />
                代码生成
              </h3>
              <div className="flex gap-1">
                {['javascript', 'python', 'php'].map(lang => (
                  <button
                    key={lang}
                    onClick={() => setSelectedLanguage(lang as any)}
                    className={`px-2 py-1 text-xs rounded ${
                      selectedLanguage === lang
                        ? 'bg-blue-500 text-white'
                        : 'bg-gray-100 hover:bg-gray-200 dark:bg-gray-800 dark:hover:bg-gray-700'
                    }`}
                  >
                    {lang}
                  </button>
                ))}
              </div>
            </div>
            
            <div className="relative">
              <pre className="bg-gray-100 dark:bg-gray-800 rounded p-3 text-xs overflow-auto max-h-48">
                {getCodeForLanguage()}
              </pre>
              
              <button
                onClick={copyCode}
                disabled={!pattern}
                className={`absolute top-2 right-2 p-1 rounded ${
                  copySuccess
                    ? 'bg-green-500 text-white'
                    : 'bg-gray-200 hover:bg-gray-300 dark:bg-gray-700 dark:hover:bg-gray-600'
                }`}
                title="复制代码"
              >
                {copySuccess ? <Check size={16} /> : <Copy size={16} />}
              </button>
            </div>
          </div>
        </div>

        {/* 右侧测试区域 */}
        <div className="w-full md:w-2/3 space-y-6">
          {/* 测试字符串输入 */}
          <div className="p-4 border border-gray-200 dark:border-gray-700 rounded-lg">
            <div className="flex justify-between items-center mb-2">
              <label className="block text-sm font-medium">测试字符串</label>
              <button
                onClick={clearAll}
                className="text-xs p-1 px-2 rounded bg-gray-100 hover:bg-gray-200 dark:bg-gray-800 dark:hover:bg-gray-700"
              >
                清空所有
              </button>
            </div>
            <textarea
              value={testString}
              onChange={e => setTestString(e.target.value)}
              placeholder="输入要测试的字符串..."
              className="w-full h-40 p-3 border border-gray-300 dark:border-gray-700 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 font-mono text-sm"
            />
          </div>

          {/* 匹配结果 */}
          <div className="p-4 border border-gray-200 dark:border-gray-700 rounded-lg">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-sm font-medium flex items-center">
                {loading ? (
                  <RefreshCw size={16} className="mr-2 animate-spin" />
                ) : (
                  result.isValid && <CheckCircle size={16} className="mr-2 text-green-500" />
                )}
                匹配结果
              </h3>
              
              <div className="text-sm">
                匹配数量: <span className="font-medium">{result.matches.length}</span>
              </div>
            </div>
            
            {/* 高亮的测试字符串 */}
            <div className="mb-4 p-3 bg-white dark:bg-gray-900 border border-gray-200 dark:border-gray-800 rounded font-mono text-sm whitespace-pre-wrap">
              {highlightedText()}
            </div>
            
            {/* 匹配列表 */}
            {result.matches.length > 0 ? (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                {result.matches.map((match, i) => (
                  <div 
                    key={i} 
                    className="flex items-center p-2 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-900 rounded"
                  >
                    <span className="mr-2 text-xs bg-gray-200 dark:bg-gray-700 px-1 rounded">
                      {i + 1}
                    </span>
                    <code className="font-mono text-sm flex-1 truncate">
                      {match || <span className="text-gray-400 italic">空字符串</span>}
                    </code>
                    <span className="text-xs text-gray-500">
                      长度: {match.length}
                    </span>
                  </div>
                ))}
              </div>
            ) : pattern && result.isValid && !loading ? (
              <div className="text-center p-4 text-gray-500">
                未找到匹配项
              </div>
            ) : null}
          </div>
        </div>
      </div>
    </div>
  );
} 