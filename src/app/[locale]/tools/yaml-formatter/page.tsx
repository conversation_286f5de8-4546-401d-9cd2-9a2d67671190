'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { ArrowLeft, Search, Download, Copy, RefreshCw, FileText, AlertTriangle } from 'lucide-react';
import * as yaml from 'js-yaml';

export default function YamlFormatterPage() {
  const [yamlInput, setYamlInput] = useState('');
  const [yamlOutput, setYamlOutput] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [indentSize, setIndentSize] = useState(2);
  const router = useRouter();

  // 格式化处理函数
  const handleFormat = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!yamlInput.trim()) {
      setError('请输入YAML数据');
      return;
    }
    
    setLoading(true);
    setError(null);
    
    try {
      // 解析YAML到对象
      const yamlObj = yaml.load(yamlInput);
      
      // 再次将对象转换为YAML字符串，实现格式化
      const formattedYaml = yaml.dump(yamlObj, {
        indent: indentSize,
        lineWidth: -1, // 不限制行宽
        noRefs: true, // 避免YAML引用标记
        noCompatMode: true, // 使用更现代的YAML格式
      });
      
      setYamlOutput(formattedYaml);
    } catch (err) {
      setError(err instanceof Error ? `YAML解析错误: ${err.message}` : '无效的YAML格式');
    } finally {
      setLoading(false);
    }
  };

  // 示例YAML数据
  const loadExample = () => {
    const exampleYaml = `# 这是一个格式不太规范的YAML示例
person:
 name: 张三
 age: 28
 email: <EMAIL>
 isActive: true
 address:
   city: 北京
   street: 朝阳区
   postcode: '100000'
 hobbies: [读书, 旅行, 编程]
  education:
  - degree: 学士
    major: 计算机科学
    year: 2018
  - degree: 硕士
    major: 人工智能
    year: 2020

# 公司信息
company:
  name: 科技有限公司
  employees: 250
  departments: [研发, 市场, 销售, 人力资源]

# 应用设置
settings:
    theme: dark
    notifications: {email: true, sms: false, push: true}`;
    
    setYamlInput(exampleYaml);
    setError(null);
  };

  // 复制到剪贴板
  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
  };

  // 下载YAML结果
  const downloadYaml = () => {
    if (yamlOutput) {
      const blob = new Blob([yamlOutput], { type: 'text/yaml' });
      const url = URL.createObjectURL(blob);
      
      const a = document.createElement('a');
      a.href = url;
      a.download = 'formatted.yaml';
      document.body.appendChild(a);
      a.click();
      
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
    }
  };

  // 清空输入
  const clearInput = () => {
    setYamlInput('');
    setYamlOutput('');
    setError(null);
  };

  return (
    <div className="min-h-screen bg-background p-6">
      <div className="max-w-6xl mx-auto">
        {/* 返回按钮 */}
        <button 
          onClick={() => router.back()}
          className="mb-6 flex items-center gap-2 text-primary hover:underline"
        >
          <ArrowLeft size={16} />
          <span>返回工具列表</span>
        </button>

        {/* 工具标题 */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold">YAML格式化</h1>
          <p className="text-muted-foreground mt-2">
            格式化YAML数据，使其更加易于阅读和编辑，同时验证YAML语法
          </p>
        </div>

        {/* 选项设置 */}
        <div className="mb-6">
          <h2 className="font-medium mb-3">格式化选项</h2>
          <div className="flex items-center gap-6">
            <div>
              <label className="text-sm text-muted-foreground block mb-1">缩进大小</label>
              <select 
                value={indentSize}
                onChange={(e) => setIndentSize(Number(e.target.value))}
                className="h-9 px-3 rounded bg-accent border border-border focus:outline-none focus:ring-2 focus:ring-primary"
              >
                <option value={2}>2个空格</option>
                <option value={4}>4个空格</option>
                <option value={8}>8个空格</option>
              </select>
            </div>
          </div>
        </div>

        {/* 转换区域 */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
          {/* YAML输入区 */}
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <h2 className="font-medium">YAML输入</h2>
              <div className="flex items-center gap-2">
                <button 
                  onClick={clearInput}
                  className="text-xs px-2 py-1 rounded bg-accent hover:bg-accent/80 text-accent-foreground"
                >
                  清空
                </button>
                <button 
                  onClick={loadExample}
                  className="text-xs px-2 py-1 rounded bg-accent hover:bg-accent/80 text-accent-foreground"
                >
                  加载示例
                </button>
                <button 
                  onClick={() => copyToClipboard(yamlInput)}
                  className="p-1 rounded hover:bg-accent"
                  title="复制"
                >
                  <Copy size={14} />
                </button>
              </div>
            </div>
            <textarea
              value={yamlInput}
              onChange={(e) => setYamlInput(e.target.value)}
              placeholder="在此粘贴需要格式化的YAML数据..."
              className="w-full h-[500px] p-4 font-mono text-sm rounded-lg bg-accent border border-border focus:outline-none focus:ring-2 focus:ring-primary resize-none"
            ></textarea>
            {error && (
              <div className="flex items-center gap-2 text-red-500 text-sm">
                <AlertTriangle size={14} />
                <span>{error}</span>
              </div>
            )}
          </div>

          {/* YAML输出区 */}
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <h2 className="font-medium">格式化结果</h2>
              <div className="flex items-center gap-2">
                {yamlOutput && (
                  <>
                    <button 
                      onClick={() => copyToClipboard(yamlOutput)}
                      className="text-xs px-2 py-1 rounded bg-primary hover:bg-primary/90 text-primary-foreground flex items-center gap-1"
                    >
                      <Copy size={14} />
                      <span>复制</span>
                    </button>
                    <button 
                      onClick={downloadYaml}
                      className="text-xs px-2 py-1 rounded bg-primary hover:bg-primary/90 text-primary-foreground flex items-center gap-1"
                    >
                      <Download size={14} />
                      <span>下载</span>
                    </button>
                  </>
                )}
              </div>
            </div>
            <div className="relative h-[500px]">
              <textarea
                value={yamlOutput}
                readOnly
                placeholder="格式化后的YAML将显示在这里..."
                className="w-full h-full p-4 font-mono text-sm rounded-lg bg-accent border border-border focus:outline-none resize-none"
              ></textarea>
              {!yamlOutput && (
                <div className="absolute inset-0 flex items-center justify-center">
                  <div className="text-center text-muted-foreground p-4">
                    <FileText size={28} className="mx-auto mb-2 opacity-40" />
                    <p>格式化后的YAML将显示在这里</p>
                    <p className="text-xs mt-1">点击下方按钮开始格式化</p>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* 格式化按钮 */}
        <div className="flex justify-center mb-8">
          <button
            onClick={handleFormat}
            disabled={loading || !yamlInput.trim()}
            className="h-12 px-8 rounded-lg bg-primary text-primary-foreground hover:bg-primary/90 flex items-center gap-3 disabled:opacity-70 disabled:cursor-not-allowed"
          >
            {loading ? (
              <>
                <span className="animate-spin h-5 w-5 border-2 border-white border-t-transparent rounded-full"></span>
                <span>格式化中...</span>
              </>
            ) : (
              <>
                <FileText size={18} />
                <span>格式化 YAML</span>
              </>
            )}
          </button>
        </div>

        {/* 信息说明 */}
        <div className="bg-card border border-border rounded-lg p-6">
          <h3 className="font-medium mb-4">YAML格式化说明</h3>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h4 className="font-medium mb-2">为什么需要格式化YAML?</h4>
              <ul className="list-disc list-inside mb-4 text-muted-foreground text-sm space-y-1">
                <li>提高可读性和可维护性</li>
                <li>修复缩进和语法错误</li>
                <li>统一代码风格和格式</li>
                <li>验证YAML文件是否合法</li>
                <li>删除不必要的空格和空行</li>
              </ul>
            </div>
            
            <div>
              <h4 className="font-medium mb-2">YAML的特点</h4>
              <ul className="list-disc list-inside mb-4 text-muted-foreground text-sm space-y-1">
                <li>缩进敏感，对空格要求严格</li>
                <li>支持层次结构的数据表示</li>
                <li>支持列表、映射和标量值</li>
                <li>可以使用锚点和引用减少重复</li>
                <li>支持多行字符串和注释</li>
              </ul>
            </div>
          </div>
          
          <div className="mt-4 text-sm text-muted-foreground">
            <p>格式化提示：</p>
            <ul className="list-disc list-inside mt-2 space-y-1">
              <li>本工具将自动修复YAML中不一致的缩进问题</li>
              <li>若YAML语法严重错误，可能无法正确格式化</li>
              <li>格式化过程会将注释和一些原始格式化信息丢失</li>
              <li>对于锚点和引用，可能会被展开为完整内容</li>
              <li>如果您的YAML中包含敏感数据，请不要上传到任何在线格式化工具</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
}
