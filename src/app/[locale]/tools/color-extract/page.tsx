'use client';

import { useState, useRef } from 'react';
import { useRouter } from 'next/navigation';
import { ArrowLeft, Upload, Copy, Download, RefreshCw, Palette } from 'lucide-react';

interface ColorInfo {
  rgb: string;
  hex: string;
  percentage: number;
  r: number;
  g: number;
  b: number;
}

interface ColorRelations {
  complementary: string;
  analogous: string[];
  triadic: string[];
  tetradic: string[];
  shades: string[];
  tints: string[];
}

export default function ColorExtractPage() {
  const [image, setImage] = useState<string | null>(null);
  const [colors, setColors] = useState<ColorInfo[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [selectedColor, setSelectedColor] = useState<ColorInfo | null>(null);
  const [showColorPicker, setShowColorPicker] = useState(false);
  const [colorRelations, setColorRelations] = useState<ColorRelations | null>(null);
  const [activeTab, setActiveTab] = useState<'main' | 'relations'>('main');
  const [activeTool, setActiveTool] = useState<string | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const router = useRouter();
  
  // 创建内容区域的引用
  const uploadRef = useRef<HTMLDivElement>(null);
  const previewRef = useRef<HTMLDivElement>(null);
  const analysisRef = useRef<HTMLDivElement>(null);
  const helpRef = useRef<HTMLDivElement>(null);

  // 工具导航项
  const toolNavItems = [
    { id: 'upload', name: '上传图片', ref: uploadRef },
    { id: 'preview', name: '图片预览', ref: previewRef, requireImage: true },
    { id: 'analysis', name: '颜色分析', ref: analysisRef, requireImage: true },
    { id: 'help', name: '使用说明', ref: helpRef }
  ];

  // 滚动到指定区域
  const scrollToSection = (id: string) => {
    setActiveTool(id);
    const item = toolNavItems.find(item => item.id === id);
    if (item && item.ref.current) {
      // 获取元素
      const element = item.ref.current;
      
      // 添加临时提示元素
      const indicator = document.createElement('div');
      indicator.className = 'section-indicator';
      element.appendChild(indicator);
      
      // 滚动到目标位置
      const navHeight = 80; // 导航栏高度估计值
      const elementPosition = element.getBoundingClientRect().top + window.pageYOffset;
      window.scrollTo({
        top: elementPosition - navHeight, 
        behavior: 'smooth'
      });
      
      // 3秒后移除指示器
      setTimeout(() => {
        element.removeChild(indicator);
      }, 3000);
    }
  };

  // 计算颜色关系
  const calculateColorRelations = (color: ColorInfo) => {
    const { r, g, b } = color;
    
    // 互补色
    const complementary = `rgb(${255-r},${255-g},${255-b})`;
    
    // 邻近色 (左右各30度)
    const analogous = [
      `rgb(${Math.round(r + 30)},${g},${b})`,
      `rgb(${Math.round(r - 30)},${g},${b})`
    ];
    
    // 三方色 (120度间隔)
    const triadic = [
      `rgb(${Math.round((r + 120) % 255)},${g},${b})`,
      `rgb(${Math.round((r + 240) % 255)},${g},${b})`
    ];
    
    // 四方色 (90度间隔)
    const tetradic = [
      `rgb(${Math.round((r + 90) % 255)},${g},${b})`,
      `rgb(${Math.round((r + 180) % 255)},${g},${b})`,
      `rgb(${Math.round((r + 270) % 255)},${g},${b})`
    ];
    
    // 明度渐变
    const shades = Array.from({ length: 5 }, (_, i) => {
      const factor = 1 - (i * 0.2);
      return `rgb(${Math.round(r * factor)},${Math.round(g * factor)},${Math.round(b * factor)})`;
    });
    
    // 饱和度渐变
    const tints = Array.from({ length: 5 }, (_, i) => {
      const factor = 1 + (i * 0.2);
      return `rgb(${Math.round(r + (255 - r) * (i * 0.2))},${Math.round(g + (255 - g) * (i * 0.2))},${Math.round(b + (255 - b) * (i * 0.2))})`;
    });

    return {
      complementary,
      analogous,
      triadic,
      tetradic,
      shades,
      tints
    };
  };

  const handleImageUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      if (!file.type.startsWith('image/')) {
        setError('请上传图片文件');
        return;
      }

      const reader = new FileReader();
      reader.onload = (e) => {
        setImage(e.target?.result as string);
        analyzeColors(e.target?.result as string);
      };
      reader.readAsDataURL(file);
    }
  };

  const analyzeColors = (imageUrl: string) => {
    setLoading(true);
    setError(null);

    const img = new Image();
    img.src = imageUrl;
    
    img.onload = () => {
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');
      
      if (!ctx) {
        setError('无法分析图片');
        setLoading(false);
        return;
      }

      canvas.width = img.width;
      canvas.height = img.height;
      ctx.drawImage(img, 0, 0);

      const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
      const data = imageData.data;

      const colorCounts: Record<string, number> = {};
      const totalPixels = data.length / 4;

      for (let i = 0; i < data.length; i += 4) {
        const r = data[i];
        const g = data[i + 1];
        const b = data[i + 2];
        const rgb = `${r},${g},${b}`;
        colorCounts[rgb] = (colorCounts[rgb] || 0) + 1;
      }

      const colorInfo: ColorInfo[] = Object.entries(colorCounts)
        .map(([rgb, count]) => {
          const [r, g, b] = rgb.split(',').map(Number);
          const hex = '#' + [r, g, b]
            .map(x => x.toString(16).padStart(2, '0'))
            .join('');
          return {
            rgb,
            hex,
            r,
            g,
            b,
            percentage: (count / totalPixels) * 100
          };
        })
        .sort((a, b) => b.percentage - a.percentage)
        .slice(0, 10);

      setColors(colorInfo);
      if (colorInfo.length > 0) {
        setSelectedColor(colorInfo[0]);
        setColorRelations(calculateColorRelations(colorInfo[0]));
      }
      setLoading(false);
    };

    img.onerror = () => {
      setError('图片加载失败');
      setLoading(false);
    };
  };

  const copyToClipboard = (text: string, type: 'rgb' | 'hex') => {
    navigator.clipboard.writeText(text);
    // 创建临时通知
    const notification = document.createElement('div');
    notification.className = 'fixed bottom-4 right-4 p-4 rounded-lg shadow-lg bg-primary text-white z-50';
    notification.innerHTML = `
      <h4 class="font-medium">复制成功</h4>
      <p class="text-sm">已复制${type === 'rgb' ? 'RGB' : 'HEX'}值到剪贴板</p>
    `;
    document.body.appendChild(notification);
    
    // 3秒后移除通知
    setTimeout(() => {
      notification.remove();
    }, 3000);
  };

  const downloadColors = () => {
    const text = colors
      .map(color => `RGB: ${color.rgb}\nHEX: ${color.hex}\n占比: ${color.percentage.toFixed(2)}%`)
      .join('\n\n');
    
    const blob = new Blob([text], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'color-palette.txt';
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  const handleColorSelect = (color: ColorInfo) => {
    setSelectedColor(color);
    setColorRelations(calculateColorRelations(color));
  };

  return (
    <div className="min-h-screen bg-background p-6">
      <style jsx global>{`
        @keyframes pulse {
          0% { opacity: 0.2; }
          50% { opacity: 0.8; }
          100% { opacity: 0.2; }
        }
        
        .section-indicator {
          position: absolute;
          left: 0;
          top: 0;
          width: 4px;
          height: 100%;
          background-color: #3b82f6; /* 蓝色 */
          animation: pulse 2s infinite;
          border-top-left-radius: 4px;
          border-bottom-left-radius: 4px;
        }
        
        .bg-card {
          position: relative;
        }
      `}</style>
      
      <div className="max-w-5xl mx-auto">
        {/* 返回按钮 */}
        <button 
          onClick={() => router.back()}
          className="mb-6 flex items-center gap-2 text-primary hover:underline"
        >
          <ArrowLeft size={16} />
          <span>返回工具列表</span>
        </button>

        {/* 工具标题 */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold">颜色提取</h1>
          <p className="text-muted-foreground mt-2">
            从图片中提取主色调，支持RGB格式显示和颜色板生成
          </p>
        </div>

        {/* 工具导航 */}
        <div className="mb-6 bg-card border border-border rounded-lg p-2 sticky top-4 z-10">
          <div className="flex flex-wrap gap-2">
            {toolNavItems.map(item => (
              <button
                key={item.id}
                onClick={() => scrollToSection(item.id)}
                disabled={item.requireImage && !image}
                className={`px-3 py-1.5 rounded-md text-sm font-medium transition-colors
                  ${activeTool === item.id 
                    ? 'bg-primary text-primary-foreground' 
                    : 'hover:bg-accent'}
                  ${item.requireImage && !image ? 'opacity-50 cursor-not-allowed' : ''}
                `}
              >
                {item.name}
              </button>
            ))}
          </div>
        </div>

        {/* 上传区域 */}
        <div ref={uploadRef} className="bg-card border border-border rounded-lg p-6 mb-6">
          <div className="flex flex-col items-center justify-center">
            <input
              type="file"
              ref={fileInputRef}
              onChange={handleImageUpload}
              accept="image/*"
              className="hidden"
            />
            <button
              onClick={() => fileInputRef.current?.click()}
              className="flex flex-col items-center gap-4 p-8 border-2 border-dashed border-border rounded-lg hover:border-primary transition-colors"
            >
              <Upload size={32} className="text-muted-foreground" />
              <div className="text-center">
                <p className="font-medium">点击上传图片</p>
                <p className="text-sm text-muted-foreground">支持 JPG、PNG 等格式</p>
              </div>
            </button>
          </div>
        </div>

        {/* 预览和结果 */}
        {image && (
          <div className="space-y-6 mb-6">
            {/* 图片预览 */}
            <div ref={previewRef} className="bg-card border border-border rounded-lg overflow-hidden">
              <div className="p-4 border-b border-border flex items-center justify-between">
                <h3 className="font-medium">图片预览</h3>
                <button 
                  onClick={() => fileInputRef.current?.click()}
                  className="p-2 rounded hover:bg-accent"
                  title="更换图片"
                >
                  <Upload size={16} />
                </button>
              </div>
              <div className="p-4">
                <img
                  src={image}
                  alt="上传的图片"
                  className="w-full h-auto max-h-[400px] object-contain mx-auto"
                />
              </div>
            </div>

            {/* 颜色分析结果 */}
            <div ref={analysisRef} className="bg-card border border-border rounded-lg">
              <div className="p-4 border-b border-border flex items-center justify-between">
                <h3 className="font-medium">颜色分析结果</h3>
                <div className="flex items-center gap-2">
                  <button 
                    onClick={() => analyzeColors(image)}
                    className="p-2 rounded hover:bg-accent"
                    title="重新分析"
                  >
                    <RefreshCw size={16} />
                  </button>
                  <button 
                    onClick={downloadColors}
                    className="p-2 rounded hover:bg-accent"
                    title="下载结果"
                  >
                    <Download size={16} />
                  </button>
                </div>
              </div>
              
              {/* 颜色列表 */}
              <div className="p-4 space-y-4">
                {colors.map((color, index) => (
                  <div 
                    key={index} 
                    className={`flex items-center gap-4 p-2 rounded-lg cursor-pointer transition-colors ${
                      selectedColor?.rgb === color.rgb ? 'bg-accent' : 'hover:bg-accent/50'
                    }`}
                    onClick={() => handleColorSelect(color)}
                  >
                    <div
                      className="w-12 h-12 rounded-lg"
                      style={{ backgroundColor: color.hex }}
                    />
                    <div className="flex-1">
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="font-medium">RGB: {color.rgb}</p>
                          <p className="text-sm text-muted-foreground">HEX: {color.hex}</p>
                        </div>
                        <div className="flex items-center gap-2">
                          <button
                            onClick={(e) => {
                              e.stopPropagation();
                              copyToClipboard(color.rgb, 'rgb');
                            }}
                            className="p-1 rounded hover:bg-accent"
                            title="复制RGB值"
                          >
                            <Copy size={16} />
                          </button>
                          <button
                            onClick={(e) => {
                              e.stopPropagation();
                              copyToClipboard(color.hex, 'hex');
                            }}
                            className="p-1 rounded hover:bg-accent"
                            title="复制HEX值"
                          >
                            <Copy size={16} />
                          </button>
                        </div>
                      </div>
                      <div className="mt-1">
                        <div className="h-1 bg-accent rounded-full">
                          <div
                            className="h-full rounded-full"
                            style={{
                              backgroundColor: color.hex,
                              width: `${color.percentage}%`
                            }}
                          />
                        </div>
                        <p className="text-xs text-muted-foreground mt-1">
                          占比: {color.percentage.toFixed(2)}%
                        </p>
                      </div>
                    </div>
                  </div>
                ))}
              </div>

              {/* 颜色关系展示 */}
              {selectedColor && colorRelations && (
                <div className="p-4 border-t border-border">
                  <div className="flex items-center gap-2 mb-4">
                    <button
                      onClick={() => setActiveTab('main')}
                      className={`px-3 py-1 rounded ${
                        activeTab === 'main' ? 'bg-primary text-primary-foreground' : 'hover:bg-accent'
                      }`}
                    >
                      主要颜色
                    </button>
                    <button
                      onClick={() => setActiveTab('relations')}
                      className={`px-3 py-1 rounded ${
                        activeTab === 'relations' ? 'bg-primary text-primary-foreground' : 'hover:bg-accent'
                      }`}
                    >
                      色彩关系
                    </button>
                  </div>

                  {activeTab === 'relations' && (
                    <div className="space-y-4">
                      {/* 互补色 */}
                      <div>
                        <h4 className="text-sm font-medium mb-2">互补色</h4>
                        <div className="flex gap-2">
                          <div
                            className="w-16 h-16 rounded-lg"
                            style={{ backgroundColor: selectedColor.hex }}
                          />
                          <div
                            className="w-16 h-16 rounded-lg"
                            style={{ backgroundColor: colorRelations.complementary }}
                          />
                        </div>
                      </div>

                      {/* 邻近色 */}
                      <div>
                        <h4 className="text-sm font-medium mb-2">邻近色</h4>
                        <div className="flex gap-2">
                          {colorRelations.analogous.map((color, index) => (
                            <div
                              key={index}
                              className="w-16 h-16 rounded-lg"
                              style={{ backgroundColor: color }}
                            />
                          ))}
                        </div>
                      </div>

                      {/* 三方色 */}
                      <div>
                        <h4 className="text-sm font-medium mb-2">三方色</h4>
                        <div className="flex gap-2">
                          {colorRelations.triadic.map((color, index) => (
                            <div
                              key={index}
                              className="w-16 h-16 rounded-lg"
                              style={{ backgroundColor: color }}
                            />
                          ))}
                        </div>
                      </div>

                      {/* 四方色 */}
                      <div>
                        <h4 className="text-sm font-medium mb-2">四方色</h4>
                        <div className="flex gap-2">
                          {colorRelations.tetradic.map((color, index) => (
                            <div
                              key={index}
                              className="w-16 h-16 rounded-lg"
                              style={{ backgroundColor: color }}
                            />
                          ))}
                        </div>
                      </div>

                      {/* 明度渐变 */}
                      <div>
                        <h4 className="text-sm font-medium mb-2">明度渐变</h4>
                        <div className="flex gap-1">
                          {colorRelations.shades.map((color, index) => (
                            <div
                              key={index}
                              className="h-8 flex-1 rounded"
                              style={{ backgroundColor: color }}
                            />
                          ))}
                        </div>
                      </div>

                      {/* 饱和度渐变 */}
                      <div>
                        <h4 className="text-sm font-medium mb-2">饱和度渐变</h4>
                        <div className="flex gap-1">
                          {colorRelations.tints.map((color, index) => (
                            <div
                              key={index}
                              className="h-8 flex-1 rounded"
                              style={{ backgroundColor: color }}
                            />
                          ))}
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              )}
            </div>
          </div>
        )}

        {/* 使用说明 */}
        <div ref={helpRef} className="mt-8">
          <h2 className="text-xl font-semibold mb-4">使用说明</h2>
          <div className="bg-card border border-border rounded-lg p-6">
            <ol className="list-decimal list-inside space-y-2">
              <li>点击上传区域或拖拽图片到上传区域</li>
              <li>等待系统分析图片中的主要颜色</li>
              <li>查看分析结果，包括RGB值、十六进制颜色代码和颜色占比</li>
              <li>点击颜色项可以查看该颜色的色彩关系（互补色、邻近色等）</li>
              <li>使用复制按钮可以快速复制颜色值</li>
              <li>可以下载完整的颜色分析报告</li>
            </ol>
            <div className="mt-4 p-4 bg-accent/50 rounded-lg">
              <p className="text-sm text-muted-foreground">
                <strong>提示：</strong> 为了获得最佳效果，建议上传清晰的图片，系统会自动提取前10个主要颜色。点击颜色项可以查看该颜色的各种色彩关系。
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
} 