'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { ArrowLeft, Copy, Check, RefreshCw } from 'lucide-react';

type ColorFormat = 'hex' | 'rgb' | 'hsl' | 'cmyk' | 'hsv';

interface ColorValues {
  hex: string;
  rgb: { r: number; g: number; b: number };
  hsl: { h: number; s: number; l: number };
  cmyk: { c: number; m: number; y: number; k: number };
  hsv: { h: number; s: number; v: number };
}

export default function ColorConverterPage() {
  const router = useRouter();
  const [inputFormat, setInputFormat] = useState<ColorFormat>('hex');
  const [inputValue, setInputValue] = useState<string>('');
  const [colorValues, setColorValues] = useState<ColorValues | null>(null);
  const [copySuccess, setCopySuccess] = useState<boolean>(false);
  const [error, setError] = useState<string>('');

  // 示例颜色
  const exampleColors = {
    hex: '#FF5733',
    rgb: 'rgb(255, 87, 51)',
    hsl: 'hsl(12, 100%, 60%)',
    cmyk: 'cmyk(0, 66, 80, 0)',
    hsv: 'hsv(12, 80%, 100%)'
  };

  // 加载示例
  const loadExample = () => {
    setInputValue(exampleColors[inputFormat]);
  };

  // 清空输入
  const clearInput = () => {
    setInputValue('');
    setColorValues(null);
    setError('');
  };

  // 处理输入变化
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setInputValue(e.target.value);
  };

  // 复制结果
  const copyResult = (format: ColorFormat) => {
    if (!colorValues) return;

    let textToCopy = '';
    switch (format) {
      case 'hex':
        textToCopy = colorValues.hex;
        break;
      case 'rgb':
        textToCopy = `rgb(${colorValues.rgb.r}, ${colorValues.rgb.g}, ${colorValues.rgb.b})`;
        break;
      case 'hsl':
        textToCopy = `hsl(${colorValues.hsl.h}, ${colorValues.hsl.s}%, ${colorValues.hsl.l}%)`;
        break;
      case 'cmyk':
        textToCopy = `cmyk(${colorValues.cmyk.c}%, ${colorValues.cmyk.m}%, ${colorValues.cmyk.y}%, ${colorValues.cmyk.k}%)`;
        break;
      case 'hsv':
        textToCopy = `hsv(${colorValues.hsv.h}, ${colorValues.hsv.s}%, ${colorValues.hsv.v}%)`;
        break;
    }

    navigator.clipboard.writeText(textToCopy)
      .then(() => {
        setCopySuccess(true);
        setTimeout(() => setCopySuccess(false), 2000);
      })
      .catch(err => {
        console.error('复制失败:', err);
      });
  };

  // 颜色转换函数
  const convertColor = (value: string, fromFormat: ColorFormat): ColorValues | null => {
    try {
      let r = 0, g = 0, b = 0;

      // 解析输入颜色
      switch (fromFormat) {
        case 'hex':
          const hex = value.replace('#', '');
          r = parseInt(hex.substring(0, 2), 16);
          g = parseInt(hex.substring(2, 4), 16);
          b = parseInt(hex.substring(4, 6), 16);
          break;

        case 'rgb':
          const rgbMatch = value.match(/rgb\((\d+),\s*(\d+),\s*(\d+)\)/);
          if (rgbMatch) {
            r = parseInt(rgbMatch[1]);
            g = parseInt(rgbMatch[2]);
            b = parseInt(rgbMatch[3]);
          }
          break;

        case 'hsl':
          const hslMatch = value.match(/hsl\((\d+),\s*(\d+)%,\s*(\d+)%\)/);
          if (hslMatch) {
            const h = parseInt(hslMatch[1]) / 360;
            const s = parseInt(hslMatch[2]) / 100;
            const l = parseInt(hslMatch[3]) / 100;
            
            const c = (1 - Math.abs(2 * l - 1)) * s;
            const x = c * (1 - Math.abs((h * 6) % 2 - 1));
            const m = l - c / 2;
            
            let r1 = 0, g1 = 0, b1 = 0;
            if (h < 1/6) { r1 = c; g1 = x; b1 = 0; }
            else if (h < 2/6) { r1 = x; g1 = c; b1 = 0; }
            else if (h < 3/6) { r1 = 0; g1 = c; b1 = x; }
            else if (h < 4/6) { r1 = 0; g1 = x; b1 = c; }
            else if (h < 5/6) { r1 = x; g1 = 0; b1 = c; }
            else { r1 = c; g1 = 0; b1 = x; }
            
            r = Math.round((r1 + m) * 255);
            g = Math.round((g1 + m) * 255);
            b = Math.round((b1 + m) * 255);
          }
          break;

        case 'cmyk':
          const cmykMatch = value.match(/cmyk\((\d+)%,\s*(\d+)%,\s*(\d+)%,\s*(\d+)%\)/);
          if (cmykMatch) {
            const c = parseInt(cmykMatch[1]) / 100;
            const m = parseInt(cmykMatch[2]) / 100;
            const y = parseInt(cmykMatch[3]) / 100;
            const k = parseInt(cmykMatch[4]) / 100;
            
            r = Math.round(255 * (1 - c) * (1 - k));
            g = Math.round(255 * (1 - m) * (1 - k));
            b = Math.round(255 * (1 - y) * (1 - k));
          }
          break;

        case 'hsv':
          const hsvMatch = value.match(/hsv\((\d+),\s*(\d+)%,\s*(\d+)%\)/);
          if (hsvMatch) {
            const h = parseInt(hsvMatch[1]) / 360;
            const s = parseInt(hsvMatch[2]) / 100;
            const v = parseInt(hsvMatch[3]) / 100;
            
            const c = v * s;
            const x = c * (1 - Math.abs((h * 6) % 2 - 1));
            const m = v - c;
            
            let r1 = 0, g1 = 0, b1 = 0;
            if (h < 1/6) { r1 = c; g1 = x; b1 = 0; }
            else if (h < 2/6) { r1 = x; g1 = c; b1 = 0; }
            else if (h < 3/6) { r1 = 0; g1 = c; b1 = x; }
            else if (h < 4/6) { r1 = 0; g1 = x; b1 = c; }
            else if (h < 5/6) { r1 = x; g1 = 0; b1 = c; }
            else { r1 = c; g1 = 0; b1 = x; }
            
            r = Math.round((r1 + m) * 255);
            g = Math.round((g1 + m) * 255);
            b = Math.round((b1 + m) * 255);
          }
          break;
      }

      // 计算其他格式
      const hex = '#' + [r, g, b].map(x => {
        const hex = x.toString(16);
        return hex.length === 1 ? '0' + hex : hex;
      }).join('');

      // RGB to HSL
      const r1 = r / 255;
      const g1 = g / 255;
      const b1 = b / 255;
      const max = Math.max(r1, g1, b1);
      const min = Math.min(r1, g1, b1);
      let h = 0, s = 0, l = (max + min) / 2;

      if (max !== min) {
        const d = max - min;
        s = l > 0.5 ? d / (2 - max - min) : d / (max + min);
        
        if (max === r1) {
          h = (g1 - b1) / d + (g1 < b1 ? 6 : 0);
        } else if (max === g1) {
          h = (b1 - r1) / d + 2;
        } else {
          h = (r1 - g1) / d + 4;
        }
        h /= 6;
      }

      h = Math.round(h * 360);
      s = Math.round(s * 100);
      l = Math.round(l * 100);

      // RGB to CMYK
      const k = 1 - Math.max(r1, g1, b1);
      const c = (1 - r1 - k) / (1 - k) || 0;
      const m = (1 - g1 - k) / (1 - k) || 0;
      const y = (1 - b1 - k) / (1 - k) || 0;

      // RGB to HSV
      const v = max;
      h = h / 360;
      s = max === 0 ? 0 : (max - min) / max;

      return {
        hex,
        rgb: { r, g, b },
        hsl: { h, s, l },
        cmyk: {
          c: Math.round(c * 100),
          m: Math.round(m * 100),
          y: Math.round(y * 100),
          k: Math.round(k * 100)
        },
        hsv: {
          h: Math.round(h * 360),
          s: Math.round(s * 100),
          v: Math.round(v * 100)
        }
      };
    } catch (error) {
      return null;
    }
  };

  // 转换处理
  useEffect(() => {
    if (!inputValue) {
      setColorValues(null);
      setError('');
      return;
    }

    const result = convertColor(inputValue, inputFormat);
    if (result) {
      setColorValues(result);
      setError('');
    } else {
      setError('无效的颜色格式');
      setColorValues(null);
    }
  }, [inputValue, inputFormat]);

  return (
    <div className="container mx-auto p-6">
      {/* 头部 */}
      <div className="flex items-center mb-6">
        <button 
          onClick={() => router.back()}
          className="p-2 mr-4 rounded-full hover:bg-gray-100 dark:hover:bg-gray-800"
        >
          <ArrowLeft size={24} />
        </button>
        <h1 className="text-2xl font-bold">颜色格式转换工具</h1>
      </div>

      {/* 工具说明 */}
      <div className="mb-8 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
        <h2 className="text-lg font-medium mb-2">工具说明</h2>
        <p className="text-gray-700 dark:text-gray-300">
          本工具支持在HEX、RGB、HSL、CMYK和HSV等不同颜色格式之间进行转换。
          输入任意格式的颜色值，即可获得其他格式的等效表示。所有转换都在浏览器本地完成。
        </p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* 左侧：输入区域 */}
        <div className="space-y-4">
          <div className="flex justify-between items-center mb-2">
            <h2 className="text-lg font-medium">输入颜色</h2>
            <div className="flex space-x-2">
              <button
                onClick={loadExample}
                className="text-xs p-1 px-2 rounded bg-gray-100 hover:bg-gray-200 dark:bg-gray-800 dark:hover:bg-gray-700"
              >
                加载示例
              </button>
              <button
                onClick={clearInput}
                className="text-xs p-1 px-2 rounded bg-gray-100 hover:bg-gray-200 dark:bg-gray-800 dark:hover:bg-gray-700 flex items-center"
              >
                <RefreshCw size={12} className="mr-1" />
                清空
              </button>
            </div>
          </div>

          {/* 格式选择 */}
          <div className="flex mb-4 bg-white dark:bg-gray-900 rounded-lg p-2 border border-gray-300 dark:border-gray-700">
            {(['hex', 'rgb', 'hsl', 'cmyk', 'hsv'] as ColorFormat[]).map(format => (
              <button
                key={format}
                onClick={() => setInputFormat(format)}
                className={`flex-1 py-2 rounded-md ${
                  inputFormat === format
                    ? 'bg-blue-500 text-white'
                    : 'bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-300'
                }`}
              >
                {format.toUpperCase()}
              </button>
            ))}
          </div>

          <div className="relative">
            <input
              type="text"
              value={inputValue}
              onChange={handleInputChange}
              placeholder={`输入${inputFormat.toUpperCase()}格式的颜色值...`}
              className="w-full p-4 border border-gray-300 dark:border-gray-700 rounded-lg bg-white dark:bg-gray-900 focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
            {error && (
              <div className="absolute right-2 top-2 text-red-500 text-sm">
                {error}
              </div>
            )}
          </div>

          {/* 颜色预览 */}
          {colorValues && (
            <div className="mt-4 p-4 border border-gray-300 dark:border-gray-700 rounded-lg">
              <h3 className="text-base font-medium mb-2">颜色预览</h3>
              <div
                className="w-full h-32 rounded-lg"
                style={{ backgroundColor: colorValues.hex }}
              />
            </div>
          )}
        </div>

        {/* 右侧：输出结果 */}
        <div className="space-y-4">
          <h2 className="text-lg font-medium">转换结果</h2>

          {colorValues ? (
            <div className="space-y-4">
              {/* HEX */}
              <div className="bg-white dark:bg-gray-900 border border-gray-300 dark:border-gray-700 rounded-lg p-4">
                <div className="flex justify-between items-center mb-2">
                  <h3 className="text-base font-medium">HEX</h3>
                  <button
                    onClick={() => copyResult('hex')}
                    className="text-xs p-1 px-2 rounded bg-gray-100 hover:bg-gray-200 dark:bg-gray-800 dark:hover:bg-gray-700 flex items-center"
                  >
                    {copySuccess ? <Check size={12} className="mr-1" /> : <Copy size={12} className="mr-1" />}
                    复制
                  </button>
                </div>
                <div className="font-mono text-sm">{colorValues.hex}</div>
              </div>

              {/* RGB */}
              <div className="bg-white dark:bg-gray-900 border border-gray-300 dark:border-gray-700 rounded-lg p-4">
                <div className="flex justify-between items-center mb-2">
                  <h3 className="text-base font-medium">RGB</h3>
                  <button
                    onClick={() => copyResult('rgb')}
                    className="text-xs p-1 px-2 rounded bg-gray-100 hover:bg-gray-200 dark:bg-gray-800 dark:hover:bg-gray-700 flex items-center"
                  >
                    {copySuccess ? <Check size={12} className="mr-1" /> : <Copy size={12} className="mr-1" />}
                    复制
                  </button>
                </div>
                <div className="font-mono text-sm">
                  rgb({colorValues.rgb.r}, {colorValues.rgb.g}, {colorValues.rgb.b})
                </div>
              </div>

              {/* HSL */}
              <div className="bg-white dark:bg-gray-900 border border-gray-300 dark:border-gray-700 rounded-lg p-4">
                <div className="flex justify-between items-center mb-2">
                  <h3 className="text-base font-medium">HSL</h3>
                  <button
                    onClick={() => copyResult('hsl')}
                    className="text-xs p-1 px-2 rounded bg-gray-100 hover:bg-gray-200 dark:bg-gray-800 dark:hover:bg-gray-700 flex items-center"
                  >
                    {copySuccess ? <Check size={12} className="mr-1" /> : <Copy size={12} className="mr-1" />}
                    复制
                  </button>
                </div>
                <div className="font-mono text-sm">
                  hsl({colorValues.hsl.h}, {colorValues.hsl.s}%, {colorValues.hsl.l}%)
                </div>
              </div>

              {/* CMYK */}
              <div className="bg-white dark:bg-gray-900 border border-gray-300 dark:border-gray-700 rounded-lg p-4">
                <div className="flex justify-between items-center mb-2">
                  <h3 className="text-base font-medium">CMYK</h3>
                  <button
                    onClick={() => copyResult('cmyk')}
                    className="text-xs p-1 px-2 rounded bg-gray-100 hover:bg-gray-200 dark:bg-gray-800 dark:hover:bg-gray-700 flex items-center"
                  >
                    {copySuccess ? <Check size={12} className="mr-1" /> : <Copy size={12} className="mr-1" />}
                    复制
                  </button>
                </div>
                <div className="font-mono text-sm">
                  cmyk({colorValues.cmyk.c}%, {colorValues.cmyk.m}%, {colorValues.cmyk.y}%, {colorValues.cmyk.k}%)
                </div>
              </div>

              {/* HSV */}
              <div className="bg-white dark:bg-gray-900 border border-gray-300 dark:border-gray-700 rounded-lg p-4">
                <div className="flex justify-between items-center mb-2">
                  <h3 className="text-base font-medium">HSV</h3>
                  <button
                    onClick={() => copyResult('hsv')}
                    className="text-xs p-1 px-2 rounded bg-gray-100 hover:bg-gray-200 dark:bg-gray-800 dark:hover:bg-gray-700 flex items-center"
                  >
                    {copySuccess ? <Check size={12} className="mr-1" /> : <Copy size={12} className="mr-1" />}
                    复制
                  </button>
                </div>
                <div className="font-mono text-sm">
                  hsv({colorValues.hsv.h}, {colorValues.hsv.s}%, {colorValues.hsv.v}%)
                </div>
              </div>
            </div>
          ) : (
            <div className="bg-white dark:bg-gray-900 border border-gray-300 dark:border-gray-700 rounded-lg p-4">
              <p className="text-gray-500 dark:text-gray-400">转换结果将显示在这里...</p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
} 