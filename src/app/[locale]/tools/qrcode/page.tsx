'use client';

import { useState, useRef } from 'react';
import { useRouter } from 'next/navigation';
import { ArrowLeft, Download, RefreshCw } from 'lucide-react';
import { QRCodeCanvas } from 'qrcode.react';
import { useLocale, useTranslations } from 'next-intl';

export default function QRCodePage() {
  const locale = useLocale();
  const t = useTranslations('tools.qrcode');
  const router = useRouter();
  const [text, setText] = useState<string>('');
  const [size, setSize] = useState<number>(256);
  const [fgColor, setFgColor] = useState<string>('#000000');
  const [bgColor, setBgColor] = useState<string>('#FFFFFF');
  const [includeMargin, setIncludeMargin] = useState<boolean>(true);
  const qrRef = useRef<HTMLDivElement>(null);

  // 示例文本
  const exampleText = 'https://wenhaofree.com';

  // 加载示例
  const loadExample = () => {
    setText(exampleText);
  };

  // 清空输入
  const clearInput = () => {
    setText('');
  };

  // 处理文本输入变化
  const handleTextChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setText(e.target.value);
  };

  // 下载二维码
  const downloadQRCode = () => {
    if (!qrRef.current) return;

    const canvas = qrRef.current.querySelector('canvas');
    if (!canvas) return;

    const url = canvas.toDataURL('image/png');
    const link = document.createElement('a');
    link.href = url;
    link.download = 'qrcode.png';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  return (
    <div className="container mx-auto p-6">
      {/* 头部 */}
      <div className="flex items-center mb-6">
        <button 
          onClick={() => router.back()}
          className="p-2 mr-4 rounded-full hover:bg-gray-100 dark:hover:bg-gray-800"
        >
          <ArrowLeft size={24} />
        </button>
        <h1 className="text-2xl font-bold">{t('title')}</h1>
      </div>

      {/* 工具说明 */}
      <div className="mb-8 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
        <h2 className="text-lg font-medium mb-2">{t('toolDescription.title')}</h2>
        <p className="text-gray-700 dark:text-gray-300">
          {t('toolDescription.content')}
        </p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* 左侧：配置区域 */}
        <div className="space-y-4">
          <div className="bg-white dark:bg-gray-900 border border-gray-300 dark:border-gray-700 rounded-lg p-4">
            <h2 className="text-lg font-medium mb-4">{t('configOptions.title')}</h2>

            {/* 文本输入 */}
            <div className="mb-4">
              <label className="block text-sm font-medium mb-2">{t('configOptions.inputText.label')}</label>
              <textarea
                value={text}
                onChange={handleTextChange}
                placeholder={t('configOptions.inputText.placeholder')}
                className="w-full h-32 p-2 border border-gray-300 dark:border-gray-700 rounded-md bg-white dark:bg-gray-900 focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>

            {/* 大小设置 */}
            <div className="mb-4">
              <label className="block text-sm font-medium mb-2">{t('configOptions.size.label')}</label>
              <input
                type="range"
                min="128"
                max="512"
                step="32"
                value={size}
                onChange={(e) => setSize(parseInt(e.target.value))}
                className="w-full"
              />
              <div className="text-sm text-gray-500 dark:text-gray-400 mt-1">
                {size} x {size} {t('configOptions.size.pixels')}
              </div>
            </div>

            {/* 颜色设置 */}
            <div className="grid grid-cols-2 gap-4 mb-4">
              <div>
                <label className="block text-sm font-medium mb-2">{t('configOptions.color.foreground')}</label>
                <input
                  type="color"
                  value={fgColor}
                  onChange={(e) => setFgColor(e.target.value)}
                  className="w-full h-10 rounded-md cursor-pointer"
                />
              </div>
              <div>
                <label className="block text-sm font-medium mb-2">{t('configOptions.color.background')}</label>
                <input
                  type="color"
                  value={bgColor}
                  onChange={(e) => setBgColor(e.target.value)}
                  className="w-full h-10 rounded-md cursor-pointer"
                />
              </div>
            </div>

            {/* 边距设置 */}
            <div className="mb-4">
              <label className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  checked={includeMargin}
                  onChange={(e) => setIncludeMargin(e.target.checked)}
                  className="rounded border-gray-300 dark:border-gray-700 text-blue-500 focus:ring-blue-500"
                />
                <span className="text-sm font-medium">{t('configOptions.includeMargin')}</span>
              </label>
            </div>

            {/* 操作按钮 */}
            <div className="flex space-x-2">
              <button
                onClick={loadExample}
                className="flex-1 py-2 bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-300 rounded-md hover:bg-gray-200 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-gray-500"
              >
                {t('buttons.loadExample')}
              </button>
              <button
                onClick={clearInput}
                className="flex-1 py-2 bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-300 rounded-md hover:bg-gray-200 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-gray-500 flex items-center justify-center"
              >
                <RefreshCw size={16} className="mr-1" />
                {t('buttons.clear')}
              </button>
            </div>
          </div>
        </div>

        {/* 右侧：预览区域 */}
        <div className="space-y-4">
          <div className="flex justify-between items-center mb-2">
            <h2 className="text-lg font-medium">{t('preview.title')}</h2>
            <button
              onClick={downloadQRCode}
              className="text-xs p-1 px-2 rounded bg-gray-100 hover:bg-gray-200 dark:bg-gray-800 dark:hover:bg-gray-700 flex items-center"
              disabled={!text}
            >
              <Download size={12} className="mr-1" />
              {t('buttons.download')}
            </button>
          </div>

          <div className="bg-white dark:bg-gray-900 border border-gray-300 dark:border-gray-700 rounded-lg p-4">
            <div ref={qrRef} className="flex justify-center items-center min-h-[300px]">
              {text ? (
                <QRCodeCanvas
                  value={text}
                  size={size}
                  fgColor={fgColor}
                  bgColor={bgColor}
                  includeMargin={includeMargin}
                />
              ) : (
                <p className="text-gray-500 dark:text-gray-400">{t('preview.placeholder')}</p>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
} 