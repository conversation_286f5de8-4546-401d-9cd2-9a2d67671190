'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { ArrowLeft, Code, Copy, Download, Trash, Upload, Check, RefreshCw, Settings } from 'lucide-react';

type CodeType = 'javascript' | 'css' | 'html';

export default function CodeCompressPage() {
  const [codeInput, setCodeInput] = useState<string>('');
  const [compressedCode, setCompressedCode] = useState<string>('');
  const [error, setError] = useState<string | null>(null);
  const [codeType, setCodeType] = useState<CodeType>('javascript');
  const [copySuccess, setCopySuccess] = useState(false);
  const [loading, setLoading] = useState(false);
  const router = useRouter();

  // 示例代码数据
  const exampleCode = {
    javascript: `// 这是一个JavaScript示例代码
function calculateSum(a, b) {
  // 计算两个数的和
  const sum = a + b;
  
  // 返回结果
  return sum;
}

// 创建一个数组
const numbers = [1, 2, 3, 4, 5];

// 使用map方法
const doubled = numbers.map(function(number) {
  return number * 2;
});

console.log("原始数组:", numbers);
console.log("加倍后:", doubled);`,

    css: `/* 这是一个CSS示例代码 */
body {
  font-family: Arial, sans-serif;
  line-height: 1.6;
  color: #333;
  background-color: #f4f4f4;
  margin: 0;
  padding: 20px;
}

.container {
  max-width: 1100px;
  margin: 0 auto;
  overflow: hidden;
  padding: 0 20px;
}

.header {
  background-color: #333;
  color: #fff;
  padding: 20px;
  text-align: center;
}

.button {
  display: inline-block;
  background: #333;
  color: #fff;
  padding: 10px 20px;
  border: none;
  cursor: pointer;
  border-radius: 5px;
}`,

    html: `<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>HTML示例</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      line-height: 1.6;
    }
    .container {
      width: 80%;
      margin: 0 auto;
    }
  </style>
</head>
<body>
  <div class="container">
    <header>
      <h1>HTML示例代码</h1>
      <p>这是一个简单的HTML示例。</p>
    </header>
    
    <main>
      <section>
        <h2>第一部分</h2>
        <p>这是内容的第一部分。</p>
      </section>
      
      <section>
        <h2>第二部分</h2>
        <p>这是内容的第二部分。</p>
      </section>
    </main>
    
    <footer>
      <p>&copy; 2023 示例网站</p>
    </footer>
  </div>
</body>
</html>`
  };

  // 加载示例数据
  const loadExample = () => {
    setCodeInput(exampleCode[codeType]);
  };

  // 清空输入
  const clearInput = () => {
    setCodeInput('');
    setCompressedCode('');
    setError(null);
  };

  // 复制到剪贴板
  const copyToClipboard = () => {
    if (compressedCode) {
      navigator.clipboard.writeText(compressedCode)
        .then(() => {
          setCopySuccess(true);
          setTimeout(() => setCopySuccess(false), 2000);
        })
        .catch(err => {
          console.error('复制失败:', err);
        });
    }
  };

  // 下载压缩后的代码
  const downloadCode = () => {
    if (!compressedCode) return;
    
    const fileExtensions = {
      javascript: 'js',
      css: 'css',
      html: 'html'
    };
    
    const blob = new Blob([compressedCode], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `compressed.${fileExtensions[codeType]}`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  // 处理文件上传
  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    // 根据文件扩展名自动选择代码类型
    const fileExt = file.name.split('.').pop()?.toLowerCase();
    if (fileExt === 'js') setCodeType('javascript');
    else if (fileExt === 'css') setCodeType('css');
    else if (fileExt === 'html' || fileExt === 'htm') setCodeType('html');

    const reader = new FileReader();
    reader.onload = (e) => {
      const content = e.target?.result as string;
      setCodeInput(content);
    };
    reader.readAsText(file);
  };

  // 压缩JavaScript代码
  const compressJavaScript = (code: string): string => {
    try {
      // 移除单行注释
      let result = code.replace(/\/\/.*$/gm, '');
      
      // 移除多行注释
      result = result.replace(/\/\*[\s\S]*?\*\//g, '');
      
      // 移除多余空白
      result = result.replace(/\s+/g, ' ');
      
      // 移除行首行尾空白
      result = result.replace(/^\s+|\s+$/gm, '');
      
      // 删除不必要的空格
      result = result.replace(/\s*([=+\-*\/%&|^!<>?:;,.()])\s*/g, '$1');
      
      // 修复删除空格可能导致的语法错误
      result = result.replace(/(\w+)\s+\(/g, '$1(');
      
      // 为每个语句添加分号（如果没有）
      result = result.replace(/([^;{})]\n)/g, '$1;');
      
      // 合并多个空行
      result = result.replace(/\n+/g, '\n');
      
      return result;
    } catch (err) {
      console.error('JavaScript压缩错误:', err);
      throw new Error('JavaScript压缩失败');
    }
  };

  // 压缩CSS代码
  const compressCSS = (code: string): string => {
    try {
      // 移除注释
      let result = code.replace(/\/\*[\s\S]*?\*\//g, '');
      
      // 移除多余空白
      result = result.replace(/\s+/g, ' ');
      
      // 移除行首行尾空白
      result = result.replace(/^\s+|\s+$/gm, '');
      
      // 删除不必要的空格
      result = result.replace(/\s*([{};:,])\s*/g, '$1');
      
      // 合并多个空行
      result = result.replace(/\n+/g, '');
      
      return result;
    } catch (err) {
      console.error('CSS压缩错误:', err);
      throw new Error('CSS压缩失败');
    }
  };

  // 压缩HTML代码
  const compressHTML = (code: string): string => {
    try {
      // 移除HTML注释
      let result = code.replace(/<!--[\s\S]*?-->/g, '');
      
      // 移除多余空白
      result = result.replace(/\s+/g, ' ');
      
      // 保留script和pre标签内的内容
      const scriptMatches: string[] = [];
      const preMatches: string[] = [];
      
      // 提取并保存脚本标签
      result = result.replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, match => {
        scriptMatches.push(match);
        return `___SCRIPT_${scriptMatches.length - 1}___`;
      });
      
      // 提取并保存pre标签
      result = result.replace(/<pre\b[^<]*(?:(?!<\/pre>)<[^<]*)*<\/pre>/gi, match => {
        preMatches.push(match);
        return `___PRE_${preMatches.length - 1}___`;
      });
      
      // 删除标签之间的多余空白
      result = result.replace(/>\s+</g, '><');
      
      // 删除标签属性周围的多余空白
      result = result.replace(/\s*([=])\s*/g, '$1');
      
      // 去除行首行尾空白
      result = result.replace(/^\s+|\s+$/gm, '');
      
      // 还原script和pre标签内容
      scriptMatches.forEach((match, i) => {
        result = result.replace(`___SCRIPT_${i}___`, match);
      });
      
      preMatches.forEach((match, i) => {
        result = result.replace(`___PRE_${i}___`, match);
      });
      
      return result;
    } catch (err) {
      console.error('HTML压缩错误:', err);
      throw new Error('HTML压缩失败');
    }
  };

  // 压缩代码
  const compressCode = () => {
    if (!codeInput.trim()) {
      setError('请输入代码');
      return;
    }

    setLoading(true);
    setError(null);

    try {
      let result;
      switch (codeType) {
        case 'javascript':
          result = compressJavaScript(codeInput);
          break;
        case 'css':
          result = compressCSS(codeInput);
          break;
        case 'html':
          result = compressHTML(codeInput);
          break;
      }
      setCompressedCode(result);
    } catch (err) {
      setError(`代码压缩错误: ${err instanceof Error ? err.message : '未知错误'}`);
      setCompressedCode('');
    } finally {
      setLoading(false);
    }
  };

  // 当输入变化时自动压缩
  useEffect(() => {
    if (codeInput.trim()) {
      const timer = setTimeout(() => {
        compressCode();
      }, 500);
      return () => clearTimeout(timer);
    }
  }, [codeInput, codeType]);

  return (
    <div className="container mx-auto p-6">
      {/* 头部 */}
      <div className="flex items-center mb-6">
        <button 
          onClick={() => router.back()}
          className="p-2 mr-4 rounded-full hover:bg-gray-100 dark:hover:bg-gray-800"
        >
          <ArrowLeft size={24} />
        </button>
        <h1 className="text-2xl font-bold">代码压缩</h1>
      </div>

      {/* 工具说明 */}
      <div className="mb-8 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
        <h2 className="text-lg font-medium mb-2">工具说明</h2>
        <p className="text-gray-700 dark:text-gray-300">
          本工具可以帮助您压缩JavaScript、CSS和HTML代码，减小文件体积，提高加载速度。
          压缩过程会移除注释、多余的空白和换行符等，但不会改变代码功能。所有处理均在浏览器本地完成，不会上传至服务器。
        </p>
      </div>

      {/* 代码类型设置 */}
      <div className="mb-6 p-4 border border-gray-200 dark:border-gray-700 rounded-lg">
        <div className="flex items-center mb-2">
          <Settings size={18} className="mr-2" />
          <h2 className="text-lg font-medium">代码类型</h2>
        </div>
        
        <div className="flex space-x-2">
          <button
            onClick={() => setCodeType('javascript')}
            className={`px-4 py-2 rounded ${
              codeType === 'javascript' 
                ? 'bg-blue-500 text-white' 
                : 'bg-gray-100 hover:bg-gray-200 dark:bg-gray-800 dark:hover:bg-gray-700'
            }`}
          >
            JavaScript
          </button>
          <button
            onClick={() => setCodeType('css')}
            className={`px-4 py-2 rounded ${
              codeType === 'css' 
                ? 'bg-blue-500 text-white' 
                : 'bg-gray-100 hover:bg-gray-200 dark:bg-gray-800 dark:hover:bg-gray-700'
            }`}
          >
            CSS
          </button>
          <button
            onClick={() => setCodeType('html')}
            className={`px-4 py-2 rounded ${
              codeType === 'html' 
                ? 'bg-blue-500 text-white' 
                : 'bg-gray-100 hover:bg-gray-200 dark:bg-gray-800 dark:hover:bg-gray-700'
            }`}
          >
            HTML
          </button>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* 输入区域 */}
        <div className="space-y-4">
          <div className="flex justify-between items-center">
            <h2 className="text-lg font-medium">原始代码</h2>
            <div className="flex space-x-2">
              <button
                onClick={loadExample}
                className="text-xs p-1 px-2 rounded bg-gray-100 hover:bg-gray-200 dark:bg-gray-800 dark:hover:bg-gray-700"
              >
                加载示例
              </button>
              <button
                onClick={clearInput}
                className="text-xs p-1 px-2 rounded bg-gray-100 hover:bg-gray-200 dark:bg-gray-800 dark:hover:bg-gray-700 flex items-center"
              >
                <Trash size={12} className="mr-1" />
                清空
              </button>
              <label className="text-xs p-1 px-2 rounded bg-gray-100 hover:bg-gray-200 dark:bg-gray-800 dark:hover:bg-gray-700 flex items-center cursor-pointer">
                <Upload size={12} className="mr-1" />
                上传
                <input
                  type="file"
                  accept=".js,.css,.html,.htm"
                  onChange={handleFileUpload}
                  className="hidden"
                />
              </label>
            </div>
          </div>
          
          <textarea
            value={codeInput}
            onChange={(e) => setCodeInput(e.target.value)}
            placeholder={`在此粘贴或输入${codeType === 'javascript' ? 'JavaScript' : codeType === 'css' ? 'CSS' : 'HTML'}代码...`}
            className="w-full h-[400px] p-4 rounded-lg border border-gray-300 dark:border-gray-700 bg-white dark:bg-gray-900 font-mono text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
        </div>

        {/* 输出区域 */}
        <div className="space-y-4">
          <div className="flex justify-between items-center">
            <h2 className="text-lg font-medium">压缩结果</h2>
            <div className="flex space-x-2">
              <button
                onClick={compressCode}
                disabled={loading}
                className="text-xs p-1 px-2 rounded bg-blue-500 hover:bg-blue-600 text-white flex items-center"
              >
                {loading ? <RefreshCw size={12} className="animate-spin mr-1" /> : <Code size={12} className="mr-1" />}
                压缩
              </button>
              <button
                onClick={copyToClipboard}
                disabled={!compressedCode}
                className={`text-xs p-1 px-2 rounded ${
                  copySuccess 
                    ? 'bg-green-500 text-white' 
                    : 'bg-gray-100 hover:bg-gray-200 dark:bg-gray-800 dark:hover:bg-gray-700'
                } flex items-center`}
              >
                {copySuccess ? <Check size={12} className="mr-1" /> : <Copy size={12} className="mr-1" />}
                {copySuccess ? '已复制' : '复制'}
              </button>
              <button
                onClick={downloadCode}
                disabled={!compressedCode}
                className="text-xs p-1 px-2 rounded bg-gray-100 hover:bg-gray-200 dark:bg-gray-800 dark:hover:bg-gray-700 flex items-center"
              >
                <Download size={12} className="mr-1" />
                下载
              </button>
            </div>
          </div>
          
          {error ? (
            <div className="p-4 rounded-lg bg-red-50 dark:bg-red-900/20 text-red-600 dark:text-red-400 font-mono text-sm">
              {error}
            </div>
          ) : compressedCode ? (
            <pre className="w-full h-[400px] p-4 rounded-lg border border-gray-300 dark:border-gray-700 bg-white dark:bg-gray-900 font-mono text-sm overflow-auto">
              {compressedCode}
            </pre>
          ) : (
            <div className="w-full h-[400px] p-4 rounded-lg border border-gray-300 dark:border-gray-700 bg-white dark:bg-gray-900 flex items-center justify-center text-gray-400">
              <p>压缩后的代码将显示在这里</p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
} 