'use client';

import { useState, useCallback, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { ArrowLeft, Check, Copy, RefreshCw, Sliders } from 'lucide-react';
import { v4 as uuidv4 } from 'uuid';

type GeneratorType = 'password' | 'number' | 'uuid' | 'string';

interface PasswordOptions {
  length: number;
  includeUppercase: boolean;
  includeLowercase: boolean;
  includeNumbers: boolean;
  includeSymbols: boolean;
}

interface NumberOptions {
  min: number;
  max: number;
  count: number;
  unique: boolean;
  sorted: boolean;
}

interface StringOptions {
  length: number;
  charset: 'alphanumeric' | 'alphabetic' | 'custom';
  customCharset: string;
}

export default function RandomGeneratorPage() {
  const router = useRouter();
  
  // 当前选择的生成器类型
  const [generatorType, setGeneratorType] = useState<GeneratorType>('password');
  
  // 生成结果
  const [result, setResult] = useState<string>('');
  
  // 复制状态
  const [copied, setCopied] = useState(false);
  
  // 密码生成器选项
  const [passwordOptions, setPasswordOptions] = useState<PasswordOptions>({
    length: 12,
    includeUppercase: true,
    includeLowercase: true,
    includeNumbers: true,
    includeSymbols: true
  });
  
  // 数字生成器选项
  const [numberOptions, setNumberOptions] = useState<NumberOptions>({
    min: 1,
    max: 100,
    count: 5,
    unique: true,
    sorted: false
  });
  
  // UUID生成器选项
  const [uuidCount, setUuidCount] = useState(1);
  
  // 字符串生成器选项
  const [stringOptions, setStringOptions] = useState<StringOptions>({
    length: 10,
    charset: 'alphanumeric',
    customCharset: ''
  });

  // 生成随机密码
  const generatePassword = useCallback(() => {
    const { length, includeUppercase, includeLowercase, includeNumbers, includeSymbols } = passwordOptions;
    
    // 验证选项
    if (!includeUppercase && !includeLowercase && !includeNumbers && !includeSymbols) {
      setResult('错误：至少需要选择一种字符类型');
      return;
    }
    
    // 字符集
    const uppercaseChars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
    const lowercaseChars = 'abcdefghijklmnopqrstuvwxyz';
    const numberChars = '0123456789';
    const symbolChars = '!@#$%^&*()_+-=[]{}|;:,.<>?';
    
    // 合并选中的字符集
    let charset = '';
    if (includeUppercase) charset += uppercaseChars;
    if (includeLowercase) charset += lowercaseChars;
    if (includeNumbers) charset += numberChars;
    if (includeSymbols) charset += symbolChars;
    
    // 生成密码
    let password = '';
    
    // 确保至少包含每种选中的字符类型
    if (includeUppercase) {
      password += uppercaseChars.charAt(Math.floor(Math.random() * uppercaseChars.length));
    }
    if (includeLowercase) {
      password += lowercaseChars.charAt(Math.floor(Math.random() * lowercaseChars.length));
    }
    if (includeNumbers) {
      password += numberChars.charAt(Math.floor(Math.random() * numberChars.length));
    }
    if (includeSymbols) {
      password += symbolChars.charAt(Math.floor(Math.random() * symbolChars.length));
    }
    
    // 填充剩余长度
    while (password.length < length) {
      const randomChar = charset.charAt(Math.floor(Math.random() * charset.length));
      password += randomChar;
    }
    
    // 打乱字符顺序
    password = password.split('').sort(() => 0.5 - Math.random()).join('');
    
    // 截取所需长度
    password = password.substring(0, length);
    
    setResult(password);
  }, [passwordOptions]);

  // 生成随机数字
  const generateNumbers = useCallback(() => {
    const { min, max, count, unique, sorted } = numberOptions;
    
    // 验证选项
    if (min > max) {
      setResult('错误：最小值不能大于最大值');
      return;
    }
    
    if (unique && (max - min + 1) < count) {
      setResult(`错误：在范围 ${min}-${max} 内无法生成 ${count} 个唯一数字`);
      return;
    }
    
    // 生成数字
    let numbers: number[] = [];
    
    if (unique) {
      // 生成唯一数字
      const availableNumbers = Array.from(
        { length: max - min + 1 },
        (_, i) => min + i
      );
      
      // 随机打乱可用数字
      for (let i = availableNumbers.length - 1; i > 0; i--) {
        const j = Math.floor(Math.random() * (i + 1));
        [availableNumbers[i], availableNumbers[j]] = [availableNumbers[j], availableNumbers[i]];
      }
      
      // 取前count个
      numbers = availableNumbers.slice(0, count);
    } else {
      // 生成可重复的随机数
      for (let i = 0; i < count; i++) {
        const randomNum = Math.floor(Math.random() * (max - min + 1)) + min;
        numbers.push(randomNum);
      }
    }
    
    // 排序（如果需要）
    if (sorted) {
      numbers.sort((a, b) => a - b);
    }
    
    setResult(numbers.join(', '));
  }, [numberOptions]);

  // 生成UUID
  const generateUuids = useCallback(() => {
    const uuids = Array.from({ length: uuidCount }, () => uuidv4());
    setResult(uuids.join('\n'));
  }, [uuidCount]);

  // 生成随机字符串
  const generateString = useCallback(() => {
    const { length, charset, customCharset } = stringOptions;
    
    let chars = '';
    
    switch (charset) {
      case 'alphanumeric':
        chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
        break;
      case 'alphabetic':
        chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz';
        break;
      case 'custom':
        chars = customCharset;
        break;
    }
    
    if (!chars) {
      setResult('错误：请提供有效的字符集');
      return;
    }
    
    let randomString = '';
    for (let i = 0; i < length; i++) {
      randomString += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    
    setResult(randomString);
  }, [stringOptions]);

  // 生成随机内容
  const generateRandom = useCallback(() => {
    switch (generatorType) {
      case 'password':
        generatePassword();
        break;
      case 'number':
        generateNumbers();
        break;
      case 'uuid':
        generateUuids();
        break;
      case 'string':
        generateString();
        break;
    }
  }, [generatorType, generatePassword, generateNumbers, generateUuids, generateString]);

  // 复制结果到剪贴板
  const copyToClipboard = useCallback(() => {
    if (!result) return;
    
    navigator.clipboard.writeText(result)
      .then(() => {
        setCopied(true);
        setTimeout(() => setCopied(false), 2000);
      })
      .catch(err => {
        console.error('复制失败:', err);
      });
  }, [result]);

  // 组件挂载时自动生成一次
  useEffect(() => {
    generateRandom();
  }, []);

  // 渲染密码选项
  const renderPasswordOptions = () => (
    <div className="space-y-4">
      <div>
        <label className="block text-sm font-medium mb-1">密码长度: {passwordOptions.length}</label>
        <input
          type="range"
          min="4"
          max="64"
          value={passwordOptions.length}
          onChange={(e) => setPasswordOptions({ ...passwordOptions, length: parseInt(e.target.value) })}
          className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer dark:bg-gray-700"
        />
        <div className="flex justify-between text-xs text-gray-500">
          <span>4</span>
          <span>64</span>
        </div>
      </div>
      
      <div className="space-y-2">
        <label className="block text-sm font-medium">字符类型</label>
        
        <div className="flex items-center">
          <input
            type="checkbox"
            id="includeUppercase"
            checked={passwordOptions.includeUppercase}
            onChange={(e) => setPasswordOptions({ ...passwordOptions, includeUppercase: e.target.checked })}
            className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 dark:bg-gray-700 dark:border-gray-600"
          />
          <label htmlFor="includeUppercase" className="ml-2 text-sm">
            大写字母 (A-Z)
          </label>
        </div>
        
        <div className="flex items-center">
          <input
            type="checkbox"
            id="includeLowercase"
            checked={passwordOptions.includeLowercase}
            onChange={(e) => setPasswordOptions({ ...passwordOptions, includeLowercase: e.target.checked })}
            className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 dark:bg-gray-700 dark:border-gray-600"
          />
          <label htmlFor="includeLowercase" className="ml-2 text-sm">
            小写字母 (a-z)
          </label>
        </div>
        
        <div className="flex items-center">
          <input
            type="checkbox"
            id="includeNumbers"
            checked={passwordOptions.includeNumbers}
            onChange={(e) => setPasswordOptions({ ...passwordOptions, includeNumbers: e.target.checked })}
            className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 dark:bg-gray-700 dark:border-gray-600"
          />
          <label htmlFor="includeNumbers" className="ml-2 text-sm">
            数字 (0-9)
          </label>
        </div>
        
        <div className="flex items-center">
          <input
            type="checkbox"
            id="includeSymbols"
            checked={passwordOptions.includeSymbols}
            onChange={(e) => setPasswordOptions({ ...passwordOptions, includeSymbols: e.target.checked })}
            className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 dark:bg-gray-700 dark:border-gray-600"
          />
          <label htmlFor="includeSymbols" className="ml-2 text-sm">
            特殊符号 (!@#$%...)
          </label>
        </div>
      </div>
    </div>
  );

  // 渲染数字选项
  const renderNumberOptions = () => (
    <div className="space-y-4">
      <div className="grid grid-cols-2 gap-4">
        <div>
          <label htmlFor="min" className="block text-sm font-medium mb-1">最小值</label>
          <input
            type="number"
            id="min"
            value={numberOptions.min}
            onChange={(e) => setNumberOptions({ ...numberOptions, min: parseInt(e.target.value) })}
            className="w-full p-2 border border-gray-300 rounded-md dark:border-gray-700 dark:bg-gray-800"
          />
        </div>
        
        <div>
          <label htmlFor="max" className="block text-sm font-medium mb-1">最大值</label>
          <input
            type="number"
            id="max"
            value={numberOptions.max}
            onChange={(e) => setNumberOptions({ ...numberOptions, max: parseInt(e.target.value) })}
            className="w-full p-2 border border-gray-300 rounded-md dark:border-gray-700 dark:bg-gray-800"
          />
        </div>
      </div>
      
      <div>
        <label htmlFor="count" className="block text-sm font-medium mb-1">生成数量</label>
        <input
          type="number"
          id="count"
          min="1"
          max="1000"
          value={numberOptions.count}
          onChange={(e) => setNumberOptions({ ...numberOptions, count: parseInt(e.target.value) })}
          className="w-full p-2 border border-gray-300 rounded-md dark:border-gray-700 dark:bg-gray-800"
        />
      </div>
      
      <div className="space-y-2">
        <div className="flex items-center">
          <input
            type="checkbox"
            id="unique"
            checked={numberOptions.unique}
            onChange={(e) => setNumberOptions({ ...numberOptions, unique: e.target.checked })}
            className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 dark:bg-gray-700 dark:border-gray-600"
          />
          <label htmlFor="unique" className="ml-2 text-sm">
            生成不重复的数字
          </label>
        </div>
        
        <div className="flex items-center">
          <input
            type="checkbox"
            id="sorted"
            checked={numberOptions.sorted}
            onChange={(e) => setNumberOptions({ ...numberOptions, sorted: e.target.checked })}
            className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 dark:bg-gray-700 dark:border-gray-600"
          />
          <label htmlFor="sorted" className="ml-2 text-sm">
            按升序排序结果
          </label>
        </div>
      </div>
    </div>
  );

  // 渲染UUID选项
  const renderUuidOptions = () => (
    <div>
      <label htmlFor="uuidCount" className="block text-sm font-medium mb-1">生成数量</label>
      <input
        type="number"
        id="uuidCount"
        min="1"
        max="100"
        value={uuidCount}
        onChange={(e) => setUuidCount(parseInt(e.target.value))}
        className="w-full p-2 border border-gray-300 rounded-md dark:border-gray-700 dark:bg-gray-800"
      />
    </div>
  );

  // 渲染字符串选项
  const renderStringOptions = () => (
    <div className="space-y-4">
      <div>
        <label className="block text-sm font-medium mb-1">字符串长度: {stringOptions.length}</label>
        <input
          type="range"
          min="1"
          max="100"
          value={stringOptions.length}
          onChange={(e) => setStringOptions({ ...stringOptions, length: parseInt(e.target.value) })}
          className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer dark:bg-gray-700"
        />
        <div className="flex justify-between text-xs text-gray-500">
          <span>1</span>
          <span>100</span>
        </div>
      </div>
      
      <div>
        <label className="block text-sm font-medium mb-2">字符集</label>
        <div className="space-y-2">
          <div className="flex items-center">
            <input
              type="radio"
              id="alphanumeric"
              value="alphanumeric"
              checked={stringOptions.charset === 'alphanumeric'}
              onChange={() => setStringOptions({ ...stringOptions, charset: 'alphanumeric' })}
              className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 dark:bg-gray-700 dark:border-gray-600"
            />
            <label htmlFor="alphanumeric" className="ml-2 text-sm">
              字母和数字 (a-z, A-Z, 0-9)
            </label>
          </div>
          
          <div className="flex items-center">
            <input
              type="radio"
              id="alphabetic"
              value="alphabetic"
              checked={stringOptions.charset === 'alphabetic'}
              onChange={() => setStringOptions({ ...stringOptions, charset: 'alphabetic' })}
              className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 dark:bg-gray-700 dark:border-gray-600"
            />
            <label htmlFor="alphabetic" className="ml-2 text-sm">
              仅字母 (a-z, A-Z)
            </label>
          </div>
          
          <div className="flex items-center">
            <input
              type="radio"
              id="custom"
              value="custom"
              checked={stringOptions.charset === 'custom'}
              onChange={() => setStringOptions({ ...stringOptions, charset: 'custom' })}
              className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 dark:bg-gray-700 dark:border-gray-600"
            />
            <label htmlFor="custom" className="ml-2 text-sm">
              自定义字符集
            </label>
          </div>
        </div>
      </div>
      
      {stringOptions.charset === 'custom' && (
        <div>
          <label htmlFor="customCharset" className="block text-sm font-medium mb-1">自定义字符集</label>
          <input
            type="text"
            id="customCharset"
            value={stringOptions.customCharset}
            onChange={(e) => setStringOptions({ ...stringOptions, customCharset: e.target.value })}
            placeholder="输入要使用的字符..."
            className="w-full p-2 border border-gray-300 rounded-md dark:border-gray-700 dark:bg-gray-800"
          />
        </div>
      )}
    </div>
  );

  // 渲染当前选择的选项面板
  const renderOptions = () => {
    switch (generatorType) {
      case 'password':
        return renderPasswordOptions();
      case 'number':
        return renderNumberOptions();
      case 'uuid':
        return renderUuidOptions();
      case 'string':
        return renderStringOptions();
      default:
        return null;
    }
  };

  return (
    <div className="container mx-auto p-6">
      {/* 头部 */}
      <div className="flex items-center mb-6">
        <button 
          onClick={() => router.back()}
          className="p-2 mr-4 rounded-full hover:bg-gray-100 dark:hover:bg-gray-800"
        >
          <ArrowLeft size={24} />
        </button>
        <h1 className="text-2xl font-bold">随机生成器</h1>
      </div>
      
      {/* 工具说明 */}
      <div className="mb-8 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
        <h2 className="text-lg font-medium mb-2">工具说明</h2>
        <p className="text-gray-700 dark:text-gray-300">
          随机生成器可以帮助您生成各种随机内容，包括强密码、随机数字、UUID和自定义字符串。
          可以根据需要调整各种参数，生成满足特定需求的随机数据。
        </p>
      </div>
      
      {/* 主内容区 */}
      <div className="flex flex-col md:flex-row gap-6">
        {/* 左侧选项面板 */}
        <div className="w-full md:w-1/3 space-y-6">
          {/* 生成器类型选择 */}
          <div className="p-4 border border-gray-200 dark:border-gray-700 rounded-lg">
            <h3 className="text-sm font-medium mb-3 flex items-center">
              <Sliders size={16} className="mr-2" />
              生成器类型
            </h3>
            
            <div className="grid grid-cols-2 gap-2">
              {[
                { type: 'password', label: '密码' },
                { type: 'number', label: '数字' },
                { type: 'uuid', label: 'UUID' },
                { type: 'string', label: '字符串' }
              ].map(({ type, label }) => (
                <button
                  key={type}
                  onClick={() => {
                    setGeneratorType(type as GeneratorType);
                    // 切换类型后自动生成一次
                    setTimeout(generateRandom, 0);
                  }}
                  className={`py-2 px-4 rounded-md text-sm ${
                    generatorType === type
                      ? 'bg-blue-500 text-white'
                      : 'bg-gray-100 hover:bg-gray-200 dark:bg-gray-800 dark:hover:bg-gray-700'
                  }`}
                >
                  {label}
                </button>
              ))}
            </div>
          </div>
          
          {/* 选项设置 */}
          <div className="p-4 border border-gray-200 dark:border-gray-700 rounded-lg">
            <h3 className="text-sm font-medium mb-3 flex items-center">
              <Sliders size={16} className="mr-2" />
              选项设置
            </h3>
            
            {renderOptions()}
          </div>
        </div>
        
        {/* 右侧结果区域 */}
        <div className="w-full md:w-2/3 space-y-6">
          {/* 结果显示 */}
          <div className="p-4 border border-gray-200 dark:border-gray-700 rounded-lg">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-sm font-medium">生成结果</h3>
              
              <div className="flex gap-2">
                <button
                  onClick={generateRandom}
                  className="p-2 rounded bg-blue-500 text-white hover:bg-blue-600 transition-colors"
                  title="重新生成"
                >
                  <RefreshCw size={16} />
                </button>
                
                <button
                  onClick={copyToClipboard}
                  disabled={!result}
                  className={`p-2 rounded ${
                    copied
                      ? 'bg-green-500 text-white'
                      : 'bg-gray-200 hover:bg-gray-300 dark:bg-gray-700 dark:hover:bg-gray-600'
                  } transition-colors`}
                  title="复制到剪贴板"
                >
                  {copied ? <Check size={16} /> : <Copy size={16} />}
                </button>
              </div>
            </div>
            
            <div className="relative">
              <pre className={`w-full min-h-24 p-3 bg-gray-100 dark:bg-gray-800 rounded font-mono text-sm overflow-auto ${
                result.startsWith('错误') ? 'text-red-500' : ''
              }`}>
                {result || '点击"重新生成"按钮生成结果'}
              </pre>
            </div>
          </div>
          
          {/* 使用说明和提示 */}
          <div className="p-4 border border-gray-200 dark:border-gray-700 rounded-lg">
            <h3 className="text-sm font-medium mb-3">使用提示</h3>
            
            {generatorType === 'password' && (
              <div className="text-sm space-y-2 text-gray-700 dark:text-gray-300">
                <p>• 安全的密码应包含大小写字母、数字和符号。</p>
                <p>• 建议密码长度至少为12个字符以提高安全性。</p>
                <p>• 使用不同网站和服务的独特密码可以增强您的在线安全。</p>
              </div>
            )}
            
            {generatorType === 'number' && (
              <div className="text-sm space-y-2 text-gray-700 dark:text-gray-300">
                <p>• 设置适当的最小值和最大值范围。</p>
                <p>• 如果需要生成唯一数字，请确保范围足够大。</p>
                <p>• 可以生成彩票号码、随机抽样等各种用途的随机数。</p>
              </div>
            )}
            
            {generatorType === 'uuid' && (
              <div className="text-sm space-y-2 text-gray-700 dark:text-gray-300">
                <p>• UUID是通用唯一标识符，常用于数据库和分布式系统。</p>
                <p>• 每个UUID都是唯一的，几乎不可能产生重复。</p>
                <p>• 本工具生成的是版本4（随机）UUID。</p>
              </div>
            )}
            
            {generatorType === 'string' && (
              <div className="text-sm space-y-2 text-gray-700 dark:text-gray-300">
                <p>• 可以生成随机用户名、临时标识符等。</p>
                <p>• 使用自定义字符集可以精确控制可能出现的字符。</p>
                <p>• 对于特殊需求，可以输入特定字符作为自定义字符集。</p>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
} 