'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { ArrowLeft, Copy, Upload, Check, RefreshCw, Trash, Download, Settings, RotateCcw } from 'lucide-react';
import * as diff from 'diff';

type DiffMode = 'chars' | 'words' | 'lines' | 'sentences';

export default function TextDiffPage() {
  const [textA, setTextA] = useState<string>('');
  const [textB, setTextB] = useState<string>('');
  const [diffResult, setDiffResult] = useState<diff.Change[]>([]);
  const [diffMode, setDiffMode] = useState<DiffMode>('words');
  const [ignoreCase, setIgnoreCase] = useState<boolean>(false);
  const [ignoreWhitespace, setIgnoreWhitespace] = useState<boolean>(false);
  const [splitView, setSplitView] = useState<boolean>(true);
  const [loading, setLoading] = useState<boolean>(false);
  const [copySuccess, setCopySuccess] = useState<boolean>(false);
  
  const router = useRouter();

  // 示例文本
  const exampleTextA = `这是一段示例文本，用于展示文本对比工具的功能。
这个工具能够帮助您快速找出两段文本之间的差异。
您可以选择不同的对比模式，如按字符、单词、行或句子进行比较。
此外，您还可以忽略大小写和空白字符的差异。`;

  const exampleTextB = `这是一段示例文本，用于展示文本对比工具的强大功能。
这个实用工具能够帮助您快速找出两段文本之间的所有差异。
您可以选择不同的对比模式，例如按字符、单词、行或句子进行比较。
此外，您还可以选择忽略大小写和空白字符的差异。`;

  // 加载示例
  const loadExample = () => {
    setTextA(exampleTextA);
    setTextB(exampleTextB);
  };

  // 清空输入
  const clearAll = () => {
    setTextA('');
    setTextB('');
    setDiffResult([]);
  };

  // 交换文本A和文本B
  const swapTexts = () => {
    const temp = textA;
    setTextA(textB);
    setTextB(temp);
  };

  // 处理文件上传 - 文本A
  const handleFileUploadA = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    const reader = new FileReader();
    reader.onload = (e) => {
      const content = e.target?.result as string;
      setTextA(content);
    };
    reader.readAsText(file);
  };

  // 处理文件上传 - 文本B
  const handleFileUploadB = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    const reader = new FileReader();
    reader.onload = (e) => {
      const content = e.target?.result as string;
      setTextB(content);
    };
    reader.readAsText(file);
  };

  // 复制差异结果为纯文本
  const copyDiffAsText = () => {
    let diffText = '';
    
    diffResult.forEach(part => {
      // 添加前缀以标识添加、删除或相同的部分
      const prefix = part.added ? '+ ' : part.removed ? '- ' : '  ';
      // 将每行添加前缀
      const lines = part.value.split('\n');
      const prefixedLines = lines.map((line: string) => prefix + line).join('\n');
      diffText += prefixedLines + (lines[lines.length - 1] === '' ? '' : '\n');
    });
    
    navigator.clipboard.writeText(diffText)
      .then(() => {
        setCopySuccess(true);
        setTimeout(() => setCopySuccess(false), 2000);
      })
      .catch(err => {
        console.error('复制失败:', err);
      });
  };

  // 下载差异结果
  const downloadDiff = () => {
    let diffText = '';
    
    diffResult.forEach(part => {
      const prefix = part.added ? '+ ' : part.removed ? '- ' : '  ';
      const lines = part.value.split('\n');
      const prefixedLines = lines.map((line: string) => prefix + line).join('\n');
      diffText += prefixedLines + (lines[lines.length - 1] === '' ? '' : '\n');
    });
    
    const blob = new Blob([diffText], { type: 'text/plain;charset=utf-8' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'text-diff-result.txt';
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  // 执行文本对比
  const performDiff = () => {
    if (!textA && !textB) {
      return;
    }

    setLoading(true);

    try {
      let result: diff.Change[] = [];
      let processedTextA = textA;
      let processedTextB = textB;
      
      // 处理选项
      if (ignoreCase) {
        processedTextA = processedTextA.toLowerCase();
        processedTextB = processedTextB.toLowerCase();
      }
      
      if (ignoreWhitespace) {
        processedTextA = processedTextA.replace(/\s+/g, ' ').trim();
        processedTextB = processedTextB.replace(/\s+/g, ' ').trim();
      }
      
      // 根据不同模式执行比较
      switch (diffMode) {
        case 'chars':
          result = diff.diffChars(processedTextA, processedTextB);
          break;
        case 'words':
          result = diff.diffWords(processedTextA, processedTextB);
          break;
        case 'lines':
          result = diff.diffLines(processedTextA, processedTextB);
          break;
        case 'sentences':
          result = diff.diffSentences(processedTextA, processedTextB);
          break;
        default:
          result = diff.diffWords(processedTextA, processedTextB);
      }
      
      setDiffResult(result);
    } catch (err) {
      console.error('比较失败:', err);
    } finally {
      setLoading(false);
    }
  };

  // 文本变化时自动执行对比
  useEffect(() => {
    const timer = setTimeout(() => {
      performDiff();
    }, 500);
    
    return () => clearTimeout(timer);
  }, [textA, textB, diffMode, ignoreCase, ignoreWhitespace]);

  // 渲染合并视图的差异结果
  const renderInlineDiff = () => {
    return (
      <div className="font-mono text-sm whitespace-pre-wrap p-4 bg-white dark:bg-gray-900 border border-gray-300 dark:border-gray-700 rounded-lg overflow-auto">
        {diffResult.map((part, index) => (
          <span
            key={index}
            className={`${
              part.added
                ? 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400'
                : part.removed
                ? 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400'
                : ''
            }`}
          >
            {part.value}
          </span>
        ))}
      </div>
    );
  };

  // 渲染分栏视图的差异结果
  const renderSplitDiff = () => {
    // 将差异结果分割为两列
    const leftParts: diff.Change[] = [];
    const rightParts: diff.Change[] = [];
    
    diffResult.forEach(part => {
      if (part.added) {
        rightParts.push(part);
      } else if (part.removed) {
        leftParts.push(part);
      } else {
        leftParts.push({...part});
        rightParts.push({...part});
      }
    });
    
    return (
      <div className="grid grid-cols-2 gap-4">
        <div className="font-mono text-sm whitespace-pre-wrap p-4 bg-white dark:bg-gray-900 border border-gray-300 dark:border-gray-700 rounded-lg overflow-auto">
          {leftParts.map((part, index) => (
            <span
              key={index}
              className={`${
                part.removed
                  ? 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400'
                  : ''
              }`}
            >
              {part.value}
            </span>
          ))}
        </div>
        <div className="font-mono text-sm whitespace-pre-wrap p-4 bg-white dark:bg-gray-900 border border-gray-300 dark:border-gray-700 rounded-lg overflow-auto">
          {rightParts.map((part, index) => (
            <span
              key={index}
              className={`${
                part.added
                  ? 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400'
                  : ''
              }`}
            >
              {part.value}
            </span>
          ))}
        </div>
      </div>
    );
  };

  return (
    <div className="container mx-auto p-6">
      {/* 头部 */}
      <div className="flex items-center mb-6">
        <button 
          onClick={() => router.back()}
          className="p-2 mr-4 rounded-full hover:bg-gray-100 dark:hover:bg-gray-800"
        >
          <ArrowLeft size={24} />
        </button>
        <h1 className="text-2xl font-bold">文本对比工具</h1>
      </div>

      {/* 工具说明 */}
      <div className="mb-8 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
        <h2 className="text-lg font-medium mb-2">工具说明</h2>
        <p className="text-gray-700 dark:text-gray-300">
          本工具可以帮助您比较两段文本之间的差异，支持按字符、单词、行或句子进行比较。
          红色标记表示删除的内容，绿色标记表示新增的内容。您可以选择合并视图或分栏视图来查看差异。
          所有处理都在浏览器中完成，不会上传文本到服务器。
        </p>
      </div>

      {/* 控制面板 */}
      <div className="mb-6 p-4 border border-gray-200 dark:border-gray-700 rounded-lg">
        <div className="flex items-center mb-4">
          <Settings size={18} className="mr-2" />
          <h2 className="text-lg font-medium">对比设置</h2>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <label className="block text-sm font-medium mb-2">对比模式</label>
            <select
              value={diffMode}
              onChange={(e) => setDiffMode(e.target.value as DiffMode)}
              className="w-full p-2 border border-gray-300 dark:border-gray-700 rounded-md bg-white dark:bg-gray-900"
            >
              <option value="chars">按字符比较</option>
              <option value="words">按单词比较</option>
              <option value="lines">按行比较</option>
              <option value="sentences">按句子比较</option>
            </select>
          </div>
          
          <div className="flex items-center space-x-4">
            <label className="flex items-center space-x-2">
              <input
                type="checkbox"
                checked={ignoreCase}
                onChange={(e) => setIgnoreCase(e.target.checked)}
                className="rounded"
              />
              <span className="text-sm">忽略大小写</span>
            </label>
            
            <label className="flex items-center space-x-2">
              <input
                type="checkbox"
                checked={ignoreWhitespace}
                onChange={(e) => setIgnoreWhitespace(e.target.checked)}
                className="rounded"
              />
              <span className="text-sm">忽略空白字符</span>
            </label>
          </div>
          
          <div className="flex items-center space-x-4">
            <label className="flex items-center space-x-2">
              <input
                type="checkbox"
                checked={splitView}
                onChange={(e) => setSplitView(e.target.checked)}
                className="rounded"
              />
              <span className="text-sm">分栏视图</span>
            </label>
            
            <div className="flex space-x-2">
              <button
                onClick={loadExample}
                className="text-xs p-1 px-2 rounded bg-gray-100 hover:bg-gray-200 dark:bg-gray-800 dark:hover:bg-gray-700"
              >
                加载示例
              </button>
              
              <button
                onClick={clearAll}
                className="text-xs p-1 px-2 rounded bg-gray-100 hover:bg-gray-200 dark:bg-gray-800 dark:hover:bg-gray-700 flex items-center"
              >
                <Trash size={12} className="mr-1" />
                清空
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* 输入区域 */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
        {/* 文本A */}
        <div className="space-y-2">
          <div className="flex justify-between items-center">
            <h3 className="text-base font-medium">文本A (原始文本)</h3>
            <div className="flex space-x-2">
              <label className="text-xs p-1 px-2 rounded bg-gray-100 hover:bg-gray-200 dark:bg-gray-800 dark:hover:bg-gray-700 flex items-center cursor-pointer">
                <Upload size={12} className="mr-1" />
                上传
                <input
                  type="file"
                  accept=".txt,.md,.csv,.json,.html,.xml,.js,.css,.ts"
                  onChange={handleFileUploadA}
                  className="hidden"
                />
              </label>
            </div>
          </div>
          <textarea
            value={textA}
            onChange={(e) => setTextA(e.target.value)}
            placeholder="请输入或粘贴第一段文本..."
            className="w-full h-[200px] p-3 font-mono text-sm border border-gray-300 dark:border-gray-700 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
        </div>

        {/* 文本B */}
        <div className="space-y-2">
          <div className="flex justify-between items-center">
            <h3 className="text-base font-medium">文本B (修改后文本)</h3>
            <div className="flex space-x-2">
              <button
                onClick={swapTexts}
                className="text-xs p-1 px-2 rounded bg-gray-100 hover:bg-gray-200 dark:bg-gray-800 dark:hover:bg-gray-700 flex items-center"
                title="交换文本A和文本B"
              >
                <RotateCcw size={12} className="mr-1" />
                交换
              </button>
              <label className="text-xs p-1 px-2 rounded bg-gray-100 hover:bg-gray-200 dark:bg-gray-800 dark:hover:bg-gray-700 flex items-center cursor-pointer">
                <Upload size={12} className="mr-1" />
                上传
                <input
                  type="file"
                  accept=".txt,.md,.csv,.json,.html,.xml,.js,.css,.ts"
                  onChange={handleFileUploadB}
                  className="hidden"
                />
              </label>
            </div>
          </div>
          <textarea
            value={textB}
            onChange={(e) => setTextB(e.target.value)}
            placeholder="请输入或粘贴第二段文本..."
            className="w-full h-[200px] p-3 font-mono text-sm border border-gray-300 dark:border-gray-700 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
        </div>
      </div>

      {/* 差异结果 */}
      <div className="space-y-4">
        <div className="flex justify-between items-center">
          <h2 className="text-lg font-medium">
            差异结果
            {loading && <RefreshCw size={16} className="ml-2 inline-block animate-spin" />}
          </h2>
          <div className="flex space-x-2">
            <button
              onClick={performDiff}
              disabled={loading || (!textA && !textB)}
              className={`text-xs p-1 px-2 rounded flex items-center ${
                (!textA && !textB) || loading
                  ? 'bg-gray-100 text-gray-400 dark:bg-gray-800 dark:text-gray-600 cursor-not-allowed'
                  : 'bg-blue-500 hover:bg-blue-600 text-white'
              }`}
            >
              <RefreshCw size={12} className={`mr-1 ${loading ? 'animate-spin' : ''}`} />
              刷新
            </button>
            <button
              onClick={copyDiffAsText}
              disabled={diffResult.length === 0}
              className={`text-xs p-1 px-2 rounded flex items-center ${
                diffResult.length === 0
                  ? 'bg-gray-100 text-gray-400 dark:bg-gray-800 dark:text-gray-600 cursor-not-allowed'
                  : 'bg-gray-100 hover:bg-gray-200 dark:bg-gray-800 dark:hover:bg-gray-700'
              }`}
            >
              {copySuccess ? <Check size={12} className="mr-1" /> : <Copy size={12} className="mr-1" />}
              {copySuccess ? '已复制' : '复制'}
            </button>
            <button
              onClick={downloadDiff}
              disabled={diffResult.length === 0}
              className={`text-xs p-1 px-2 rounded flex items-center ${
                diffResult.length === 0
                  ? 'bg-gray-100 text-gray-400 dark:bg-gray-800 dark:text-gray-600 cursor-not-allowed'
                  : 'bg-gray-100 hover:bg-gray-200 dark:bg-gray-800 dark:hover:bg-gray-700'
              }`}
            >
              <Download size={12} className="mr-1" />
              下载
            </button>
          </div>
        </div>

        <div className="border border-gray-200 dark:border-gray-700 rounded-lg overflow-hidden">
          <div className="bg-gray-100 dark:bg-gray-800 p-3 border-b border-gray-200 dark:border-gray-700">
            <div className="flex items-center space-x-6">
              <div className="flex items-center">
                <span className="inline-block w-3 h-3 bg-red-500 rounded-full mr-1"></span>
                <span className="text-sm">删除</span>
              </div>
              <div className="flex items-center">
                <span className="inline-block w-3 h-3 bg-green-500 rounded-full mr-1"></span>
                <span className="text-sm">添加</span>
              </div>
              <div className="flex items-center">
                <span className="inline-block w-3 h-3 bg-gray-300 dark:bg-gray-600 rounded-full mr-1"></span>
                <span className="text-sm">相同</span>
              </div>
            </div>
          </div>
          
          <div className="max-h-[500px] overflow-auto">
            {diffResult.length > 0 ? (
              splitView ? renderSplitDiff() : renderInlineDiff()
            ) : (
              <div className="p-8 text-center text-gray-500">
                {textA || textB ? (
                  loading ? (
                    <p>正在比较文本差异...</p>
                  ) : (
                    <p>两段文本完全相同或尚未输入足够的文本</p>
                  )
                ) : (
                  <p>请在上方输入要比较的文本</p>
                )}
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
} 