'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { ArrowLeft, Search, Download, Copy, RefreshCw, ArrowRightLeft, AlertTriangle } from 'lucide-react';
import * as yaml from 'js-yaml';

export default function YamlToPropertiesPage() {
  const [yamlInput, setYamlInput] = useState('');
  const [propertiesOutput, setPropertiesOutput] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const router = useRouter();

  // 将嵌套对象转换为扁平的属性
  const flattenObject = (obj: any, prefix = '', result: Record<string, string> = {}) => {
    for (const key in obj) {
      const value = obj[key];
      const newKey = prefix ? `${prefix}.${key}` : key;
      
      if (typeof value === 'object' && value !== null && !Array.isArray(value)) {
        flattenObject(value, newKey, result);
      } else if (Array.isArray(value)) {
        // 处理数组
        value.forEach((item, index) => {
          if (typeof item === 'object' && item !== null) {
            flattenObject(item, `${newKey}[${index}]`, result);
          } else {
            result[`${newKey}[${index}]`] = String(item);
          }
        });
      } else {
        result[newKey] = String(value);
      }
    }
    
    return result;
  };

  // 转换处理函数
  const handleConvert = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!yamlInput.trim()) {
      setError('请输入YAML数据');
      return;
    }
    
    setLoading(true);
    setError(null);
    
    try {
      // 解析YAML
      const yamlObj = yaml.load(yamlInput);
      
      // 转换为属性格式
      const flatProperties = flattenObject(yamlObj);
      
      // 生成属性文件内容
      const propertiesContent = Object.entries(flatProperties)
        .map(([key, value]) => `${key}=${value}`)
        .join('\n');
      
      setPropertiesOutput(propertiesContent);
    } catch (err) {
      setError(err instanceof Error ? `YAML解析错误: ${err.message}` : '无效的YAML格式');
    } finally {
      setLoading(false);
    }
  };

  // 示例YAML数据
  const loadExample = () => {
    const exampleYaml = `# 服务器配置
server:
  port: 8080
  servlet:
    context-path: /api
  tomcat:
    max-threads: 200
    connection-timeout: 5000

# 数据库配置
spring:
  datasource:
    url: **************************************
    username: root
    password: password
    driver-class-name: com.mysql.cj.jdbc.Driver
  jpa:
    hibernate:
      ddl-auto: update
    show-sql: true

# 日志配置
logging:
  level:
    root: INFO
    org.springframework.web: DEBUG
  file:
    name: app.log
    max-size: 10MB

# 应用配置
app:
  name: Example Application
  description: A sample Spring Boot application
  version: 1.0.0
  features:
    - user-management
    - reporting
    - notifications
  admin:
    email: <EMAIL>
    role: SUPER_ADMIN`;
    
    setYamlInput(exampleYaml);
    setError(null);
  };

  // 复制到剪贴板
  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
  };

  // 下载Properties结果
  const downloadProperties = () => {
    if (propertiesOutput) {
      const blob = new Blob([propertiesOutput], { type: 'text/plain' });
      const url = URL.createObjectURL(blob);
      
      const a = document.createElement('a');
      a.href = url;
      a.download = 'converted.properties';
      document.body.appendChild(a);
      a.click();
      
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
    }
  };

  // 清空输入
  const clearInput = () => {
    setYamlInput('');
    setPropertiesOutput('');
    setError(null);
  };

  return (
    <div className="min-h-screen bg-background p-6">
      <div className="max-w-6xl mx-auto">
        {/* 返回按钮 */}
        <button 
          onClick={() => router.back()}
          className="mb-6 flex items-center gap-2 text-primary hover:underline"
        >
          <ArrowLeft size={16} />
          <span>返回工具列表</span>
        </button>

        {/* 工具标题 */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold">YAML转Properties</h1>
          <p className="text-muted-foreground mt-2">
            将YAML配置转换为Java Properties格式，适用于Java应用配置迁移
          </p>
        </div>

        {/* 转换区域 */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
          {/* YAML输入区 */}
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <h2 className="font-medium">YAML输入</h2>
              <div className="flex items-center gap-2">
                <button 
                  onClick={clearInput}
                  className="text-xs px-2 py-1 rounded bg-accent hover:bg-accent/80 text-accent-foreground"
                >
                  清空
                </button>
                <button 
                  onClick={loadExample}
                  className="text-xs px-2 py-1 rounded bg-accent hover:bg-accent/80 text-accent-foreground"
                >
                  加载示例
                </button>
                <button 
                  onClick={() => copyToClipboard(yamlInput)}
                  className="p-1 rounded hover:bg-accent"
                  title="复制"
                >
                  <Copy size={14} />
                </button>
              </div>
            </div>
            <textarea
              value={yamlInput}
              onChange={(e) => setYamlInput(e.target.value)}
              placeholder="在此粘贴YAML数据..."
              className="w-full h-[500px] p-4 font-mono text-sm rounded-lg bg-accent border border-border focus:outline-none focus:ring-2 focus:ring-primary resize-none"
            ></textarea>
            {error && (
              <div className="flex items-center gap-2 text-red-500 text-sm">
                <AlertTriangle size={14} />
                <span>{error}</span>
              </div>
            )}
          </div>

          {/* Properties输出区 */}
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <h2 className="font-medium">Properties输出</h2>
              <div className="flex items-center gap-2">
                {propertiesOutput && (
                  <>
                    <button 
                      onClick={() => copyToClipboard(propertiesOutput)}
                      className="text-xs px-2 py-1 rounded bg-primary hover:bg-primary/90 text-primary-foreground flex items-center gap-1"
                    >
                      <Copy size={14} />
                      <span>复制</span>
                    </button>
                    <button 
                      onClick={downloadProperties}
                      className="text-xs px-2 py-1 rounded bg-primary hover:bg-primary/90 text-primary-foreground flex items-center gap-1"
                    >
                      <Download size={14} />
                      <span>下载</span>
                    </button>
                  </>
                )}
              </div>
            </div>
            <div className="relative h-[500px]">
              <textarea
                value={propertiesOutput}
                readOnly
                placeholder="Properties输出将显示在这里..."
                className="w-full h-full p-4 font-mono text-sm rounded-lg bg-accent border border-border focus:outline-none resize-none"
              ></textarea>
              {!propertiesOutput && (
                <div className="absolute inset-0 flex items-center justify-center">
                  <div className="text-center text-muted-foreground p-4">
                    <ArrowRightLeft size={28} className="mx-auto mb-2 opacity-40" />
                    <p>转换后的Properties将显示在这里</p>
                    <p className="text-xs mt-1">点击下方按钮开始转换</p>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* 转换按钮 */}
        <div className="flex justify-center mb-8">
          <button
            onClick={handleConvert}
            disabled={loading || !yamlInput.trim()}
            className="h-12 px-8 rounded-lg bg-primary text-primary-foreground hover:bg-primary/90 flex items-center gap-3 disabled:opacity-70 disabled:cursor-not-allowed"
          >
            {loading ? (
              <>
                <span className="animate-spin h-5 w-5 border-2 border-white border-t-transparent rounded-full"></span>
                <span>转换中...</span>
              </>
            ) : (
              <>
                <ArrowRightLeft size={18} />
                <span>转换 YAML 到 Properties</span>
              </>
            )}
          </button>
        </div>

        {/* 信息说明 */}
        <div className="bg-card border border-border rounded-lg p-6">
          <h3 className="font-medium mb-4">关于YAML和Properties</h3>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h4 className="font-medium mb-2">YAML (YAML Ain't Markup Language)</h4>
              <ul className="list-disc list-inside mb-4 text-muted-foreground text-sm space-y-1">
                <li>人类友好的数据序列化格式</li>
                <li>使用缩进表示层次结构，更易读</li>
                <li>支持复杂的数据结构和注释</li>
                <li>广泛用于现代应用的配置文件</li>
                <li>常见于Spring Boot、Kubernetes等环境</li>
              </ul>
            </div>
            
            <div>
              <h4 className="font-medium mb-2">Java Properties</h4>
              <ul className="list-disc list-inside mb-4 text-muted-foreground text-sm space-y-1">
                <li>Java平台的传统配置格式</li>
                <li>使用键值对表示配置项</li>
                <li>格式为 key=value 的简单文本</li>
                <li>不支持层次结构，使用点号分隔多级属性</li>
                <li>不支持复杂的数据类型，所有值都是字符串</li>
              </ul>
            </div>
          </div>
          
          <div className="mt-4 text-sm text-muted-foreground">
            <p>转换提示：</p>
            <ul className="list-disc list-inside mt-2 space-y-1">
              <li>YAML中的嵌套结构将转换为带点号分隔的扁平化属性</li>
              <li>YAML数组将转换为索引属性，例如 array[0], array[1]</li>
              <li>所有值都将转换为字符串格式</li>
              <li>YAML中的注释在转换后会丢失</li>
              <li>复杂的YAML结构（如引用和锚点）可能无法完全准确转换</li>
              <li>此工具最适合简单到中等复杂度的YAML配置文件</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
}
