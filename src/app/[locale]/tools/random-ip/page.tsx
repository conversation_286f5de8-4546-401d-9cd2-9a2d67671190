'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { ArrowLeft, Copy, Check, RefreshCw, Download } from 'lucide-react';

type IPVersion = 'ipv4' | 'ipv6';

interface IPRange {
  start: string;
  end: string;
}

export default function RandomIPPage() {
  const router = useRouter();
  const [version, setVersion] = useState<IPVersion>('ipv4');
  const [count, setCount] = useState<number>(1);
  const [range, setRange] = useState<IPRange>({
    start: '0.0.0.0',
    end: '***************'
  });
  const [generatedIPs, setGeneratedIPs] = useState<string[]>([]);
  const [copySuccess, setCopySuccess] = useState<boolean>(false);

  // 生成随机IPv4地址
  const generateIPv4 = (): string => {
    const start = range.start.split('.').map(Number);
    const end = range.end.split('.').map(Number);
    
    const ip = start.map((startNum, i) => {
      const endNum = end[i];
      return Math.floor(Math.random() * (endNum - startNum + 1)) + startNum;
    });
    
    return ip.join('.');
  };

  // 生成随机IPv6地址
  const generateIPv6 = (): string => {
    const segments = Array(8).fill(0).map(() => 
      Math.floor(Math.random() * 65536).toString(16).padStart(4, '0')
    );
    return segments.join(':');
  };

  // 生成IP地址
  const generateIPs = () => {
    const ips: string[] = [];
    for (let i = 0; i < count; i++) {
      ips.push(version === 'ipv4' ? generateIPv4() : generateIPv6());
    }
    setGeneratedIPs(ips);
  };

  // 复制结果
  const copyResult = () => {
    const text = generatedIPs.join('\n');
    navigator.clipboard.writeText(text)
      .then(() => {
        setCopySuccess(true);
        setTimeout(() => setCopySuccess(false), 2000);
      })
      .catch(err => {
        console.error('复制失败:', err);
      });
  };

  // 下载结果
  const downloadResult = () => {
    if (!generatedIPs.length) return;

    const text = generatedIPs.join('\n');
    const blob = new Blob([text], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `random-ips-${version}.txt`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  };

  // 处理范围变化
  const handleRangeChange = (type: 'start' | 'end', value: string) => {
    setRange(prev => ({
      ...prev,
      [type]: value
    }));
  };

  return (
    <div className="container mx-auto p-6">
      {/* 头部 */}
      <div className="flex items-center mb-6">
        <button 
          onClick={() => router.back()}
          className="p-2 mr-4 rounded-full hover:bg-gray-100 dark:hover:bg-gray-800"
        >
          <ArrowLeft size={24} />
        </button>
        <h1 className="text-2xl font-bold">随机IP生成器</h1>
      </div>

      {/* 工具说明 */}
      <div className="mb-8 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
        <h2 className="text-lg font-medium mb-2">工具说明</h2>
        <p className="text-gray-700 dark:text-gray-300">
          本工具可以生成随机的IPv4和IPv6地址。对于IPv4，您可以指定生成地址的范围。
          所有生成都在浏览器本地完成，不会上传您的数据。
        </p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* 左侧：配置区域 */}
        <div className="space-y-4">
          <div className="bg-white dark:bg-gray-900 border border-gray-300 dark:border-gray-700 rounded-lg p-4">
            <h2 className="text-lg font-medium mb-4">配置选项</h2>

            {/* IP版本选择 */}
            <div className="mb-4">
              <label className="block text-sm font-medium mb-2">IP版本</label>
              <div className="flex space-x-2">
                <button
                  onClick={() => setVersion('ipv4')}
                  className={`flex-1 py-2 rounded-md ${
                    version === 'ipv4'
                      ? 'bg-blue-500 text-white'
                      : 'bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-300'
                  }`}
                >
                  IPv4
                </button>
                <button
                  onClick={() => setVersion('ipv6')}
                  className={`flex-1 py-2 rounded-md ${
                    version === 'ipv6'
                      ? 'bg-blue-500 text-white'
                      : 'bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-300'
                  }`}
                >
                  IPv6
                </button>
              </div>
            </div>

            {/* 生成数量 */}
            <div className="mb-4">
              <label className="block text-sm font-medium mb-2">生成数量</label>
              <input
                type="number"
                min="1"
                max="1000"
                value={count}
                onChange={(e) => setCount(Math.min(1000, Math.max(1, parseInt(e.target.value) || 1)))}
                className="w-full p-2 border border-gray-300 dark:border-gray-700 rounded-md bg-white dark:bg-gray-900 focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>

            {/* IP范围设置（仅IPv4） */}
            {version === 'ipv4' && (
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium mb-2">起始IP</label>
                  <input
                    type="text"
                    value={range.start}
                    onChange={(e) => handleRangeChange('start', e.target.value)}
                    className="w-full p-2 border border-gray-300 dark:border-gray-700 rounded-md bg-white dark:bg-gray-900 focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="0.0.0.0"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium mb-2">结束IP</label>
                  <input
                    type="text"
                    value={range.end}
                    onChange={(e) => handleRangeChange('end', e.target.value)}
                    className="w-full p-2 border border-gray-300 dark:border-gray-700 rounded-md bg-white dark:bg-gray-900 focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="***************"
                  />
                </div>
              </div>
            )}

            {/* 生成按钮 */}
            <button
              onClick={generateIPs}
              className="w-full py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
            >
              生成IP地址
            </button>
          </div>
        </div>

        {/* 右侧：结果区域 */}
        <div className="space-y-4">
          <div className="flex justify-between items-center mb-2">
            <h2 className="text-lg font-medium">生成结果</h2>
            <div className="flex space-x-2">
              <button
                onClick={copyResult}
                className="text-xs p-1 px-2 rounded bg-gray-100 hover:bg-gray-200 dark:bg-gray-800 dark:hover:bg-gray-700 flex items-center"
                disabled={!generatedIPs.length}
              >
                {copySuccess ? <Check size={12} className="mr-1" /> : <Copy size={12} className="mr-1" />}
                {copySuccess ? '已复制' : '复制结果'}
              </button>
              <button
                onClick={downloadResult}
                className="text-xs p-1 px-2 rounded bg-gray-100 hover:bg-gray-200 dark:bg-gray-800 dark:hover:bg-gray-700 flex items-center"
                disabled={!generatedIPs.length}
              >
                <Download size={12} className="mr-1" />
                下载结果
              </button>
            </div>
          </div>

          <div className="bg-white dark:bg-gray-900 border border-gray-300 dark:border-gray-700 rounded-lg p-4">
            <div className="font-mono text-sm whitespace-pre-wrap">
              {generatedIPs.length > 0 ? (
                generatedIPs.map((ip, index) => (
                  <div key={index} className="py-1">
                    {ip}
                  </div>
                ))
              ) : (
                <p className="text-gray-500 dark:text-gray-400">生成的IP地址将显示在这里...</p>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
} 