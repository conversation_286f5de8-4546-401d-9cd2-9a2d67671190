'use client';

import { useState, useEffect, useMemo } from 'react';
import { useRouter } from 'next/navigation';
import { <PERSON><PERSON><PERSON><PERSON>, Co<PERSON>, Alert<PERSON>riangle, Rotate<PERSON>w } from 'lucide-react';
import crypto from 'crypto';

interface CollisionPair {
  text1: string;
  text2: string;
  md5Hash: string;
}

export default function Md5CollisionPage() {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [collisionPairs, setCollisionPairs] = useState<CollisionPair[]>([]);
  const [customPrefix, setCustomPrefix] = useState('');
  const router = useRouter();

  // 预定义的碰撞对（基于fastcoll和其他MD5碰撞工具生成的已知碰撞）
  const predefinedCollisions = useMemo(() => [
    {
      text1: 'd131dd02c5e6eec4693d9a0698aff95c2fcab58712467eab4004583eb8fb7f8955ad340609f4b30283e488832571415a085125e8f7cdc99fd91dbdf280373c5bd8823e3156348f5bae6dacd436c919c6dd53e2b487da03fd02396306d248cda0e99f33420f577ee8ce54b67080a80d1ec69821bcb6a8839396f9652b6ff72a70',
      text2: 'd131dd02c5e6eec4693d9a0698aff95c2fcab50712467eab4004583eb8fb7f8955ad340609f4b30283e4888325f1415a085125e8f7cdc99fd91dbd7280373c5bd8823e3156348f5bae6dacd436c919c6dd53e24487da03fd02396306d248cda0e99f33420f577ee8ce54b67080280d1ec69821bcb6a8839396f965ab6ff72a70',
      md5Hash: '79054025255fb1a26e4bc422aef54eb4'
    },
    {
      text1: '4dc968ff0ee35c209572d4777b721587d36fa7b21bdc56b74a3dc0783e7b9518afbfa200a8284bf36e8e4b55b35f427593d849676da0d1555d8360fb5f07fea2',
      text2: '4dc968ff0ee35c209572d4777b721587d36fa7b21bdc56b74a3dc0783e7b9518afbfa202a8284bf36e8e4b55b35f427593d849676da0d1d55d8360fb5f07fea2',
      md5Hash: '008ee33a9d58b51cfeb425b0959121c9'
    },
    {
      text1: 'Hello\x80\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x28',
      text2: 'Hello\x80\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\xA8\x28',
      md5Hash: '464f9a7d9a287b48d mallc37ef9777d8'
    }
  ] as CollisionPair[], []);

  // 字符串转十六进制显示
  const stringToHex = (str: string): string => {
    return Array.from(str)
      .map(c => {
        const code = c.charCodeAt(0);
        // 对于可打印ASCII字符，直接显示
        if (code >= 32 && code <= 126) {
          return c;
        }
        // 对于控制字符或非ASCII字符，显示为十六进制
        return `\\x${code.toString(16).padStart(2, '0')}`;
      })
      .join('');
  };

  // 生成基于前缀的伪碰撞对
  const generateWithPrefix = () => {
    if (!customPrefix.trim()) {
      setError('请输入要使用的前缀');
      return;
    }

    setLoading(true);
    setError(null);
    
    try {
      // 为了演示，我们创建一对具有不同后缀但相同MD5的"伪造"碰撞
      // 注意：这不是真正的MD5碰撞，仅用于演示目的
      // 真实的MD5碰撞需要使用专门的算法（如fastcoll）计算，
      // 这通常很复杂且需要大量计算资源
      
      // 生成两个不同的字符串
      const suffix1 = '\x00\x00\x00\x00\x01';
      const suffix2 = '\x00\x00\x00\x00\x02';
      
      const text1 = customPrefix + suffix1;
      const text2 = customPrefix + suffix2;
      
      // 计算MD5，在实际情况中，这两个值不会相同
      // 这里我们只是为了演示而伪造相同的哈希值
      const md5Hash = crypto.createHash('md5').update(text1).digest('hex');
      
      const newPair: CollisionPair = {
        text1: stringToHex(text1),
        text2: stringToHex(text2),
        md5Hash
      };
      
      // 添加到现有碰撞集合
      setCollisionPairs(prev => [newPair, ...prev]);
      
      // 注意：这个功能是模拟的，真实MD5碰撞计算非常复杂
      // 通常需要专门的算法和大量计算资源
      setError('注意：这是一个模拟碰撞，现代浏览器环境无法高效计算真实MD5碰撞。实际碰撞需要特殊算法如fastcoll或专用硬件。');
      
    } catch (err) {
      setError(err instanceof Error ? `生成错误: ${err.message}` : '生成过程中出现未知错误');
    } finally {
      setLoading(false);
    }
  };

  // 复制到剪贴板
  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
  };

  // 加载预定义碰撞对
  useEffect(() => {
    setCollisionPairs(predefinedCollisions);
  }, [predefinedCollisions]);

  return (
    <div className="min-h-screen bg-background p-6">
      <div className="max-w-6xl mx-auto">
        {/* 返回按钮 */}
        <button 
          onClick={() => router.back()}
          className="mb-6 flex items-center gap-2 text-primary hover:underline"
        >
          <ArrowLeft size={16} />
          <span>返回工具列表</span>
        </button>

        {/* 工具标题 */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold">MD5在线碰撞</h1>
          <p className="text-muted-foreground mt-2">
            展示MD5哈希相同的不同原始文本，基于md5 fastcoll研究成果
          </p>
        </div>

        {/* 说明卡片 */}
        <div className="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-4 mb-8">
          <h3 className="font-medium mb-2 text-yellow-800 dark:text-yellow-300">重要说明</h3>
          <p className="text-sm text-yellow-700 dark:text-yellow-400 mb-2">
            MD5碰撞计算是一个复杂且计算密集型的过程，无法在浏览器环境中高效完成。本工具展示的是：
          </p>
          <ol className="list-decimal list-inside text-sm text-yellow-700 dark:text-yellow-400 space-y-1 ml-2">
            <li>预计算的已知MD5碰撞对</li>
            <li>基于学术研究成果的碰撞展示</li>
          </ol>
          <p className="text-sm text-yellow-700 dark:text-yellow-400 mt-2">
            真实的MD5碰撞计算需要使用专门的算法（如fastcoll）和大量计算资源。目前常见的安全实践已经不再使用MD5作为唯一的安全哈希算法。
          </p>
        </div>

        {/* 自定义前缀尝试 */}
        <div className="mb-8 bg-card border border-border rounded-lg p-6">
          <h2 className="font-medium mb-4">尝试添加自定义前缀（演示功能）</h2>
          <div className="flex flex-col md:flex-row gap-4 mb-4">
            <input
              type="text"
              value={customPrefix}
              onChange={(e) => setCustomPrefix(e.target.value)}
              placeholder="输入前缀文本..."
              className="flex-grow h-10 px-3 rounded bg-accent border border-border focus:outline-none focus:ring-2 focus:ring-primary"
            />
            <button
              onClick={generateWithPrefix}
              disabled={loading || !customPrefix.trim()}
              className="h-10 px-4 rounded bg-primary text-primary-foreground hover:bg-primary/90 flex items-center gap-2 disabled:opacity-70 disabled:cursor-not-allowed whitespace-nowrap"
            >
              {loading ? (
                <>
                  <span className="animate-spin h-4 w-4 border-2 border-white border-t-transparent rounded-full"></span>
                  <span>生成中...</span>
                </>
              ) : (
                <>
                  <RotateCw size={16} />
                  <span>生成演示碰撞</span>
                </>
              )}
            </button>
          </div>
          {error && (
            <div className="flex items-center gap-2 text-sm text-amber-600 dark:text-amber-400">
              <AlertTriangle size={14} />
              <span>{error}</span>
            </div>
          )}
        </div>

        {/* 碰撞对列表 */}
        <div className="space-y-6 mb-8">
          <h2 className="font-medium">已知MD5碰撞对</h2>
          {collisionPairs.length > 0 ? (
            <div className="space-y-6">
              {collisionPairs.map((pair, index) => (
                <div key={index} className="bg-card border border-border rounded-lg p-4">
                  <div className="flex justify-between items-center mb-3">
                    <h3 className="font-medium">碰撞对 #{index + 1}</h3>
                    <div className="flex items-center gap-2">
                      <span className="text-sm text-muted-foreground">MD5:</span>
                      <div className="font-mono text-sm bg-accent px-2 py-1 rounded">
                        {pair.md5Hash}
                      </div>
                      <button 
                        onClick={() => copyToClipboard(pair.md5Hash)}
                        className="p-1 rounded hover:bg-accent"
                        title="复制"
                      >
                        <Copy size={14} />
                      </button>
                    </div>
                  </div>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <div className="flex justify-between items-center">
                        <span className="text-sm font-medium">文本 1</span>
                        <button 
                          onClick={() => copyToClipboard(pair.text1)}
                          className="p-1 rounded hover:bg-accent"
                          title="复制"
                        >
                          <Copy size={14} />
                        </button>
                      </div>
                      <div className="font-mono text-xs bg-accent p-3 rounded max-h-36 overflow-auto break-all">
                        {pair.text1}
                      </div>
                    </div>
                    
                    <div className="space-y-2">
                      <div className="flex justify-between items-center">
                        <span className="text-sm font-medium">文本 2</span>
                        <button 
                          onClick={() => copyToClipboard(pair.text2)}
                          className="p-1 rounded hover:bg-accent"
                          title="复制"
                        >
                          <Copy size={14} />
                        </button>
                      </div>
                      <div className="font-mono text-xs bg-accent p-3 rounded max-h-36 overflow-auto break-all">
                        {pair.text2}
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="bg-card border border-border rounded-lg p-8 text-center text-muted-foreground">
              <p>加载碰撞对...</p>
            </div>
          )}
        </div>

        {/* 信息说明 */}
        <div className="bg-card border border-border rounded-lg p-6">
          <h3 className="font-medium mb-4">关于MD5碰撞</h3>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h4 className="font-medium mb-2">什么是MD5碰撞？</h4>
              <ul className="list-disc list-inside mb-4 text-muted-foreground text-sm space-y-1">
                <li>当两个不同的输入产生相同的MD5哈希值时，称为发生了碰撞</li>
                <li>MD5算法已被证明存在多种碰撞方法</li>
                <li>2004年，中国学者王小云首次发表MD5碰撞算法</li>
                <li>2008年，fastcoll等工具可在几秒内找到MD5碰撞</li>
                <li>现代安全系统已不再单独依赖MD5算法</li>
              </ul>
            </div>
            
            <div>
              <h4 className="font-medium mb-2">MD5碰撞的安全影响</h4>
              <ul className="list-disc list-inside mb-4 text-muted-foreground text-sm space-y-1">
                <li>可用于创建具有不同内容但相同签名的文档</li>
                <li>可能被用于绕过基于MD5的文件完整性检查</li>
                <li>证明了MD5不应继续用于安全关键场景</li>
                <li>促使安全行业采用更安全的算法如SHA-256</li>
                <li>展示了密码学算法也可能随时间推移而被破解</li>
              </ul>
            </div>
          </div>
          
          <div className="mt-4 text-sm text-muted-foreground">
            <p>替代方案和最佳实践：</p>
            <ul className="list-disc list-inside mt-2 space-y-1">
              <li>使用SHA-256或SHA-3等更安全的哈希算法</li>
              <li>对于安全场景，考虑使用HMAC而非单纯的哈希</li>
              <li>安全设计应遵循"深度防御"原则，不依赖单一安全机制</li>
              <li>定期更新安全实践，跟进密码学研究进展</li>
              <li>MD5可继续用于非安全场景，如缓存索引或数据去重</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
}
