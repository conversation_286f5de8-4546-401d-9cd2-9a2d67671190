'use client';

import { useState } from 'react';
import { useRouter, usePathname } from 'next/navigation';
import { Search, Filter, Grid, List, ArrowRight } from 'lucide-react';
import { useTheme } from 'next-themes';
import { cn } from '@/lib/utils';
import { isToolDeveloped } from '@/lib/tools/utils';
import { tools } from '@/lib/tools/data';

// 工具分类定义
const toolCategories = [
  {
    id: 'domain-ip',
    name: '域名/IP',
    icon: '🌐',
    color: 'bg-blue-500',
    tools: tools.filter(tool => tool.category === '域名/IP')
  },
  {
    id: 'dev-tools',
    name: '开发工具',
    icon: '⚙️',
    color: 'bg-purple-500',
    tools: tools.filter(tool => tool.category === '开发工具')
  },
  {
    id: 'security',
    name: '安全工具',
    icon: '🔒',
    color: 'bg-red-500',
    tools: tools.filter(tool => tool.category === '安全工具')
  },
  {
    id: 'utility',
    name: '实用工具',
    icon: '🧰',
    color: 'bg-green-500',
    tools: tools.filter(tool => tool.category === '实用工具')
  }
];

export default function ToolsIndexPage() {
  const [searchQuery, setSearchQuery] = useState('');
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const router = useRouter();
  const pathname = usePathname();
  const currentLocale = pathname.split('/')[1];

  const navigateToTool = (toolId: string) => {
    if (isToolDeveloped(toolId)) {
      router.push(`/${currentLocale}/tools/${toolId}`);
    } else {
      // 查找工具信息
      const tool = tools.find(t => t.id === toolId);
      
      if (tool) {
        // 显示提示通知
        const notification = document.createElement('div');
        notification.className = 'fixed bottom-4 right-4 p-4 rounded-lg shadow-lg bg-blue-500 text-white z-50';
        notification.innerHTML = `
          <h4 class="font-medium">功能提示</h4>
          <p class="text-sm">${tool.name} 功能正在开发中</p>
        `;
        document.body.appendChild(notification);
        
        // 3秒后移除通知
        setTimeout(() => {
          notification.remove();
        }, 3000);
      }
    }
  };

  // 过滤工具
  const filteredCategories = toolCategories.map(category => {
    return {
      ...category,
      tools: category.tools.filter(tool => 
        tool.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        tool.description.toLowerCase().includes(searchQuery.toLowerCase())
      )
    };
  }).filter(category => category.tools.length > 0);

  return (
    <div className="p-6">
      <h1 className="text-3xl font-bold mb-2">工具箱</h1>
      <p className="text-muted-foreground mb-8">选择您需要使用的工具</p>

      {/* 工具栏 */}
      <div className="flex items-center justify-between mb-8">
        <div className="flex items-center gap-4">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 -translate-y-1/2 text-muted-foreground" size={18} />
            <input
              type="text"
              placeholder="搜索工具..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-64 h-10 pl-10 pr-4 rounded-lg bg-accent border border-border focus:outline-none focus:ring-2 focus:ring-primary"
            />
          </div>
          <button className="h-10 px-4 rounded-lg bg-accent border border-border hover:bg-accent/80 flex items-center gap-2">
            <Filter size={18} />
            <span>筛选</span>
          </button>
        </div>
        <div className="flex items-center gap-2">
          <button
            onClick={() => setViewMode('grid')}
            className={`p-2 rounded ${
              viewMode === 'grid' ? 'bg-primary text-primary-foreground' : 'hover:bg-accent'
            }`}
          >
            <Grid size={20} />
          </button>
          <button
            onClick={() => setViewMode('list')}
            className={`p-2 rounded ${
              viewMode === 'list' ? 'bg-primary text-primary-foreground' : 'hover:bg-accent'
            }`}
          >
            <List size={20} />
          </button>
        </div>
      </div>

      {/* 工具分类 */}
      {filteredCategories.map((category) => (
        <div key={category.id} className="mb-12">
          <div className="flex items-center gap-3 mb-4">
            <div className={`p-2 rounded-lg ${category.color} text-white`}>
              <span className="text-xl">{category.icon}</span>
            </div>
            <h2 className="text-xl font-semibold">{category.name}</h2>
          </div>

          <div className={`grid gap-4 ${
            viewMode === 'grid' ? 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4' : 'grid-cols-1'
          }`}>
            {category.tools.map((tool) => (
              <div
                key={tool.id}
                className={`group cursor-pointer bg-card border border-border rounded-lg overflow-hidden hover:shadow-md transition-all duration-200 ${
                  viewMode === 'list' ? 'flex items-center gap-4 p-4' : 'p-5'
                }`}
                onClick={() => navigateToTool(tool.id)}
              >
                <div className={`${
                  viewMode === 'list' ? 'w-12 h-12 flex-shrink-0' : 'w-14 h-14 mb-4'
                } rounded-lg bg-accent flex items-center justify-center text-2xl`}>
                  {tool.icon}
                </div>
                <div className="flex-1">
                  <div className="flex items-center justify-between">
                    <h3 className="font-medium mb-1">{tool.name}</h3>
                    <div className="opacity-0 group-hover:opacity-100 transition-opacity">
                      <ArrowRight size={16} className="text-primary" />
                    </div>
                  </div>
                  <p className="text-sm text-muted-foreground line-clamp-2">{tool.description}</p>
                </div>
              </div>
            ))}
          </div>
        </div>
      ))}

      {/* 如果没有匹配的工具 */}
      {filteredCategories.length === 0 && (
        <div className="flex flex-col items-center justify-center py-12">
          <p className="text-muted-foreground mb-2">没有找到匹配的工具</p>
          <button
            onClick={() => setSearchQuery('')}
            className="text-primary hover:underline"
          >
            清除搜索条件
          </button>
        </div>
      )}
    </div>
  );
} 