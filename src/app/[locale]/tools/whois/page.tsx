'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { ArrowLeft, Search, Download, Copy, RefreshCw } from 'lucide-react';

export default function WhoisPage() {
  const [domain, setDomain] = useState('');
  const [loading, setLoading] = useState(false);
  const [result, setResult] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);
  const router = useRouter();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!domain) {
      setError('请输入域名');
      return;
    }
    
    // 简单的域名格式验证
    const domainRegex = /^[a-zA-Z0-9][a-zA-Z0-9-]{0,61}[a-zA-Z0-9](?:\.[a-zA-Z]{2,})+$/;
    if (!domainRegex.test(domain)) {
      setError('请输入有效的域名');
      return;
    }
    
    setLoading(true);
    setError(null);
    
    try {
      const response = await fetch('/api/whois', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ domain }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || '查询失败');
      }

      setResult(data.data);
      setLoading(false);
    } catch (err) {
      setError(err instanceof Error ? err.message : '查询失败，请稍后重试');
      setLoading(false);
    }
  };

  const copyToClipboard = () => {
    if (result) {
      navigator.clipboard.writeText(result);
      // 创建临时通知
      const notification = document.createElement('div');
      notification.className = 'fixed bottom-4 right-4 p-4 rounded-lg shadow-lg bg-primary text-white z-50';
      notification.innerHTML = `
        <h4 class="font-medium">复制成功</h4>
        <p class="text-sm">已复制查询结果到剪贴板</p>
      `;
      document.body.appendChild(notification);
      
      // 3秒后移除通知
      setTimeout(() => {
        notification.remove();
      }, 3000);
    }
  };

  const refreshQuery = () => {
    if (domain) {
      handleSubmit({ preventDefault: () => {} } as React.FormEvent);
    }
  };

  const downloadResult = () => {
    if (result) {
      const blob = new Blob([result], { type: 'text/plain' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `whois-${domain}.txt`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
    }
  };

  return (
    <div className="min-h-screen bg-background p-6">
      <div className="max-w-5xl mx-auto">
        {/* 返回按钮 */}
        <button 
          onClick={() => router.back()}
          className="mb-6 flex items-center gap-2 text-primary hover:underline"
        >
          <ArrowLeft size={16} />
          <span>返回工具列表</span>
        </button>

        {/* 工具标题 */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold">Whois查询</h1>
          <p className="text-muted-foreground mt-2">
            查询域名注册信息，包括所有者、注册商、注册日期和到期日期等详细信息
          </p>
        </div>

        {/* 查询表单 */}
        <div className="bg-card border border-border rounded-lg p-6 mb-6">
          <form onSubmit={handleSubmit} className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1">
              <label htmlFor="domain" className="block text-sm font-medium mb-2">
                域名
              </label>
              <div className="relative">
                <input
                  id="domain"
                  type="text"
                  placeholder="wenhaofree.com"
                  value={domain}
                  onChange={(e) => setDomain(e.target.value)}
                  className="w-full h-10 pl-4 pr-10 rounded-lg bg-accent border border-border focus:outline-none focus:ring-2 focus:ring-primary"
                />
              </div>
              {error && <p className="text-red-500 text-sm mt-1">{error}</p>}
            </div>
            <div className="flex items-end">
              <button
                type="submit"
                disabled={loading}
                className="h-10 px-6 rounded-lg bg-primary text-primary-foreground hover:bg-primary/90 flex items-center gap-2 disabled:opacity-70"
              >
                {loading ? (
                  <>
                    <span className="animate-spin h-4 w-4 border-2 border-white border-t-transparent rounded-full"></span>
                    <span>查询中...</span>
                  </>
                ) : (
                  <>
                    <Search size={16} />
                    <span>查询</span>
                  </>
                )}
              </button>
            </div>
          </form>
        </div>

        {/* 结果显示 */}
        {result && (
          <div className="bg-card border border-border rounded-lg">
            <div className="p-4 border-b border-border flex items-center justify-between">
              <h3 className="font-medium">查询结果</h3>
              <div className="flex items-center gap-2">
                <button 
                  onClick={refreshQuery}
                  className="p-2 rounded hover:bg-accent"
                  title="刷新"
                >
                  <RefreshCw size={16} />
                </button>
                <button 
                  onClick={copyToClipboard}
                  className="p-2 rounded hover:bg-accent"
                  title="复制到剪贴板"
                >
                  <Copy size={16} />
                </button>
                <button 
                  onClick={downloadResult}
                  className="p-2 rounded hover:bg-accent"
                  title="下载结果"
                >
                  <Download size={16} />
                </button>
              </div>
            </div>
            <pre className="p-6 overflow-auto bg-accent/50 whitespace-pre-wrap rounded-b-lg">
              {result}
            </pre>
          </div>
        )}

        {/* 使用说明 */}
        <div className="mt-8">
          <h2 className="text-xl font-semibold mb-4">使用说明</h2>
          <div className="bg-card border border-border rounded-lg p-6">
            <ol className="list-decimal list-inside space-y-2">
              <li>在输入框中输入您想要查询的域名（如：wenhaofree.com）</li>
              <li>点击"查询"按钮获取域名的Whois注册信息</li>
              <li>查询结果将显示域名的所有者、注册商、创建日期、到期日期等信息</li>
              <li>您可以使用工具栏上的按钮复制或下载查询结果</li>
            </ol>
            <div className="mt-4 p-4 bg-accent/50 rounded-lg">
              <p className="text-sm text-muted-foreground">
                <strong>提示：</strong> Whois信息可能会因隐私保护而被部分隐藏。某些顶级域名可能有不同的Whois格式。
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
} 