'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { ArrowLeft, Co<PERSON>, Hash, FileText, AlertTriangle } from 'lucide-react';
import crypto from 'crypto';

export default function ShaCalculatorPage() {
  const [input, setInput] = useState('');
  const [results, setResults] = useState<{[key: string]: string}>({});
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  type AlgorithmsState = {
    sha1: boolean;
    sha224: boolean;
    sha256: boolean;
    sha384: boolean;
    sha512: boolean;
    sha3_224: boolean;
    sha3_256: boolean;
    sha3_384: boolean;
    sha3_512: boolean;
    [key: string]: boolean; // Allow index access
  };

  const [selectedAlgorithms, setSelectedAlgorithms] = useState<AlgorithmsState>({
    sha1: true,
    sha224: true,
    sha256: true,
    sha384: true,
    sha512: true,
    sha3_224: false,
    sha3_256: false,
    sha3_384: false,
    sha3_512: false
  });
  const router = useRouter();

  // 计算哈希值
  const calculateHashes = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!input.trim()) {
      setError('请输入需要计算哈希的文本');
      return;
    }
    
    setLoading(true);
    setError(null);
    const newResults: {[key: string]: string} = {};
    
    try {
      // 计算选中的哈希算法
      Object.entries(selectedAlgorithms).forEach(([algorithm, selected]) => {
        if (selected) {
          const hash = crypto.createHash(algorithm.replace('_', '-'));
          hash.update(input);
          newResults[algorithm] = hash.digest('hex');
        }
      });
      
      setResults(newResults);
    } catch (err) {
      setError(err instanceof Error ? `计算错误: ${err.message}` : '计算过程中出现未知错误');
    } finally {
      setLoading(false);
    }
  };

  // 复制到剪贴板
  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
  };

  // 复制所有结果
  const copyAllResults = () => {
    const formattedResults = Object.entries(results)
      .map(([algorithm, hash]) => `${algorithm.toUpperCase()}: ${hash}`)
      .join('\n');
    
    navigator.clipboard.writeText(formattedResults);
  };

  // 清空输入
  const clearInput = () => {
    setInput('');
    setResults({});
    setError(null);
  };

  // 加载示例
  const loadExample = () => {
    setInput('Hello, World!');
    setError(null);
  };

  // 切换算法选择
  const toggleAlgorithm = (algorithm: string) => {
    setSelectedAlgorithms(prev => ({
      ...prev,
      [algorithm]: !prev[algorithm as keyof typeof selectedAlgorithms]
    }));
  };

  // 选择全部算法
  const selectAllAlgorithms = () => {
    const updatedSelection = Object.keys(selectedAlgorithms).reduce((acc, key) => {
      acc[key] = true;
      return acc;
    }, { ...selectedAlgorithms });
    
    setSelectedAlgorithms(updatedSelection);
  };

  // 取消选择全部算法
  const deselectAllAlgorithms = () => {
    const updatedSelection = Object.keys(selectedAlgorithms).reduce((acc, key) => {
      acc[key] = false;
      return acc;
    }, { ...selectedAlgorithms });
    
    setSelectedAlgorithms(updatedSelection);
  };

  // 算法分组
  const algorithmGroups = [
    { title: 'SHA-1/2 算法', algorithms: ['sha1', 'sha224', 'sha256', 'sha384', 'sha512'] },
    { title: 'SHA-3 算法', algorithms: ['sha3_224', 'sha3_256', 'sha3_384', 'sha3_512'] }
  ];

  return (
    <div className="min-h-screen bg-background p-6">
      <div className="max-w-6xl mx-auto">
        {/* 返回按钮 */}
        <button 
          onClick={() => router.back()}
          className="mb-6 flex items-center gap-2 text-primary hover:underline"
        >
          <ArrowLeft size={16} />
          <span>返回工具列表</span>
        </button>

        {/* 工具标题 */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold">SHA哈希计算器</h1>
          <p className="text-muted-foreground mt-2">
            计算SHA1、SHA256、SHA512等多种哈希算法，提供结果对比
          </p>
        </div>

        {/* 算法选择 */}
        <div className="mb-6 bg-card border border-border rounded-lg p-4">
          <div className="flex justify-between items-center mb-4">
            <h2 className="font-medium">选择哈希算法</h2>
            <div className="flex gap-2">
              <button 
                onClick={selectAllAlgorithms}
                className="text-xs px-2 py-1 rounded bg-accent hover:bg-accent/80 text-accent-foreground"
              >
                全选
              </button>
              <button 
                onClick={deselectAllAlgorithms}
                className="text-xs px-2 py-1 rounded bg-accent hover:bg-accent/80 text-accent-foreground"
              >
                取消全选
              </button>
            </div>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {algorithmGroups.map((group) => (
              <div key={group.title}>
                <h3 className="text-sm font-medium mb-2">{group.title}</h3>
                <div className="flex flex-wrap gap-2">
                  {group.algorithms.map((algorithm) => (
                    <button
                      key={algorithm}
                      onClick={() => toggleAlgorithm(algorithm)}
                      className={`px-3 py-1 rounded-full text-xs ${
                        selectedAlgorithms[algorithm as keyof typeof selectedAlgorithms]
                          ? 'bg-primary text-primary-foreground'
                          : 'bg-accent text-accent-foreground'
                      }`}
                    >
                      {algorithm.toUpperCase().replace('_', '-')}
                    </button>
                  ))}
                </div>
              </div>
            ))}
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
          {/* 输入区 */}
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <h2 className="font-medium">输入文本</h2>
              <div className="flex items-center gap-2">
                <button 
                  onClick={clearInput}
                  className="text-xs px-2 py-1 rounded bg-accent hover:bg-accent/80 text-accent-foreground"
                >
                  清空
                </button>
                <button 
                  onClick={loadExample}
                  className="text-xs px-2 py-1 rounded bg-accent hover:bg-accent/80 text-accent-foreground"
                >
                  加载示例
                </button>
                <button 
                  onClick={() => copyToClipboard(input)}
                  className="p-1 rounded hover:bg-accent"
                  title="复制"
                >
                  <Copy size={14} />
                </button>
              </div>
            </div>
            <textarea
              value={input}
              onChange={(e) => setInput(e.target.value)}
              placeholder="在此输入需要计算哈希的文本..."
              className="w-full h-[300px] p-4 font-mono text-sm rounded-lg bg-accent border border-border focus:outline-none focus:ring-2 focus:ring-primary resize-none"
            ></textarea>
            {error && (
              <div className="flex items-center gap-2 text-red-500 text-sm">
                <AlertTriangle size={14} />
                <span>{error}</span>
              </div>
            )}
          </div>

          {/* 结果区 */}
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <h2 className="font-medium">哈希结果</h2>
              {Object.keys(results).length > 0 && (
                <button 
                  onClick={copyAllResults}
                  className="text-xs px-2 py-1 rounded bg-primary hover:bg-primary/90 text-primary-foreground flex items-center gap-1"
                >
                  <Copy size={14} />
                  <span>复制所有结果</span>
                </button>
              )}
            </div>
            <div className="h-[300px] overflow-y-auto rounded-lg bg-accent border border-border p-4">
              {Object.keys(results).length > 0 ? (
                <div className="space-y-3">
                  {Object.entries(results).map(([algorithm, hash]) => (
                    <div key={algorithm} className="bg-card p-3 rounded-md">
                      <div className="flex justify-between items-center mb-1">
                        <span className="font-medium text-sm">{algorithm.toUpperCase().replace('_', '-')}</span>
                        <button 
                          onClick={() => copyToClipboard(hash)}
                          className="p-1 rounded hover:bg-accent"
                          title="复制"
                        >
                          <Copy size={14} />
                        </button>
                      </div>
                      <div className="font-mono text-xs break-all">{hash}</div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="h-full flex items-center justify-center text-center text-muted-foreground">
                  <div>
                    <Hash size={28} className="mx-auto mb-2 opacity-40" />
                    <p>计算结果将显示在这里</p>
                    <p className="text-xs mt-1">点击下方按钮开始计算哈希值</p>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* 计算按钮 */}
        <div className="flex justify-center mb-8">
          <button
            onClick={calculateHashes}
            disabled={loading || !input.trim() || !Object.values(selectedAlgorithms).some(Boolean)}
            className="h-12 px-8 rounded-lg bg-primary text-primary-foreground hover:bg-primary/90 flex items-center gap-3 disabled:opacity-70 disabled:cursor-not-allowed"
          >
            {loading ? (
              <>
                <span className="animate-spin h-5 w-5 border-2 border-white border-t-transparent rounded-full"></span>
                <span>计算中...</span>
              </>
            ) : (
              <>
                <Hash size={18} />
                <span>计算哈希值</span>
              </>
            )}
          </button>
        </div>

        {/* 信息说明 */}
        <div className="bg-card border border-border rounded-lg p-6">
          <h3 className="font-medium mb-4">关于SHA哈希算法</h3>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h4 className="font-medium mb-2">SHA-1/2 系列</h4>
              <ul className="list-disc list-inside mb-4 text-muted-foreground text-sm space-y-1">
                <li>SHA-1: 160位输出，已被认为不安全</li>
                <li>SHA-224: 224位输出，基于SHA-2</li>
                <li>SHA-256: 256位输出，最常用的SHA-2变体</li>
                <li>SHA-384: 384位输出，基于SHA-2</li>
                <li>SHA-512: 512位输出，最安全的SHA-2变体</li>
              </ul>
            </div>
            
            <div>
              <h4 className="font-medium mb-2">SHA-3 系列</h4>
              <ul className="list-disc list-inside mb-4 text-muted-foreground text-sm space-y-1">
                <li>SHA3-224: 224位输出，基于KECCAK算法</li>
                <li>SHA3-256: 256位输出，基于KECCAK算法</li>
                <li>SHA3-384: 384位输出，基于KECCAK算法</li>
                <li>SHA3-512: 512位输出，基于KECCAK算法</li>
                <li>SHA-3采用全新设计，比SHA-2更抗量子计算</li>
              </ul>
            </div>
          </div>
          
          <div className="mt-4 text-sm text-muted-foreground">
            <p>哈希算法使用提示：</p>
            <ul className="list-disc list-inside mt-2 space-y-1">
              <li>哈希是单向函数，无法从哈希值还原原始数据</li>
              <li>相同的输入始终产生相同的哈希值</li>
              <li>输入的微小变化会导致哈希值的巨大变化</li>
              <li>SHA-1已被证明存在碰撞，不建议用于安全场景</li>
              <li>SHA-256是目前最常用的算法，平衡了安全性和性能</li>
              <li>SHA-512提供更高的安全性，但计算开销更大</li>
              <li>推荐安全关键场景使用SHA-256或更高位数的算法</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
}
