'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { ArrowLeft, Upload, Copy, Trash, Check, BarChart } from 'lucide-react';

type CountResult = {
  chars: number;
  charsNoSpace: number;
  words: number;
  lines: number;
  paragraphs: number;
  chineseChars: number;
  englishWords: number;
  numbers: number;
  punctuation: number;
};

export default function WordCountPage() {
  const [text, setText] = useState<string>('');
  const [result, setResult] = useState<CountResult>({
    chars: 0,
    charsNoSpace: 0,
    words: 0,
    lines: 0,
    paragraphs: 0,
    chineseChars: 0,
    englishWords: 0,
    numbers: 0,
    punctuation: 0
  });
  const [copySuccess, setCopySuccess] = useState<boolean>(false);
  const router = useRouter();
  
  // 示例文本
  const exampleText = `这是一段示例文本，用于演示字数统计工具的功能。
这段文本包含中文、English单词、数字123和标点符号！

另起一个段落，可以测试段落数量统计功能。
总共有4行文本。`;

  // 处理文本输入变化
  const handleTextChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setText(e.target.value);
  };

  // 加载示例
  const loadExample = () => {
    setText(exampleText);
  };

  // 清空输入
  const clearText = () => {
    setText('');
  };

  // 处理文件上传
  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    const reader = new FileReader();
    reader.onload = (e) => {
      const content = e.target?.result as string;
      setText(content);
    };
    reader.readAsText(file);
  };

  // 复制统计结果
  const copyResult = () => {
    const resultText = `字符数（含空格）：${result.chars}
字符数（不含空格）：${result.charsNoSpace}
单词数：${result.words}
行数：${result.lines}
段落数：${result.paragraphs}
中文字符数：${result.chineseChars}
英文单词数：${result.englishWords}
数字个数：${result.numbers}
标点符号数：${result.punctuation}`;

    navigator.clipboard.writeText(resultText)
      .then(() => {
        setCopySuccess(true);
        setTimeout(() => setCopySuccess(false), 2000);
      })
      .catch(err => {
        console.error('复制失败:', err);
      });
  };

  // 计算统计结果
  const countText = (text: string): CountResult => {
    if (!text) {
      return {
        chars: 0,
        charsNoSpace: 0,
        words: 0,
        lines: 0,
        paragraphs: 0,
        chineseChars: 0,
        englishWords: 0,
        numbers: 0,
        punctuation: 0
      };
    }

    // 总字符数（包含空格）
    const chars = text.length;
    
    // 字符数（不含空格）
    const charsNoSpace = text.replace(/\s/g, '').length;
    
    // 单词数（中英文单词、数字等）
    const words = text.trim().split(/\s+/).length;
    
    // 行数
    const lines = text.split(/\n/).length;
    
    // 段落数（空行分隔的段落）
    const paragraphs = text.split(/\n\s*\n/).filter(Boolean).length || 1;
    
    // 中文字符数
    const chineseChars = (text.match(/[\u4e00-\u9fa5]/g) || []).length;
    
    // 英文单词数
    const englishWordsMatch = text.match(/[a-zA-Z]+/g);
    const englishWords = englishWordsMatch ? englishWordsMatch.length : 0;
    
    // 数字个数
    const numbersMatch = text.match(/\d+/g);
    const numbers = numbersMatch ? numbersMatch.length : 0;
    
    // 标点符号数
    const punctuationMatch = text.match(/[!"#$%&'()*+,-./:;<=>?@[\\\]^_`{|}~。，、；：？！""''（）【】《》]/g);
    const punctuation = punctuationMatch ? punctuationMatch.length : 0;

    return {
      chars,
      charsNoSpace,
      words,
      lines,
      paragraphs,
      chineseChars,
      englishWords,
      numbers,
      punctuation
    };
  };

  // 文本变化时重新计算
  useEffect(() => {
    const timer = setTimeout(() => {
      setResult(countText(text));
    }, 300);
    
    return () => clearTimeout(timer);
  }, [text]);

  return (
    <div className="container mx-auto p-6">
      {/* 头部 */}
      <div className="flex items-center mb-6">
        <button 
          onClick={() => router.back()}
          className="p-2 mr-4 rounded-full hover:bg-gray-100 dark:hover:bg-gray-800"
        >
          <ArrowLeft size={24} />
        </button>
        <h1 className="text-2xl font-bold">字数统计工具</h1>
      </div>

      {/* 工具说明 */}
      <div className="mb-8 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
        <h2 className="text-lg font-medium mb-2">工具说明</h2>
        <p className="text-gray-700 dark:text-gray-300">
          本工具可以帮助您统计文本的字符数、单词数、行数和段落数等信息。支持中英文混合统计，
          可以区分中文字符、英文单词、数字和标点符号。所有处理都在浏览器本地完成，不会上传您的文本。
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {/* 输入区域 */}
        <div className="md:col-span-2 space-y-4">
          <div className="flex justify-between items-center mb-2">
            <h2 className="text-lg font-medium">输入文本</h2>
            <div className="flex space-x-2">
              <button
                onClick={loadExample}
                className="text-xs p-1 px-2 rounded bg-gray-100 hover:bg-gray-200 dark:bg-gray-800 dark:hover:bg-gray-700"
              >
                加载示例
              </button>
              <button
                onClick={clearText}
                className="text-xs p-1 px-2 rounded bg-gray-100 hover:bg-gray-200 dark:bg-gray-800 dark:hover:bg-gray-700 flex items-center"
              >
                <Trash size={12} className="mr-1" />
                清空
              </button>
              <label className="text-xs p-1 px-2 rounded bg-gray-100 hover:bg-gray-200 dark:bg-gray-800 dark:hover:bg-gray-700 flex items-center cursor-pointer">
                <Upload size={12} className="mr-1" />
                上传文件
                <input
                  type="file"
                  accept=".txt,.md,.csv,.json,.html,.xml,.js,.css,.ts"
                  onChange={handleFileUpload}
                  className="hidden"
                />
              </label>
            </div>
          </div>
          
          <textarea
            value={text}
            onChange={handleTextChange}
            placeholder="在此输入或粘贴文本..."
            className="w-full h-[400px] p-4 border border-gray-300 dark:border-gray-700 rounded-lg bg-white dark:bg-gray-900 focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
        </div>

        {/* 统计结果 */}
        <div className="space-y-4">
          <div className="flex justify-between items-center mb-2">
            <h2 className="text-lg font-medium">统计结果</h2>
            <button
              onClick={copyResult}
              className="text-xs p-1 px-2 rounded bg-gray-100 hover:bg-gray-200 dark:bg-gray-800 dark:hover:bg-gray-700 flex items-center"
              disabled={!text}
            >
              {copySuccess ? <Check size={12} className="mr-1" /> : <Copy size={12} className="mr-1" />}
              {copySuccess ? '已复制' : '复制结果'}
            </button>
          </div>
          
          <div className="bg-white dark:bg-gray-900 border border-gray-300 dark:border-gray-700 rounded-lg p-4">
            <div className="grid grid-cols-1 gap-3">
              <div className="flex justify-between items-center py-2 border-b border-gray-200 dark:border-gray-700">
                <span>字符数（含空格）</span>
                <span className="text-blue-600 dark:text-blue-400 font-medium">{result.chars}</span>
              </div>
              <div className="flex justify-between items-center py-2 border-b border-gray-200 dark:border-gray-700">
                <span>字符数（不含空格）</span>
                <span className="text-blue-600 dark:text-blue-400 font-medium">{result.charsNoSpace}</span>
              </div>
              <div className="flex justify-between items-center py-2 border-b border-gray-200 dark:border-gray-700">
                <span>单词数</span>
                <span className="text-blue-600 dark:text-blue-400 font-medium">{result.words}</span>
              </div>
              <div className="flex justify-between items-center py-2 border-b border-gray-200 dark:border-gray-700">
                <span>行数</span>
                <span className="text-blue-600 dark:text-blue-400 font-medium">{result.lines}</span>
              </div>
              <div className="flex justify-between items-center py-2 border-b border-gray-200 dark:border-gray-700">
                <span>段落数</span>
                <span className="text-blue-600 dark:text-blue-400 font-medium">{result.paragraphs}</span>
              </div>
            </div>
          </div>
          
          {/* 详细统计 */}
          <div className="bg-white dark:bg-gray-900 border border-gray-300 dark:border-gray-700 rounded-lg p-4">
            <h3 className="text-base font-medium mb-3 flex items-center">
              <BarChart size={16} className="mr-2" />
              详细统计
            </h3>
            <div className="grid grid-cols-1 gap-3">
              <div className="flex justify-between items-center py-2 border-b border-gray-200 dark:border-gray-700">
                <span>中文字符</span>
                <span className="text-green-600 dark:text-green-400 font-medium">{result.chineseChars}</span>
              </div>
              <div className="flex justify-between items-center py-2 border-b border-gray-200 dark:border-gray-700">
                <span>英文单词</span>
                <span className="text-green-600 dark:text-green-400 font-medium">{result.englishWords}</span>
              </div>
              <div className="flex justify-between items-center py-2 border-b border-gray-200 dark:border-gray-700">
                <span>数字</span>
                <span className="text-green-600 dark:text-green-400 font-medium">{result.numbers}</span>
              </div>
              <div className="flex justify-between items-center py-2">
                <span>标点符号</span>
                <span className="text-green-600 dark:text-green-400 font-medium">{result.punctuation}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
} 