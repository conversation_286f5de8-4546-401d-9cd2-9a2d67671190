'use client';

import { useState, useRef } from 'react';
import { useRouter } from 'next/navigation';
import { ArrowLeft, Upload, Download, RefreshCw, Image, FileType, Settings, Trash } from 'lucide-react';
import { useLocale, useTranslations } from 'next-intl';

interface CompressionOptions {
  quality: number;
  maxWidth: number;
  maxHeight: number;
  format: 'jpeg' | 'png' | 'webp';
  maintainAspectRatio: boolean;
}

export default function ImageCompressPage() {
  const locale = useLocale();
  const t = useTranslations('tools.imgCompress');
  const [files, setFiles] = useState<File[]>([]);
  const [processedFiles, setProcessedFiles] = useState<Array<{
    originalFile: File;
    compressedUrl: string;
    originalSize: number;
    compressedSize: number;
    compressionRatio: number;
  }>>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [options, setOptions] = useState<CompressionOptions>({
    quality: 80,
    maxWidth: 1920,
    maxHeight: 1080,
    format: 'webp',
    maintainAspectRatio: true
  });
  const fileInputRef = useRef<HTMLInputElement>(null);
  const router = useRouter();

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      const selectedFiles = Array.from(e.target.files);
      
      // 过滤出图片文件
      const imageFiles = selectedFiles.filter(file => 
        file.type.startsWith('image/')
      );
      
      if (imageFiles.length === 0) {
        setError(t('errors.invalidFiles'));
        return;
      }
      
      setFiles(prev => [...prev, ...imageFiles]);
      setError(null);
    }
  };

  const calculateDimensions = (width: number, height: number) => {
    if (!options.maintainAspectRatio) {
      return {
        width: Math.min(width, options.maxWidth),
        height: Math.min(height, options.maxHeight)
      };
    }

    const ratio = width / height;
    let newWidth = width;
    let newHeight = height;

    if (width > options.maxWidth) {
      newWidth = options.maxWidth;
      newHeight = Math.round(newWidth / ratio);
    }

    if (newHeight > options.maxHeight) {
      newHeight = options.maxHeight;
      newWidth = Math.round(newHeight * ratio);
    }

    return { width: newWidth, height: newHeight };
  };

  const compressImage = async (file: File): Promise<{
    compressedUrl: string;
    compressedSize: number;
  }> => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.readAsDataURL(file);
      reader.onload = (event) => {
        const img = document.createElement('img');
        img.src = event.target?.result as string;
        img.onload = () => {
          const canvas = document.createElement('canvas');
          const ctx = canvas.getContext('2d');
          
          if (!ctx) {
            reject(new Error(t('errors.canvasContextFailed')));
            return;
          }

          // 计算新的尺寸
          const { width, height } = calculateDimensions(img.width, img.height);
          
          // 设置画布尺寸
          canvas.width = width;
          canvas.height = height;
          
          // 使用双线性插值算法
          ctx.imageSmoothingEnabled = true;
          ctx.imageSmoothingQuality = 'high';
          
          // 绘制图像
          ctx.drawImage(img, 0, 0, width, height);
          
          // 根据选择的格式进行压缩
          const mimeType = `image/${options.format}`;
          canvas.toBlob(
            (blob) => {
              if (!blob) {
                reject(new Error(t('errors.compressionFailed')));
                return;
              }
              
              const compressedUrl = URL.createObjectURL(blob);
              resolve({
                compressedUrl,
                compressedSize: blob.size,
              });
            },
            mimeType,
            options.quality / 100
          );
        };
        img.onerror = () => {
          reject(new Error(t('errors.imageLoadFailed')));
        };
      };
      reader.onerror = () => {
        reject(new Error(t('errors.fileReadFailed')));
      };
    });
  };

  const handleCompression = async () => {
    if (files.length === 0) {
      setError(t('errors.noFiles'));
      return;
    }

    setLoading(true);
    setError(null);
    
    try {
      const results = [];
      
      for (const file of files) {
        try {
          const { compressedUrl, compressedSize } = await compressImage(file);
          
          // 计算压缩比例
          const originalSize = file.size;
          const compressionRatio = ((originalSize - compressedSize) / originalSize) * 100;
          
          results.push({
            originalFile: file,
            compressedUrl,
            originalSize,
            compressedSize,
            compressionRatio
          });
        } catch (err) {
          console.error(`Processing file ${file.name} failed:`, err);
        }
      }
      
      setProcessedFiles(results);
    } catch (err) {
      setError(t('errors.compressionError'));
      console.error(err);
    } finally {
      setLoading(false);
    }
  };

  const downloadCompressedImage = (url: string, fileName: string) => {
    const a = document.createElement('a');
    a.href = url;
    const extension = options.format === 'jpeg' ? 'jpg' : options.format;
    a.download = `compressed_${fileName.replace(/\.[^/.]+$/, '')}.${extension}`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
  };

  const formatFileSize = (bytes: number): string => {
    if (bytes < 1024) return bytes + ' B';
    else if (bytes < 1024 * 1024) return (bytes / 1024).toFixed(2) + ' KB';
    else return (bytes / (1024 * 1024)).toFixed(2) + ' MB';
  };

  const removeFile = (index: number) => {
    setFiles(prev => prev.filter((_, i) => i !== index));
  };

  const removeAllFiles = () => {
    setFiles([]);
    setProcessedFiles([]);
  };

  const navigateBack = () => {
    router.back();
  };

  const triggerFileInput = () => {
    fileInputRef.current?.click();
  };

  return (
    <div className="container mx-auto p-6">
      {/* 头部 */}
      <div className="flex items-center mb-6">
        <button 
          onClick={navigateBack}
          className="p-2 mr-4 rounded-full hover:bg-gray-100 dark:hover:bg-gray-800"
        >
          <ArrowLeft size={24} />
        </button>
        <h1 className="text-2xl font-bold">{t('title')}</h1>
      </div>

      {/* 工具说明 */}
      <div className="mb-8 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
        <h2 className="text-lg font-medium mb-2">{t('toolDescription.title')}</h2>
        <p className="text-gray-700 dark:text-gray-300">
          {t('toolDescription.content')}
        </p>
      </div>

      {/* 压缩设置 */}
      <div className="mb-6 p-4 border border-gray-200 dark:border-gray-700 rounded-lg">
        <div className="flex items-center mb-2">
          <Settings size={18} className="mr-2" />
          <h2 className="text-lg font-medium">{t('compressionSettings.title')}</h2>
        </div>
        
        <div className="space-y-4">
          {/* 质量设置 */}
          <div>
            <label className="block mb-2 text-sm font-medium">
              {t('compressionSettings.quality')}: {options.quality}%
            </label>
            <input
              type="range"
              min="1"
              max="100"
              value={options.quality}
              onChange={(e) => setOptions(prev => ({ ...prev, quality: parseInt(e.target.value) }))}
              className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer dark:bg-gray-700"
            />
            <div className="flex justify-between text-xs text-gray-500 mt-1">
              <span>{t('compressionSettings.lowQuality')}</span>
              <span>{t('compressionSettings.highQuality')}</span>
            </div>
          </div>

          {/* 最大宽度设置 */}
          <div>
            <label className="block mb-2 text-sm font-medium">
              {t('compressionSettings.maxWidth')}: {options.maxWidth}px
            </label>
            <input
              type="range"
              min="100"
              max="3840"
              step="100"
              value={options.maxWidth}
              onChange={(e) => setOptions(prev => ({ ...prev, maxWidth: parseInt(e.target.value) }))}
              className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer dark:bg-gray-700"
            />
            <div className="flex justify-between text-xs text-gray-500 mt-1">
              <span>100px</span>
              <span>3840px</span>
            </div>
          </div>

          {/* 最大高度设置 */}
          <div>
            <label className="block mb-2 text-sm font-medium">
              {t('compressionSettings.maxHeight')}: {options.maxHeight}px
            </label>
            <input
              type="range"
              min="100"
              max="2160"
              step="100"
              value={options.maxHeight}
              onChange={(e) => setOptions(prev => ({ ...prev, maxHeight: parseInt(e.target.value) }))}
              className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer dark:bg-gray-700"
            />
            <div className="flex justify-between text-xs text-gray-500 mt-1">
              <span>100px</span>
              <span>2160px</span>
            </div>
          </div>

          {/* 格式选择 */}
          <div>
            <label className="block mb-2 text-sm font-medium">
              {t('compressionSettings.format')}
            </label>
            <select
              value={options.format}
              onChange={(e) => setOptions(prev => ({ ...prev, format: e.target.value as 'jpeg' | 'png' | 'webp' }))}
              className="w-full p-2 border border-gray-300 dark:border-gray-700 rounded-lg bg-white dark:bg-gray-800"
            >
              <option value="webp">WebP (推荐)</option>
              <option value="jpeg">JPEG</option>
              <option value="png">PNG</option>
            </select>
          </div>

          {/* 保持宽高比选项 */}
          <div className="flex items-center">
            <input
              type="checkbox"
              id="maintainAspectRatio"
              checked={options.maintainAspectRatio}
              onChange={(e) => setOptions(prev => ({ ...prev, maintainAspectRatio: e.target.checked }))}
              className="mr-2"
            />
            <label htmlFor="maintainAspectRatio" className="text-sm font-medium">
              {t('compressionSettings.maintainAspectRatio')}
            </label>
          </div>
        </div>
      </div>

      {/* 文件上传区域 */}
      <div 
        className="mb-6 p-8 border-2 border-dashed border-gray-300 dark:border-gray-700 rounded-lg text-center cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-800/50 transition-colors"
        onClick={triggerFileInput}
      >
        <input
          type="file"
          ref={fileInputRef}
          onChange={handleFileChange}
          multiple
          accept="image/*"
          className="hidden"
        />
        <Upload size={36} className="mx-auto mb-4 text-gray-400" />
        <h3 className="text-lg font-medium mb-2">{t('uploadArea.title')}</h3>
        <p className="text-sm text-gray-500 dark:text-gray-400">
          {t('uploadArea.supportedFormats')}
        </p>
      </div>

      {/* 错误信息 */}
      {error && (
        <div className="mb-6 p-4 bg-red-50 dark:bg-red-900/20 text-red-600 dark:text-red-400 rounded-lg">
          {error}
        </div>
      )}

      {/* 文件列表 */}
      {files.length > 0 && (
        <div className="mb-6">
          <div className="flex justify-between items-center mb-4">
            <h3 className="text-lg font-medium">{t('fileList.title')} ({files.length})</h3>
            <button
              onClick={removeAllFiles}
              className="flex items-center text-red-500 hover:text-red-700 text-sm"
            >
              <Trash size={16} className="mr-1" />
              {t('fileList.removeAll')}
            </button>
          </div>
          
          <div className="space-y-3">
            {files.map((file, index) => (
              <div key={index} className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                <div className="flex items-center">
                  <div className="p-2 mr-3 bg-white dark:bg-gray-700 rounded">
                    <FileType size={20} className="text-blue-500" />
                  </div>
                  <div>
                    <p className="font-medium truncate max-w-xs">{file.name}</p>
                    <p className="text-sm text-gray-500">{formatFileSize(file.size)}</p>
                  </div>
                </div>
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    removeFile(index);
                  }}
                  className="p-1 hover:bg-gray-200 dark:hover:bg-gray-700 rounded-full"
                >
                  <Trash size={16} className="text-red-500" />
                </button>
              </div>
            ))}
          </div>
          
          <button
            onClick={handleCompression}
            disabled={loading}
            className={`mt-4 w-full py-3 px-4 flex items-center justify-center rounded-lg ${
              loading
                ? 'bg-blue-400 cursor-not-allowed'
                : 'bg-blue-500 hover:bg-blue-600'
            } text-white transition-colors`}
          >
            {loading ? (
              <>
                <RefreshCw size={18} className="animate-spin mr-2" />
                {t('buttons.processing')}
              </>
            ) : (
              <>
                <Image size={18} className="mr-2" />
                {t('buttons.startCompression')}
              </>
            )}
          </button>
        </div>
      )}

      {/* 压缩结果 */}
      {processedFiles.length > 0 && (
        <div className="mt-10">
          <h3 className="text-xl font-medium mb-4">{t('results.title')}</h3>
          
          <div className="space-y-6">
            {processedFiles.map((item, index) => (
              <div key={index} className="border border-gray-200 dark:border-gray-700 rounded-lg overflow-hidden">
                <div className="p-4 bg-gray-50 dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700">
                  <h4 className="font-medium">{item.originalFile.name}</h4>
                </div>
                
                <div className="p-4 grid grid-cols-1 md:grid-cols-2 gap-4">
                  {/* 图片预览 */}
                  <div className="aspect-video bg-gray-100 dark:bg-gray-800 rounded flex items-center justify-center overflow-hidden">
                    <img
                      src={item.compressedUrl}
                      alt={t('results.compressedImageAlt', { filename: item.originalFile.name })}
                      className="object-contain max-w-full max-h-full"
                    />
                  </div>
                  
                  {/* 压缩信息 */}
                  <div className="flex flex-col justify-center">
                    <div className="grid grid-cols-2 gap-4 mb-4">
                      <div>
                        <p className="text-sm text-gray-500 mb-1">{t('results.originalSize')}</p>
                        <p className="font-medium">{formatFileSize(item.originalSize)}</p>
                      </div>
                      <div>
                        <p className="text-sm text-gray-500 mb-1">{t('results.compressedSize')}</p>
                        <p className="font-medium">{formatFileSize(item.compressedSize)}</p>
                      </div>
                      <div>
                        <p className="text-sm text-gray-500 mb-1">{t('results.compressionRatio')}</p>
                        <p className="font-medium">{item.compressionRatio.toFixed(2)}%</p>
                      </div>
                      <div>
                        <p className="text-sm text-gray-500 mb-1">{t('results.qualitySetting')}</p>
                        <p className="font-medium">{options.quality}%</p>
                      </div>
                    </div>
                    
                    <button
                      onClick={() => downloadCompressedImage(item.compressedUrl, item.originalFile.name)}
                      className="py-2 px-4 bg-green-500 hover:bg-green-600 text-white rounded flex items-center justify-center"
                    >
                      <Download size={18} className="mr-2" />
                      {t('buttons.downloadCompressed')}
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
} 