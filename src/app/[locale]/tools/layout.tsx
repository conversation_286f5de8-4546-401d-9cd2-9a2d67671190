'use client';

import { useCallback, useEffect, useState } from 'react';
import { useRouter, usePathname } from 'next/navigation';
import { ArrowLeft } from 'lucide-react';
import { isToolDeveloped } from '@/lib/tools/utils';

export default function ToolsLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  const router = useRouter();
  const pathname = usePathname();
  const [toolName, setToolName] = useState<string>('');

  // 根据路径获取工具名称
  useEffect(() => {
    if (pathname) {
      const segments = pathname.split('/');
      const toolSegment = segments[segments.length - 1];
      
      // 检查工具是否已开发
      if (toolSegment && !isToolDeveloped(toolSegment) && segments.length > 3) {
        router.push(`/${segments[1]}/tools`);
        return;
      }
      
      const toolNames: Record<string, string> = {
        'json-format': 'JSON格式化',
        'base64': 'Base64编解码',
        'md5': 'MD5加密',
        'sha256': 'SHA256加密',
        'url-encode': 'URL编解码',
        'color-converter': '颜色格式转换',
        'random-ip': '随机IP生成器',
        'qrcode': '二维码生成器',
        'img-resize': '图片调整大小'
      };
      
      setToolName(toolNames[toolSegment] || '工具详情');
    }
  }, [pathname, router]);

  const handleBack = useCallback(() => {
    router.back();
  }, [router]);

  return (
    <div className="min-h-screen bg-background pb-12">
      <div className="max-w-screen-2xl mx-auto">
        {children}
      </div>
    </div>
  );
} 