'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { ArrowLeft, Copy, Upload, Trash, Check, Download, RefreshCw } from 'lucide-react';

type Mode = 'encode' | 'decode';

export default function UrlEncodePage() {
  const router = useRouter();
  const [mode, setMode] = useState<Mode>('encode');
  const [input, setInput] = useState<string>('');
  const [output, setOutput] = useState<string>('');
  const [copySuccess, setCopySuccess] = useState<boolean>(false);

  // 示例文本
  const exampleText = 'https://wenhaofree.com/path?name=张三&age=25&tags=web,开发,工具';

  // 加载示例
  const loadExample = () => {
    setInput(exampleText);
  };

  // 清空输入
  const clearInput = () => {
    setInput('');
    setOutput('');
  };

  // 处理文本输入变化
  const handleTextChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setInput(e.target.value);
  };

  // 处理文件上传
  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    const reader = new FileReader();
    reader.onload = (e) => {
      const content = e.target?.result as string;
      setInput(content);
    };
    reader.readAsText(file);
  };

  // 复制结果
  const copyResult = () => {
    navigator.clipboard.writeText(output)
      .then(() => {
        setCopySuccess(true);
        setTimeout(() => setCopySuccess(false), 2000);
      })
      .catch(err => {
        console.error('复制失败:', err);
      });
  };

  // 下载结果
  const downloadResult = () => {
    if (!output) return;

    const downloadData = `data:text/plain;charset=utf-8,${encodeURIComponent(output)}`;
    const link = document.createElement('a');
    link.href = downloadData;
    link.download = mode === 'encode' ? 'encoded-url.txt' : 'decoded-url.txt';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  // 转换处理
  useEffect(() => {
    if (!input) {
      setOutput('');
      return;
    }

    try {
      if (mode === 'encode') {
        // URL编码
        setOutput(encodeURIComponent(input));
      } else {
        // URL解码
        setOutput(decodeURIComponent(input));
      }
    } catch (error) {
      setOutput(`处理失败: ${error instanceof Error ? error.message : String(error)}`);
    }
  }, [input, mode]);

  return (
    <div className="container mx-auto p-6">
      {/* 头部 */}
      <div className="flex items-center mb-6">
        <button 
          onClick={() => router.back()}
          className="p-2 mr-4 rounded-full hover:bg-gray-100 dark:hover:bg-gray-800"
        >
          <ArrowLeft size={24} />
        </button>
        <h1 className="text-2xl font-bold">URL编解码工具</h1>
      </div>

      {/* 工具说明 */}
      <div className="mb-8 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
        <h2 className="text-lg font-medium mb-2">工具说明</h2>
        <p className="text-gray-700 dark:text-gray-300">
          URL编码（也称为百分号编码）是一种将特殊字符转换为URL安全格式的方法。
          本工具支持URL编码和解码，可以帮助您处理包含特殊字符的URL，所有处理都在浏览器本地完成，
          不会上传您的数据。
        </p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* 左侧：输入区域 */}
        <div className="space-y-4">
          <div className="flex justify-between items-center mb-2">
            <h2 className="text-lg font-medium">输入区域</h2>
            <div className="flex space-x-2">
              <button
                onClick={loadExample}
                className="text-xs p-1 px-2 rounded bg-gray-100 hover:bg-gray-200 dark:bg-gray-800 dark:hover:bg-gray-700"
              >
                加载示例
              </button>
              <button
                onClick={clearInput}
                className="text-xs p-1 px-2 rounded bg-gray-100 hover:bg-gray-200 dark:bg-gray-800 dark:hover:bg-gray-700 flex items-center"
              >
                <Trash size={12} className="mr-1" />
                清空
              </button>
              <label className="text-xs p-1 px-2 rounded bg-gray-100 hover:bg-gray-200 dark:bg-gray-800 dark:hover:bg-gray-700 flex items-center cursor-pointer">
                <Upload size={12} className="mr-1" />
                上传文件
                <input
                  type="file"
                  accept=".txt,.md,.csv,.json,.html,.xml,.js,.css,.ts"
                  onChange={handleFileUpload}
                  className="hidden"
                />
              </label>
            </div>
          </div>

          {/* 模式选择 */}
          <div className="flex mb-4 bg-white dark:bg-gray-900 rounded-lg p-2 border border-gray-300 dark:border-gray-700">
            <button
              onClick={() => setMode('encode')}
              className={`flex-1 py-2 rounded-md ${
                mode === 'encode'
                  ? 'bg-blue-500 text-white'
                  : 'bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-300'
              }`}
            >
              编码
            </button>
            <button
              onClick={() => setMode('decode')}
              className={`flex-1 py-2 rounded-md ${
                mode === 'decode'
                  ? 'bg-blue-500 text-white'
                  : 'bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-300'
              }`}
            >
              解码
            </button>
          </div>

          <textarea
            value={input}
            onChange={handleTextChange}
            placeholder={mode === 'encode' ? "在此输入要编码的URL..." : "在此输入要解码的URL..."}
            className="w-full h-[300px] p-4 border border-gray-300 dark:border-gray-700 rounded-lg bg-white dark:bg-gray-900 focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
        </div>

        {/* 右侧：输出区域 */}
        <div className="space-y-4">
          <div className="flex justify-between items-center mb-2">
            <h2 className="text-lg font-medium">输出结果</h2>
            <div className="flex space-x-2">
              <button
                onClick={copyResult}
                className="text-xs p-1 px-2 rounded bg-gray-100 hover:bg-gray-200 dark:bg-gray-800 dark:hover:bg-gray-700 flex items-center"
                disabled={!output}
              >
                {copySuccess ? <Check size={12} className="mr-1" /> : <Copy size={12} className="mr-1" />}
                {copySuccess ? '已复制' : '复制结果'}
              </button>
              <button
                onClick={downloadResult}
                className="text-xs p-1 px-2 rounded bg-gray-100 hover:bg-gray-200 dark:bg-gray-800 dark:hover:bg-gray-700 flex items-center"
                disabled={!output}
              >
                <Download size={12} className="mr-1" />
                下载结果
              </button>
            </div>
          </div>

          <div className="w-full p-4 border border-gray-300 dark:border-gray-700 rounded-lg bg-white dark:bg-gray-900 h-[300px] overflow-auto">
            <pre className="text-sm whitespace-pre-wrap break-all">
              {output || (mode === 'encode' ? '编码结果将显示在这里...' : '解码结果将显示在这里...')}
            </pre>
          </div>
        </div>
      </div>
    </div>
  );
} 