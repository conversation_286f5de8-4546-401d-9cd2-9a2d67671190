'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { ArrowLeft, Copy, Key, Lock, Unlock, FileText, AlertTriangle, RefreshCw } from 'lucide-react';
import crypto from 'crypto';
import { useLocale, useTranslations } from 'next-intl';

export default function AesEncryptionPage() {
  const locale = useLocale();
  const t = useTranslations('tools.aesEncryption');
  
  const [input, setInput] = useState('');
  const [output, setOutput] = useState('');
  const [key, setKey] = useState('');
  const [iv, setIv] = useState('');
  const [mode, setMode] = useState('cbc');
  const [keySize, setKeySize] = useState('256');
  const [action, setAction] = useState<'encrypt' | 'decrypt'>('encrypt');
  const [padding, setPadding] = useState('pkcs7');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const router = useRouter();

  // 支持的加密模式
  const supportedModes = [
    { value: 'cbc', label: 'CBC' },
    { value: 'ecb', label: 'ECB' },
    { value: 'cfb', label: 'CFB' },
    { value: 'ofb', label: 'OFB' },
    { value: 'ctr', label: 'CTR' }
  ];

  // 支持的密钥大小
  const supportedKeySizes = [
    { value: '128', label: t('settings.keySize.128bit') },
    { value: '192', label: t('settings.keySize.192bit') },
    { value: '256', label: t('settings.keySize.256bit') }
  ];

  // 支持的填充模式
  const supportedPaddings = [
    { value: 'pkcs7', label: 'PKCS#7' },
    { value: 'nopadding', label: t('settings.padding.noPadding') }
  ];

  // 生成随机密钥和IV
  const generateRandomKey = () => {
    const randomKey = crypto.randomBytes(parseInt(keySize) / 8).toString('hex');
    setKey(randomKey);
  };

  const generateRandomIv = () => {
    // AES块大小总是128位(16字节)
    const randomIv = crypto.randomBytes(16).toString('hex');
    setIv(randomIv);
  };

  // 加密处理
  const encrypt = () => {
    if (!input.trim()) {
      setError(t('errors.emptyEncryptText'));
      return;
    }

    if (!key.trim()) {
      setError(t('errors.emptyKey'));
      return;
    }

    // 对于需要IV的模式，检查IV
    if (mode !== 'ecb' && !iv.trim()) {
      setError(t('errors.emptyIv'));
      return;
    }

    setLoading(true);
    setError(null);

    try {
      // 准备密钥和IV
      const keyBuffer = Buffer.from(key, 'hex').slice(0, parseInt(keySize) / 8);
      let ivBuffer = undefined;
      
      if (mode !== 'ecb') {
        ivBuffer = Buffer.from(iv, 'hex').slice(0, 16);
      }
      
      // 创建密码器
      const algorithm = `aes-${keySize}-${mode}`;
      const cipher = crypto.createCipheriv(
        algorithm, 
        keyBuffer, 
        ivBuffer || null
      );
      
      // 指定填充
      if (padding === 'nopadding') {
        cipher.setAutoPadding(false);
      }
      
      // 加密数据
      let encrypted = cipher.update(input, 'utf8', 'base64');
      encrypted += cipher.final('base64');
      
      setOutput(encrypted);
    } catch (err) {
      setError(err instanceof Error ? `${t('errors.encryptError')}: ${err.message}` : t('errors.unknownEncryptError'));
    } finally {
      setLoading(false);
    }
  };

  // 解密处理
  const decrypt = () => {
    if (!input.trim()) {
      setError(t('errors.emptyDecryptText'));
      return;
    }

    if (!key.trim()) {
      setError(t('errors.emptyKey'));
      return;
    }

    // 对于需要IV的模式，检查IV
    if (mode !== 'ecb' && !iv.trim()) {
      setError(t('errors.emptyIv'));
      return;
    }

    setLoading(true);
    setError(null);

    try {
      // 准备密钥和IV
      const keyBuffer = Buffer.from(key, 'hex').slice(0, parseInt(keySize) / 8);
      let ivBuffer = undefined;
      
      if (mode !== 'ecb') {
        ivBuffer = Buffer.from(iv, 'hex').slice(0, 16);
      }
      
      // 创建解密器
      const algorithm = `aes-${keySize}-${mode}`;
      const decipher = crypto.createDecipheriv(
        algorithm, 
        keyBuffer, 
        ivBuffer || null
      );
      
      // 指定填充
      if (padding === 'nopadding') {
        decipher.setAutoPadding(false);
      }
      
      // 解密数据
      let decrypted = decipher.update(input, 'base64', 'utf8');
      decrypted += decipher.final('utf8');
      
      setOutput(decrypted);
    } catch (err) {
      setError(err instanceof Error ? `${t('errors.decryptError')}: ${err.message}` : t('errors.unknownDecryptError'));
    } finally {
      setLoading(false);
    }
  };

  // 执行加密或解密
  const handleProcess = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (action === 'encrypt') {
      encrypt();
    } else {
      decrypt();
    }
  };

  // 复制到剪贴板
  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
  };

  // 清空输入
  const clearInput = () => {
    setInput('');
    setOutput('');
    setError(null);
  };

  // 加载示例
  const loadExample = () => {
    setInput(action === 'encrypt' ? t('examples.encryptText') : t('examples.decryptText'));
    generateRandomKey();
    generateRandomIv();
    setError(null);
  };

  // 切换加密/解密模式
  const toggleAction = (newAction: 'encrypt' | 'decrypt') => {
    setAction(newAction);
    setInput('');
    setOutput('');
    setError(null);
  };

  return (
    <div className="min-h-screen bg-background p-6">
      <div className="max-w-6xl mx-auto">
        {/* 返回按钮 */}
        <button 
          onClick={() => router.back()}
          className="mb-6 flex items-center gap-2 text-primary hover:underline"
        >
          <ArrowLeft size={16} />
          <span>{t('navigation.back')}</span>
        </button>

        {/* 工具标题 */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold">{t('title')}</h1>
          <p className="text-muted-foreground mt-2">
            {t('description')}
          </p>
        </div>

        {/* 操作切换按钮 */}
        <div className="mb-6 bg-card border border-border rounded-lg p-2 inline-flex">
          <button
            onClick={() => toggleAction('encrypt')}
            className={`py-2 px-4 rounded-md flex items-center gap-2 ${
              action === 'encrypt'
                ? 'bg-primary text-primary-foreground'
                : 'text-muted-foreground hover:bg-accent'
            }`}
          >
            <Lock size={16} />
            <span>{t('modes.encrypt')}</span>
          </button>
          <button
            onClick={() => toggleAction('decrypt')}
            className={`py-2 px-4 rounded-md flex items-center gap-2 ${
              action === 'decrypt'
                ? 'bg-primary text-primary-foreground'
                : 'text-muted-foreground hover:bg-accent'
            }`}
          >
            <Unlock size={16} />
            <span>{t('modes.decrypt')}</span>
          </button>
        </div>

        {/* 加密/解密设置 */}
        <div className="mb-6 bg-card border border-border rounded-lg p-4">
          <h2 className="font-medium mb-4">{t('settings.title')}</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {/* 密钥大小 */}
            <div>
              <label className="block text-sm mb-2">{t('settings.keySize.label')}</label>
              <select
                value={keySize}
                onChange={(e) => setKeySize(e.target.value)}
                className="w-full h-10 px-3 rounded bg-accent border border-border focus:outline-none focus:ring-2 focus:ring-primary"
              >
                {supportedKeySizes.map((size) => (
                  <option key={size.value} value={size.value}>
                    {size.label}
                  </option>
                ))}
              </select>
            </div>
            
            {/* 加密模式 */}
            <div>
              <label className="block text-sm mb-2">{t('settings.mode.label')}</label>
              <select
                value={mode}
                onChange={(e) => setMode(e.target.value)}
                className="w-full h-10 px-3 rounded bg-accent border border-border focus:outline-none focus:ring-2 focus:ring-primary"
              >
                {supportedModes.map((mode) => (
                  <option key={mode.value} value={mode.value}>
                    {mode.label}
                  </option>
                ))}
              </select>
            </div>
            
            {/* 填充模式 */}
            <div>
              <label className="block text-sm mb-2">{t('settings.padding.label')}</label>
              <select
                value={padding}
                onChange={(e) => setPadding(e.target.value)}
                className="w-full h-10 px-3 rounded bg-accent border border-border focus:outline-none focus:ring-2 focus:ring-primary"
              >
                {supportedPaddings.map((padding) => (
                  <option key={padding.value} value={padding.value}>
                    {padding.label}
                  </option>
                ))}
              </select>
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
          {/* 输入区 */}
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <h2 className="font-medium">
                {action === 'encrypt' ? t('input.encryptTitle') : t('input.decryptTitle')}
              </h2>
              <div className="flex items-center gap-2">
                <button 
                  onClick={clearInput}
                  className="text-xs px-2 py-1 rounded bg-accent hover:bg-accent/80 text-accent-foreground"
                >
                  {t('buttons.clear')}
                </button>
                <button 
                  onClick={loadExample}
                  className="text-xs px-2 py-1 rounded bg-accent hover:bg-accent/80 text-accent-foreground"
                >
                  {t('buttons.loadExample')}
                </button>
                <button 
                  onClick={() => copyToClipboard(input)}
                  className="p-1 rounded hover:bg-accent"
                  title={t('buttons.copy')}
                >
                  <Copy size={14} />
                </button>
              </div>
            </div>
            <textarea
              value={input}
              onChange={(e) => setInput(e.target.value)}
              placeholder={action === 'encrypt' ? t('input.encryptPlaceholder') : t('input.decryptPlaceholder')}
              className="w-full h-[200px] p-4 font-mono text-sm rounded-lg bg-accent border border-border focus:outline-none focus:ring-2 focus:ring-primary resize-none"
            ></textarea>
            
            {/* 密钥输入 */}
            <div>
              <div className="flex items-center justify-between mb-2">
                <label className="text-sm font-medium">{t('key.title')}</label>
                <div className="flex items-center gap-2">
                  <button 
                    onClick={generateRandomKey}
                    className="text-xs px-2 py-1 rounded bg-accent hover:bg-accent/80 text-accent-foreground flex items-center gap-1"
                  >
                    <RefreshCw size={12} />
                    <span>{t('buttons.generateKey')}</span>
                  </button>
                  <button 
                    onClick={() => copyToClipboard(key)}
                    className="p-1 rounded hover:bg-accent"
                    title={t('buttons.copy')}
                  >
                    <Copy size={14} />
                  </button>
                </div>
              </div>
              <input
                type="text"
                value={key}
                onChange={(e) => setKey(e.target.value)}
                placeholder={t('key.placeholder')}
                className="w-full h-10 px-3 font-mono text-sm rounded bg-accent border border-border focus:outline-none focus:ring-2 focus:ring-primary"
              />
              <p className="text-xs text-muted-foreground mt-1">
                {t('key.requiredLength', { size: keySize, chars: parseInt(keySize) / 4 })}
              </p>
            </div>
            
            {/* IV输入 (仅对需要IV的模式显示) */}
            {mode !== 'ecb' && (
              <div>
                <div className="flex items-center justify-between mb-2">
                  <label className="text-sm font-medium">{t('iv.title')}</label>
                  <div className="flex items-center gap-2">
                    <button 
                      onClick={generateRandomIv}
                      className="text-xs px-2 py-1 rounded bg-accent hover:bg-accent/80 text-accent-foreground flex items-center gap-1"
                    >
                      <RefreshCw size={12} />
                      <span>{t('buttons.generateIv')}</span>
                    </button>
                    <button 
                      onClick={() => copyToClipboard(iv)}
                      className="p-1 rounded hover:bg-accent"
                      title={t('buttons.copy')}
                    >
                      <Copy size={14} />
                    </button>
                  </div>
                </div>
                <input
                  type="text"
                  value={iv}
                  onChange={(e) => setIv(e.target.value)}
                  placeholder={t('iv.placeholder')}
                  className="w-full h-10 px-3 font-mono text-sm rounded bg-accent border border-border focus:outline-none focus:ring-2 focus:ring-primary"
                />
                <p className="text-xs text-muted-foreground mt-1">
                  {t('iv.requiredLength')}
                </p>
              </div>
            )}
            
            {error && (
              <div className="flex items-center gap-2 text-red-500 text-sm">
                <AlertTriangle size={14} />
                <span>{error}</span>
              </div>
            )}
          </div>

          {/* 结果区 */}
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <h2 className="font-medium">
                {action === 'encrypt' ? t('output.encryptTitle') : t('output.decryptTitle')}
              </h2>
              {output && (
                <button 
                  onClick={() => copyToClipboard(output)}
                  className="text-xs px-2 py-1 rounded bg-primary hover:bg-primary/90 text-primary-foreground flex items-center gap-1"
                >
                  <Copy size={14} />
                  <span>{t('buttons.copyResult')}</span>
                </button>
              )}
            </div>
            <div className="h-[350px] overflow-y-auto rounded-lg bg-accent border border-border p-4">
              {output ? (
                <div className="font-mono text-sm break-all whitespace-pre-wrap">
                  {output}
                </div>
              ) : (
                <div className="h-full flex items-center justify-center text-center text-muted-foreground">
                  <div>
                    {action === 'encrypt' ? (
                      <Lock size={28} className="mx-auto mb-2 opacity-40" />
                    ) : (
                      <Unlock size={28} className="mx-auto mb-2 opacity-40" />
                    )}
                    <p>{action === 'encrypt' ? t('output.encryptEmpty') : t('output.decryptEmpty')}</p>
                    <p className="text-xs mt-1">{t('output.clickButton', { action: action === 'encrypt' ? t('modes.encrypt') : t('modes.decrypt') })}</p>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* 执行按钮 */}
        <div className="flex justify-center mb-8">
          <button
            onClick={handleProcess}
            disabled={loading || !input.trim() || !key.trim() || (mode !== 'ecb' && !iv.trim())}
            className="h-12 px-8 rounded-lg bg-primary text-primary-foreground hover:bg-primary/90 flex items-center gap-3 disabled:opacity-70 disabled:cursor-not-allowed"
          >
            {loading ? (
              <>
                <span className="animate-spin h-5 w-5 border-2 border-white border-t-transparent rounded-full"></span>
                <span>{action === 'encrypt' ? t('processing.encrypting') : t('processing.decrypting')}</span>
              </>
            ) : (
              <>
                {action === 'encrypt' ? <Lock size={18} /> : <Unlock size={18} />}
                <span>{action === 'encrypt' ? t('modes.encrypt') : t('modes.decrypt')}</span>
              </>
            )}
          </button>
        </div>

        {/* 信息说明 */}
        <div className="bg-card border border-border rounded-lg p-6">
          <h3 className="font-medium mb-4">{t('info.title')}</h3>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h4 className="font-medium mb-2">{t('info.features.title')}</h4>
              <ul className="list-disc list-inside mb-4 text-muted-foreground text-sm space-y-1">
                {t('info.features.items').split('|').map((item, index) => (
                  <li key={index}>{item}</li>
                ))}
              </ul>
            </div>
            
            <div>
              <h4 className="font-medium mb-2">{t('info.modes.title')}</h4>
              <ul className="list-disc list-inside mb-4 text-muted-foreground text-sm space-y-1">
                {t('info.modes.items').split('|').map((item, index) => (
                  <li key={index}>{item}</li>
                ))}
              </ul>
            </div>
          </div>
          
          <div className="mt-4 text-sm text-muted-foreground">
            <p>{t('info.tips.title')}</p>
            <ul className="list-disc list-inside mt-2 space-y-1">
              {t('info.tips.items').split('|').map((item, index) => (
                <li key={index}>{item}</li>
              ))}
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
}
