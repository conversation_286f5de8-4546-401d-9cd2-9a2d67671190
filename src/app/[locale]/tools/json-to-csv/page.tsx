'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { ArrowLeft, Download, Copy, RefreshCw, ArrowRightLeft, AlertTriangle, FileText } from 'lucide-react';

export default function JsonToCsvPage() {
  const [jsonInput, setJsonInput] = useState('');
  const [csvOutput, setCsvOutput] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [includeHeader, setIncludeHeader] = useState(true);
  const [delimiter, setDelimiter] = useState(',');
  const router = useRouter();

  // 将JSON转换为CSV
  const convertJsonToCsv = (jsonData: any, includeHeader: boolean, delimiter: string) => {
    // 处理单个对象（转换成数组）
    if (!Array.isArray(jsonData)) {
      jsonData = [jsonData];
    }
    
    if (jsonData.length === 0) {
      return '';
    }
    
    // 获取所有对象的所有可能的键
    const allKeys = new Set<string>();
    jsonData.forEach((item: any) => {
      if (typeof item === 'object' && item !== null) {
        Object.keys(item).forEach(key => allKeys.add(key));
      }
    });
    
    const headers = Array.from(allKeys);
    
    // 如果对象为空，返回空字符串
    if (headers.length === 0) {
      return '';
    }
    
    // 构建CSV行
    const rows: string[] = [];
    
    // 添加表头行
    if (includeHeader) {
      rows.push(headers.map(key => escapeCsvValue(key, delimiter)).join(delimiter));
    }
    
    // 添加数据行
    jsonData.forEach((item: any) => {
      const row = headers.map(key => {
        const value = item && typeof item === 'object' ? item[key] : '';
        return escapeCsvValue(value === undefined || value === null ? '' : String(value), delimiter);
      });
      rows.push(row.join(delimiter));
    });
    
    return rows.join('\n');
  };

  // 转义CSV值
  const escapeCsvValue = (value: string, delimiter: string) => {
    // 如果值包含逗号、双引号或换行符，需要用双引号包围
    if (value.includes(delimiter) || value.includes('"') || value.includes('\n')) {
      // 将双引号替换为两个双引号
      value = value.replace(/"/g, '""');
      // 用双引号包围整个值
      return `"${value}"`;
    }
    return value;
  };

  // 转换处理函数
  const handleConvert = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!jsonInput.trim()) {
      setError('请输入JSON数据');
      return;
    }
    
    setLoading(true);
    setError(null);
    
    try {
      // 解析JSON
      const jsonData = JSON.parse(jsonInput);
      
      // 转换为CSV
      const csvContent = convertJsonToCsv(jsonData, includeHeader, delimiter);
      
      setCsvOutput(csvContent);
    } catch (err) {
      setError(err instanceof Error ? `JSON解析错误: ${err.message}` : '无效的JSON格式');
    } finally {
      setLoading(false);
    }
  };

  // 示例JSON数据
  const loadExample = () => {
    const exampleJson = `[
  {
    "id": 1,
    "name": "张三",
    "email": "<EMAIL>",
    "age": 28,
    "department": "研发部",
    "skills": ["JavaScript", "React", "Node.js"],
    "isActive": true
  },
  {
    "id": 2,
    "name": "李四",
    "email": "<EMAIL>",
    "age": 32,
    "department": "市场部",
    "skills": ["Marketing", "SEO", "Social Media"],
    "isActive": true
  },
  {
    "id": 3,
    "name": "王五",
    "email": "<EMAIL>",
    "age": 25,
    "department": "设计部",
    "skills": ["Photoshop", "Illustrator", "UI/UX"],
    "isActive": false
  },
  {
    "id": 4,
    "name": "赵六",
    "email": "<EMAIL>",
    "age": 35,
    "department": "人力资源",
    "skills": ["Recruitment", "Training", "HR Management"],
    "isActive": true
  },
  {
    "id": 5,
    "name": "钱七",
    "email": "<EMAIL>",
    "age": 29,
    "department": "财务部",
    "skills": ["Accounting", "Financial Analysis", "Budgeting"],
    "isActive": true
  }
]`;
    
    setJsonInput(exampleJson);
    setError(null);
  };

  // 复制到剪贴板
  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
  };

  // 下载CSV结果
  const downloadCsv = () => {
    if (csvOutput) {
      const blob = new Blob([csvOutput], { type: 'text/csv;charset=utf-8;' });
      const url = URL.createObjectURL(blob);
      
      const a = document.createElement('a');
      a.href = url;
      a.download = 'converted.csv';
      document.body.appendChild(a);
      a.click();
      
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
    }
  };

  // 清空输入
  const clearInput = () => {
    setJsonInput('');
    setCsvOutput('');
    setError(null);
  };

  return (
    <div className="min-h-screen bg-background p-6">
      <div className="max-w-6xl mx-auto">
        {/* 返回按钮 */}
        <button 
          onClick={() => router.back()}
          className="mb-6 flex items-center gap-2 text-primary hover:underline"
        >
          <ArrowLeft size={16} />
          <span>返回工具列表</span>
        </button>

        {/* 工具标题 */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold">JSON转CSV</h1>
          <p className="text-muted-foreground mt-2">
            将JSON数据转换为CSV格式，便于在Excel或其他电子表格软件中使用
          </p>
        </div>

        {/* 选项设置 */}
        <div className="mb-6">
          <h2 className="font-medium mb-3">转换选项</h2>
          <div className="flex flex-wrap items-center gap-6">
            <div className="flex items-center gap-2">
              <input
                type="checkbox"
                id="includeHeader"
                checked={includeHeader}
                onChange={(e) => setIncludeHeader(e.target.checked)}
                className="h-4 w-4 rounded border-border text-primary focus:ring-primary"
              />
              <label htmlFor="includeHeader" className="text-sm text-muted-foreground">
                包含表头行
              </label>
            </div>
            
            <div>
              <label className="text-sm text-muted-foreground block mb-1">分隔符</label>
              <select 
                value={delimiter}
                onChange={(e) => setDelimiter(e.target.value)}
                className="h-9 px-3 rounded bg-accent border border-border focus:outline-none focus:ring-2 focus:ring-primary"
              >
                <option value=",">逗号 (,)</option>
                <option value=";">分号 (;)</option>
                <option value="\t">制表符 (Tab)</option>
                <option value="|">竖线 (|)</option>
              </select>
            </div>
          </div>
        </div>

        {/* 转换区域 */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
          {/* JSON输入区 */}
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <h2 className="font-medium">JSON输入</h2>
              <div className="flex items-center gap-2">
                <button 
                  onClick={clearInput}
                  className="text-xs px-2 py-1 rounded bg-accent hover:bg-accent/80 text-accent-foreground"
                >
                  清空
                </button>
                <button 
                  onClick={loadExample}
                  className="text-xs px-2 py-1 rounded bg-accent hover:bg-accent/80 text-accent-foreground"
                >
                  加载示例
                </button>
                <button 
                  onClick={() => copyToClipboard(jsonInput)}
                  className="p-1 rounded hover:bg-accent"
                  title="复制"
                >
                  <Copy size={14} />
                </button>
              </div>
            </div>
            <textarea
              value={jsonInput}
              onChange={(e) => setJsonInput(e.target.value)}
              placeholder="在此粘贴JSON数据..."
              className="w-full h-[500px] p-4 font-mono text-sm rounded-lg bg-accent border border-border focus:outline-none focus:ring-2 focus:ring-primary resize-none"
            ></textarea>
            {error && (
              <div className="flex items-center gap-2 text-red-500 text-sm">
                <AlertTriangle size={14} />
                <span>{error}</span>
              </div>
            )}
          </div>

          {/* CSV输出区 */}
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <h2 className="font-medium">CSV输出</h2>
              <div className="flex items-center gap-2">
                {csvOutput && (
                  <>
                    <button 
                      onClick={() => copyToClipboard(csvOutput)}
                      className="text-xs px-2 py-1 rounded bg-primary hover:bg-primary/90 text-primary-foreground flex items-center gap-1"
                    >
                      <Copy size={14} />
                      <span>复制</span>
                    </button>
                    <button 
                      onClick={downloadCsv}
                      className="text-xs px-2 py-1 rounded bg-primary hover:bg-primary/90 text-primary-foreground flex items-center gap-1"
                    >
                      <Download size={14} />
                      <span>下载CSV</span>
                    </button>
                  </>
                )}
              </div>
            </div>
            <div className="relative h-[500px]">
              <textarea
                value={csvOutput}
                readOnly
                placeholder="CSV输出将显示在这里..."
                className="w-full h-full p-4 font-mono text-sm rounded-lg bg-accent border border-border focus:outline-none resize-none"
              ></textarea>
              {!csvOutput && (
                <div className="absolute inset-0 flex items-center justify-center">
                  <div className="text-center text-muted-foreground p-4">
                    <FileText size={28} className="mx-auto mb-2 opacity-40" />
                    <p>转换后的CSV将显示在这里</p>
                    <p className="text-xs mt-1">点击下方按钮开始转换</p>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* 转换按钮 */}
        <div className="flex justify-center mb-8">
          <button
            onClick={handleConvert}
            disabled={loading || !jsonInput.trim()}
            className="h-12 px-8 rounded-lg bg-primary text-primary-foreground hover:bg-primary/90 flex items-center gap-3 disabled:opacity-70 disabled:cursor-not-allowed"
          >
            {loading ? (
              <>
                <span className="animate-spin h-5 w-5 border-2 border-white border-t-transparent rounded-full"></span>
                <span>转换中...</span>
              </>
            ) : (
              <>
                <ArrowRightLeft size={18} />
                <span>转换 JSON 到 CSV</span>
              </>
            )}
          </button>
        </div>

        {/* 信息说明 */}
        <div className="bg-card border border-border rounded-lg p-6">
          <h3 className="font-medium mb-4">关于JSON和CSV</h3>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h4 className="font-medium mb-2">JSON (JavaScript Object Notation)</h4>
              <ul className="list-disc list-inside mb-4 text-muted-foreground text-sm space-y-1">
                <li>基于JavaScript的轻量级数据交换格式</li>
                <li>使用键值对结构，支持嵌套对象和数组</li>
                <li>常用于Web API和配置文件</li>
                <li>支持复杂的数据结构和数据类型</li>
                <li>易于程序解析，但不太适合直接在电子表格中查看</li>
              </ul>
            </div>
            
            <div>
              <h4 className="font-medium mb-2">CSV (Comma-Separated Values)</h4>
              <ul className="list-disc list-inside mb-4 text-muted-foreground text-sm space-y-1">
                <li>简单的表格数据格式，用分隔符分隔值</li>
                <li>可以直接在Excel、Google Sheets等电子表格软件中打开</li>
                <li>每行代表一条记录，每列代表一个字段</li>
                <li>不支持嵌套数据结构，只有扁平结构</li>
                <li>理想用于数据分析和报表生成</li>
              </ul>
            </div>
          </div>
          
          <div className="mt-4 text-sm text-muted-foreground">
            <p>转换提示：</p>
            <ul className="list-disc list-inside mt-2 space-y-1">
              <li>JSON应该是包含对象的数组，或者单个对象</li>
              <li>复杂数据类型（如嵌套对象或数组）会被转换为字符串</li>
              <li>如果JSON中的对象有不同的属性，CSV将包含所有可能的列</li>
              <li>CSV文件中的特殊字符（如逗号、引号）会被适当转义</li>
              <li>可以选择包含或不包含表头行，以及自定义分隔符</li>
              <li>输出的CSV可以直接复制或下载为文件</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
}
