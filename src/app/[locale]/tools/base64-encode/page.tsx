'use client';

import { useState, useRef, useEffect, useCallback } from 'react';
import { useRouter } from 'next/navigation';
import { ArrowLeft, Copy, Upload, Trash, Check, Download, RefreshCw, AlertTriangle, Info } from 'lucide-react';

type Mode = 'encode' | 'decode';
type InputType = 'text' | 'file';
type EncodingType = 'standard' | 'urlsafe';
type CharsetType = 'utf8' | 'ascii' | 'iso88591' | 'ucs2';

export default function Base64Page() {
  const router = useRouter();
  const [mode, setMode] = useState<Mode>('encode');
  const [inputType, setInputType] = useState<InputType>('text');
  const [encodingType, setEncodingType] = useState<EncodingType>('standard');
  const [charsetType, setCharsetType] = useState<CharsetType>('utf8');
  const [input, setInput] = useState<string>('');
  const [output, setOutput] = useState<string>('');
  const [fileName, setFileName] = useState<string>('');
  const [fileType, setFileType] = useState<string>('');
  const [fileSize, setFileSize] = useState<number>(0);
  const [copySuccess, setCopySuccess] = useState<boolean>(false);
  const [errorMessage, setErrorMessage] = useState<string>('');
  const [lineBreaks, setLineBreaks] = useState<boolean>(false);
  const [processing, setProcessing] = useState<boolean>(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // 示例文本
  const exampleText = '这是一个示例文本，用于演示Base64编解码功能。This is an example text for demonstrating Base64 encoding and decoding.';

  // 加载示例
  const loadExample = () => {
    setInput(exampleText);
    setInputType('text');
    setFileName('');
    setFileType('');
  };

  // 清空输入
  const clearInput = () => {
    setInput('');
    setOutput('');
    setFileName('');
    setFileType('');
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  // 处理文本输入变化
  const handleTextChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setInput(e.target.value);
  };

  // 处理文件上传
  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    setFileName(file.name);
    setFileType(file.type);
    setFileSize(file.size);
    setInputType('file');
    setErrorMessage('');
    setProcessing(true);

    // 文件大小检查
    if (file.size > 5 * 1024 * 1024) { // 5MB 限制
      setErrorMessage(`文件过大(${(file.size / (1024 * 1024)).toFixed(2)}MB)。为了浏览器性能，建议处理5MB以下的文件。`);    
    }

    const reader = new FileReader();
    
    reader.onload = (e) => {
      try {
        if (typeof e.target?.result === 'string') {
          // 如果是DataURL，提取base64部分
          const base64Data = e.target.result.split(',')[1];
          setInput(base64Data || '');
        } else if (e.target?.result instanceof ArrayBuffer) {
          // 如果是二进制数据
          const bytes = new Uint8Array(e.target.result);
          let binary = '';
          const chunkSize = 1024; // 分块处理以避免大文件性能问题
          
          for (let i = 0; i < bytes.byteLength; i += chunkSize) {
            const chunk = bytes.slice(i, Math.min(i + chunkSize, bytes.byteLength));
            binary += Array.from(chunk)
              .map(byte => String.fromCharCode(byte))
              .join('');
          }
          
          setInput(btoa(binary));
        }
      } catch (error) {
        setErrorMessage(`文件处理失败: ${error instanceof Error ? error.message : String(error)}`);
        setInput('');
      } finally {
        setProcessing(false);
      }
    };

    reader.onerror = () => {
      setErrorMessage('文件读取失败，请重试');
      setProcessing(false);
    };

    // 根据模式选择读取方式
    if (mode === 'encode') {
      reader.readAsDataURL(file);
    } else {
      reader.readAsText(file);
    }
  };

  // 复制结果
  const copyResult = () => {
    navigator.clipboard.writeText(output)
      .then(() => {
        setCopySuccess(true);
        setTimeout(() => setCopySuccess(false), 2000);
      })
      .catch(err => {
        console.error('复制失败:', err);
      });
  };

  // 下载结果
  const downloadResult = () => {
    if (!output) return;

    let downloadData: string;
    let downloadType: string;
    let downloadName: string;

    if (mode === 'decode' && inputType === 'file') {
      // 尝试解析base64为二进制
      try {
        const byteCharacters = atob(output);
        const byteNumbers = new Array(byteCharacters.length);
        for (let i = 0; i < byteCharacters.length; i++) {
          byteNumbers[i] = byteCharacters.charCodeAt(i);
        }
        const byteArray = new Uint8Array(byteNumbers);
        
        downloadData = URL.createObjectURL(new Blob([byteArray], { type: fileType || 'application/octet-stream' }));
        downloadType = 'binary';
        downloadName = fileName ? `decoded-${fileName}` : 'decoded-file';
      } catch (e) {
        // 如果解析失败，作为文本下载
        downloadData = `data:text/plain;charset=utf-8,${encodeURIComponent(output)}`;
        downloadType = 'text';
        downloadName = 'decoded-base64.txt';
      }
    } else {
      // 文本下载
      downloadData = `data:text/plain;charset=utf-8,${encodeURIComponent(output)}`;
      downloadType = 'text';
      downloadName = mode === 'encode' ? 'encoded-base64.txt' : 'decoded-text.txt';
    }

    const link = document.createElement('a');
    link.href = downloadData;
    link.download = downloadName;
    document.body.appendChild(link);
    link.click();
    
    // 如果是对象URL，需要释放
    if (downloadType === 'binary') {
      URL.revokeObjectURL(downloadData);
    }
    
    document.body.removeChild(link);
  };

  // 编码字符串
  const encodeString = useCallback((str: string): string => {
    try {
      // 根据选择的字符集进行编码
      let binaryStr: string;
      switch (charsetType) {
        case 'ascii':
          binaryStr = str.replace(/[^\u0000-\u007F]/g, '?'); // ASCII 只保留0-127的字符
          break;
        case 'iso88591':
          binaryStr = str.replace(/[^\u0000-\u00FF]/g, '?'); // ISO-8859-1 只保留0-255的字符
          break;
        case 'ucs2':
          binaryStr = str; // UCS-2 (UTF-16) 保持原样，让浏览器处理
          break;
        case 'utf8':
        default:
          binaryStr = unescape(encodeURIComponent(str)); // UTF-8 编码
          break;
      }

      // Base64 编码
      let encoded = btoa(binaryStr);

      // 如果需要URL安全
      if (encodingType === 'urlsafe') {
        encoded = encoded.replace(/\+/g, '-').replace(/\//g, '_').replace(/=+$/, '');
      }

      // 添加换行
      if (lineBreaks) {
        encoded = encoded.replace(/.{76}/g, '$&\n');
      }

      return encoded;
    } catch (error) {
      setErrorMessage(`编码失败: ${error instanceof Error ? error.message : String(error)}`);
      return '';
    }
  }, [charsetType, encodingType, lineBreaks]);

  // 解码字符串
  const decodeString = useCallback((str: string): string => {
    try {
      // 移除可能存在的换行符和空格
      let cleanStr = str.replace(/\s/g, '');
      
      // 如果是URL安全的，先转换回标准格式
      if (encodingType === 'urlsafe') {
        cleanStr = cleanStr.replace(/-/g, '+').replace(/_/g, '/');
        // 添加回等号填充
        while (cleanStr.length % 4) {
          cleanStr += '=';
        }
      }

      // Base64 解码
      const binaryStr = atob(cleanStr);

      // 根据字符集解码
      switch (charsetType) {
        case 'ascii':
        case 'iso88591':
          return binaryStr; // 直接返回
        case 'ucs2':
          // 处理UTF-16编码
          const bytes = new Uint16Array(binaryStr.length / 2);
          for (let i = 0; i < binaryStr.length; i += 2) {
            bytes[i/2] = (binaryStr.charCodeAt(i) << 8) | binaryStr.charCodeAt(i + 1);
          }
          // 使用Array.from来避免Uint16Array迭代的问题
          return String.fromCharCode.apply(null, Array.from(bytes));
        case 'utf8':
        default:
          return decodeURIComponent(escape(binaryStr)); // UTF-8 解码
      }
    } catch (error) {
      setErrorMessage(`解码失败: ${error instanceof Error ? error.message : String(error)}`);
      return '';
    }
  }, [encodingType, charsetType]);

  // 转换处理
  useEffect(() => {
    if (!input) {
      setOutput('');
      setErrorMessage('');
      return;
    }

    setErrorMessage('');
    try {
      if (mode === 'encode') {
        if (inputType === 'text') {
          // 文本编码
          setOutput(encodeString(input));
        } else {
          // 文件已经在上传时进行了编码，根据编码类型处理
          if (encodingType === 'urlsafe') {
            // 转换为URL安全格式
            setOutput(input.replace(/\+/g, '-').replace(/\//g, '_').replace(/=+$/, ''));
          } else {
            // 添加换行
            setOutput(lineBreaks ? input.replace(/.{76}/g, '$&\n') : input);
          }
        }
      } else {
        // 解码
        setOutput(decodeString(input));
      }
    } catch (error) {
      setErrorMessage(`处理失败: ${error instanceof Error ? error.message : String(error)}`);
      setOutput('');
    }
  }, [input, mode, inputType, encodingType, charsetType, lineBreaks, encodeString, decodeString]);

  return (
    <div className="container mx-auto p-6">
      {/* 头部 */}
      <div className="flex items-center mb-6">
        <button 
          onClick={() => router.back()}
          className="p-2 mr-4 rounded-full hover:bg-gray-100 dark:hover:bg-gray-800"
        >
          <ArrowLeft size={24} />
        </button>
        <h1 className="text-2xl font-bold">Base64编解码工具</h1>
      </div>

      {/* 工具说明 */}
      <div className="mb-8 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
        <h2 className="text-lg font-medium mb-2">工具说明</h2>
        <p className="text-gray-700 dark:text-gray-300">
          Base64是一种基于64个可打印字符来表示二进制数据的编码方式，常用于在处理文本数据的场合，
          使二进制数据可以被文本处理。本工具支持文本和文件的Base64编码和解码，包括标准Base64和URL安全Base64，
          并提供多种字符编码选项。所有处理都在浏览器本地完成，不会上传您的数据。
        </p>
        <div className="mt-4 flex items-start">
          <Info size={16} className="mr-2 mt-1 flex-shrink-0 text-blue-500" />
          <div className="text-sm text-gray-600 dark:text-gray-400">
            <p><strong>标准Base64</strong>：使用A-Z, a-z, 0-9, +, / 和 = (填充符) 字符集</p>
            <p><strong>URL安全Base64</strong>：使用A-Z, a-z, 0-9, -, _ 字符集，移除填充的等号，适用于URL和文件名</p>
          </div>
        </div>
      </div>
      
      {/* 错误消息显示 */}
      {errorMessage && (
        <div className="mb-4 p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg flex items-start">
          <AlertTriangle size={16} className="mr-2 mt-1 flex-shrink-0 text-red-500" />
          <p className="text-sm text-red-700 dark:text-red-300">{errorMessage}</p>
        </div>
      )}

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* 左侧：输入区域 */}
        <div className="space-y-4">
          <div className="flex justify-between items-center mb-2">
            <h2 className="text-lg font-medium">输入区域</h2>
            <div className="flex space-x-2">
              <button
                onClick={loadExample}
                className="text-xs p-1 px-2 rounded bg-gray-100 hover:bg-gray-200 dark:bg-gray-800 dark:hover:bg-gray-700"
              >
                加载示例
              </button>
              <button
                onClick={clearInput}
                className="text-xs p-1 px-2 rounded bg-gray-100 hover:bg-gray-200 dark:bg-gray-800 dark:hover:bg-gray-700 flex items-center"
              >
                <Trash size={12} className="mr-1" />
                清空
              </button>
              <label className="text-xs p-1 px-2 rounded bg-gray-100 hover:bg-gray-200 dark:bg-gray-800 dark:hover:bg-gray-700 flex items-center cursor-pointer">
                <Upload size={12} className="mr-1" />
                上传文件
                <input
                  ref={fileInputRef}
                  type="file"
                  onChange={handleFileUpload}
                  className="hidden"
                />
              </label>
            </div>
          </div>

          {/* 模式选择 */}
          <div className="flex mb-4 bg-white dark:bg-gray-900 rounded-lg p-2 border border-gray-300 dark:border-gray-700">
            <button
              onClick={() => setMode('encode')}
              className={`flex-1 py-2 rounded-md ${
                mode === 'encode'
                  ? 'bg-blue-500 text-white'
                  : 'bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-300'
              }`}
            >
              编码
            </button>
            <button
              onClick={() => setMode('decode')}
              className={`flex-1 py-2 rounded-md ${
                mode === 'decode'
                  ? 'bg-blue-500 text-white'
                  : 'bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-300'
              }`}
            >
              解码
            </button>
          </div>

          {/* 高级选项 */}
          <div className="mb-4 p-3 bg-white dark:bg-gray-900 rounded-lg border border-gray-300 dark:border-gray-700">
            <h3 className="text-sm font-medium mb-2">高级选项</h3>
            
            {/* Base64类型选择 */}
            <div className="mb-3">
              <label className="block text-xs text-gray-600 dark:text-gray-400 mb-1">Base64格式：</label>
              <div className="flex space-x-2">
                <button
                  onClick={() => setEncodingType('standard')}
                  className={`px-2 py-1 text-xs rounded ${encodingType === 'standard' ? 'bg-blue-100 dark:bg-blue-900 text-blue-700 dark:text-blue-300' : 'bg-gray-100 dark:bg-gray-800'}`}
                >
                  标准Base64
                </button>
                <button
                  onClick={() => setEncodingType('urlsafe')}
                  className={`px-2 py-1 text-xs rounded ${encodingType === 'urlsafe' ? 'bg-blue-100 dark:bg-blue-900 text-blue-700 dark:text-blue-300' : 'bg-gray-100 dark:bg-gray-800'}`}
                >
                  URL安全Base64
                </button>
              </div>
            </div>
            
            {/* 字符编码选择 */}
            <div className="mb-3">
              <label className="block text-xs text-gray-600 dark:text-gray-400 mb-1">字符编码：</label>
              <div className="grid grid-cols-4 gap-2">
                <button
                  onClick={() => setCharsetType('utf8')}
                  className={`px-2 py-1 text-xs rounded ${charsetType === 'utf8' ? 'bg-blue-100 dark:bg-blue-900 text-blue-700 dark:text-blue-300' : 'bg-gray-100 dark:bg-gray-800'}`}
                >
                  UTF-8
                </button>
                <button
                  onClick={() => setCharsetType('ascii')}
                  className={`px-2 py-1 text-xs rounded ${charsetType === 'ascii' ? 'bg-blue-100 dark:bg-blue-900 text-blue-700 dark:text-blue-300' : 'bg-gray-100 dark:bg-gray-800'}`}
                >
                  ASCII
                </button>
                <button
                  onClick={() => setCharsetType('iso88591')}
                  className={`px-2 py-1 text-xs rounded ${charsetType === 'iso88591' ? 'bg-blue-100 dark:bg-blue-900 text-blue-700 dark:text-blue-300' : 'bg-gray-100 dark:bg-gray-800'}`}
                >
                  ISO-8859-1
                </button>
                <button
                  onClick={() => setCharsetType('ucs2')}
                  className={`px-2 py-1 text-xs rounded ${charsetType === 'ucs2' ? 'bg-blue-100 dark:bg-blue-900 text-blue-700 dark:text-blue-300' : 'bg-gray-100 dark:bg-gray-800'}`}
                >
                  UCS-2
                </button>
              </div>
            </div>
            
            {/* 其他选项 */}
            <div>
              <label className="flex items-center space-x-2 text-xs">
                <input 
                  type="checkbox" 
                  checked={lineBreaks} 
                  onChange={(e) => setLineBreaks(e.target.checked)} 
                  className="form-checkbox h-3 w-3"
                />
                <span className="text-gray-600 dark:text-gray-400">每76个字符添加换行（适用于邮件传输）</span>
              </label>
            </div>
          </div>

          {fileName && (
            <div className="mb-4 p-2 bg-gray-100 dark:bg-gray-800 rounded">
              <p className="text-sm">文件: {fileName}</p>
              <div className="flex text-xs text-gray-500 dark:text-gray-400 space-x-2">
                {fileType && <p>类型: {fileType}</p>}
                {fileSize > 0 && <p>大小: {(fileSize / 1024).toFixed(2)} KB</p>}
              </div>
            </div>
          )}
          
          {processing && (
            <div className="mb-4 p-2 flex items-center justify-center bg-blue-50 dark:bg-blue-900/20 rounded">
              <RefreshCw size={14} className="animate-spin mr-2" />
              <p className="text-sm">处理中...</p>
            </div>
          )}

          <textarea
            value={inputType === 'text' ? input : ''}
            onChange={handleTextChange}
            placeholder={mode === 'encode' ? "在此输入要编码的文本..." : "在此输入要解码的Base64文本..."}
            className="w-full h-[300px] p-4 border border-gray-300 dark:border-gray-700 rounded-lg bg-white dark:bg-gray-900 focus:outline-none focus:ring-2 focus:ring-blue-500"
            disabled={inputType === 'file'}
          />
        </div>

        {/* 右侧：输出区域 */}
        <div className="space-y-4">
          <div className="flex justify-between items-center mb-2">
            <h2 className="text-lg font-medium">输出结果</h2>
            <div className="flex space-x-2">
              <button
                onClick={copyResult}
                className="text-xs p-1 px-2 rounded bg-gray-100 hover:bg-gray-200 dark:bg-gray-800 dark:hover:bg-gray-700 flex items-center"
                disabled={!output}
              >
                {copySuccess ? <Check size={12} className="mr-1" /> : <Copy size={12} className="mr-1" />}
                {copySuccess ? '已复制' : '复制结果'}
              </button>
              <button
                onClick={downloadResult}
                className="text-xs p-1 px-2 rounded bg-gray-100 hover:bg-gray-200 dark:bg-gray-800 dark:hover:bg-gray-700 flex items-center"
                disabled={!output}
              >
                <Download size={12} className="mr-1" />
                下载结果
              </button>
            </div>
          </div>

          <div className="w-full p-4 border border-gray-300 dark:border-gray-700 rounded-lg bg-white dark:bg-gray-900 h-[300px] overflow-auto relative">
            {output ? (
              <>
                <pre className="text-sm whitespace-pre-wrap break-all">{output}</pre>
                {output.length > 5000 && (
                  <div className="absolute bottom-2 right-2 bg-yellow-100 dark:bg-yellow-900/50 text-yellow-800 dark:text-yellow-200 text-xs py-1 px-2 rounded">
                    显示 {output.length.toLocaleString()} 个字符
                  </div>
                )}
              </>
            ) : (
              <div className="text-sm text-gray-500 dark:text-gray-400 h-full flex items-center justify-center">
                {mode === 'encode' ? '编码结果将显示在这里...' : '解码结果将显示在这里...'}
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
} 