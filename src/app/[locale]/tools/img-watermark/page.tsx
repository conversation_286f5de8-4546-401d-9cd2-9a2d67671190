'use client';

import { useState, useRef, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Image from 'next/image';
import { 
  ArrowLeft, Upload, Download, RefreshCw, Image as ImageIcon, 
  FileType, Settings, Trash, Type, AlignLeft, 
  AlignCenter, AlignRight, AlignJustify, Sliders, AlertTriangle
} from 'lucide-react';

type WatermarkType = 'text' | 'image';
type WatermarkPosition = 'top-left' | 'top-center' | 'top-right' | 'middle-left' | 'middle-center' | 'middle-right' | 'bottom-left' | 'bottom-center' | 'bottom-right';

interface WatermarkSettings {
  type: WatermarkType;
  text: string;
  textColor: string;
  textSize: number;
  fontFamily: string;
  position: WatermarkPosition;
  opacity: number;
  padding: number;
  imageUrl?: string;
}

export default function ImageWatermarkPage() {
  const [file, setFile] = useState<File | null>(null);
  const [watermarkFile, setWatermarkFile] = useState<File | null>(null);
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);
  const [processedUrl, setProcessedUrl] = useState<string | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [watermarkSettings, setWatermarkSettings] = useState<WatermarkSettings>({
    type: 'text',
    text: '版权所有',
    textColor: '#ffffff',
    textSize: 24,
    fontFamily: 'Arial',
    position: 'bottom-right',
    opacity: 50,
    padding: 20
  });
  
  const fileInputRef = useRef<HTMLInputElement>(null);
  const watermarkFileInputRef = useRef<HTMLInputElement>(null);
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const router = useRouter();

  // 当选择主图片时的处理
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      const selectedFile = e.target.files[0];
      
      if (!selectedFile.type.startsWith('image/')) {
        setError('请选择有效的图片文件');
        return;
      }
      
      setFile(selectedFile);
      setPreviewUrl(URL.createObjectURL(selectedFile));
      setProcessedUrl(null);
      setError(null);
    }
  };

  // 当选择水印图片时的处理
  const handleWatermarkFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      const selectedFile = e.target.files[0];
      
      if (!selectedFile.type.startsWith('image/')) {
        setError('请选择有效的图片文件作为水印');
        return;
      }
      
      setWatermarkFile(selectedFile);
      setWatermarkSettings(prev => ({
        ...prev,
        imageUrl: URL.createObjectURL(selectedFile),
        type: 'image'
      }));
      setProcessedUrl(null);
    }
  };

  // 处理水印设置变更
  const handleSettingsChange = (key: keyof WatermarkSettings, value: any) => {
    setWatermarkSettings(prev => ({
      ...prev,
      [key]: value
    }));
    setProcessedUrl(null);
  };

  // 应用水印到图片
  const applyWatermark = async () => {
    if (!file) {
      setError('请先选择图片文件');
      return;
    }

    setLoading(true);
    setError(null);
    
    try {
      const imageUrl = URL.createObjectURL(file);
      
      // 创建图片对象
      const img = new window.Image();
      img.src = imageUrl;
      
      await new Promise<void>((resolve, reject) => {
        img.onload = () => resolve();
        img.onerror = () => reject(new Error('图片加载失败'));
      });

      // 准备画布
      const canvas = document.createElement('canvas');
      canvas.width = img.width;
      canvas.height = img.height;
      
      const ctx = canvas.getContext('2d');
      if (!ctx) {
        throw new Error('无法创建画布上下文');
      }
      
      // 绘制原始图片
      ctx.drawImage(img, 0, 0);
      
      // 计算水印位置
      const padding = watermarkSettings.padding;
      let x = padding;
      let y = padding;
      
      // 根据位置设置坐标
      if (watermarkSettings.position.includes('center')) {
        x = canvas.width / 2;
      } else if (watermarkSettings.position.includes('right')) {
        x = canvas.width - padding;
      }
      
      if (watermarkSettings.position.includes('middle')) {
        y = canvas.height / 2;
      } else if (watermarkSettings.position.includes('bottom')) {
        y = canvas.height - padding;
      }
      
      // 设置透明度
      ctx.globalAlpha = watermarkSettings.opacity / 100;
      
      // 添加水印
      if (watermarkSettings.type === 'text') {
        // 文字水印
        ctx.fillStyle = watermarkSettings.textColor;
        ctx.font = `${watermarkSettings.textSize}px ${watermarkSettings.fontFamily}`;
        
        // 文字对齐方式
        ctx.textAlign = watermarkSettings.position.includes('left') 
          ? 'left' 
          : watermarkSettings.position.includes('right') 
            ? 'right' 
            : 'center';
            
        ctx.textBaseline = watermarkSettings.position.includes('top') 
          ? 'top' 
          : watermarkSettings.position.includes('bottom') 
            ? 'bottom' 
            : 'middle';
            
        ctx.fillText(watermarkSettings.text, x, y);
      } else if (watermarkSettings.type === 'image' && watermarkSettings.imageUrl) {
        // 图片水印
        const watermarkImg = new window.Image();
        watermarkImg.src = watermarkSettings.imageUrl;
        
        await new Promise<void>((resolve, reject) => {
          watermarkImg.onload = () => resolve();
          watermarkImg.onerror = () => reject(new Error('水印图片加载失败'));
        });
        
        // 计算水印图片的大小 - 最大宽度为原图的1/4
        const maxWidth = canvas.width / 4;
        let wmWidth = watermarkImg.width;
        let wmHeight = watermarkImg.height;
        
        if (wmWidth > maxWidth) {
          const ratio = maxWidth / wmWidth;
          wmWidth = maxWidth;
          wmHeight = wmHeight * ratio;
        }
        
        // 调整图片水印位置
        if (watermarkSettings.position.includes('center')) {
          x -= wmWidth / 2;
        } else if (watermarkSettings.position.includes('right')) {
          x -= wmWidth;
        }
        
        if (watermarkSettings.position.includes('middle')) {
          y -= wmHeight / 2;
        } else if (watermarkSettings.position.includes('bottom')) {
          y -= wmHeight;
        }
        
        ctx.drawImage(watermarkImg, x, y, wmWidth, wmHeight);
      }
      
      // 恢复透明度
      ctx.globalAlpha = 1.0;
      
      // 转换为URL
      const processedImageUrl = canvas.toDataURL(file.type);
      setProcessedUrl(processedImageUrl);
      
      // 更新预览
      if (canvasRef.current) {
        const previewCtx = canvasRef.current.getContext('2d');
        if (previewCtx) {
          canvasRef.current.width = img.width;
          canvasRef.current.height = img.height;
          const previewImg = new window.Image();
          previewImg.src = processedImageUrl;
          
          previewImg.onload = () => {
            previewCtx.drawImage(previewImg, 0, 0, canvasRef.current!.width, canvasRef.current!.height);
          };
        }
      }
    } catch (err) {
      setError('处理图片时出现错误');
      console.error(err);
    } finally {
      setLoading(false);
    }
  };

  // 下载处理后的图片
  const downloadProcessedImage = () => {
    if (!processedUrl || !file) return;
    
    const a = document.createElement('a');
    a.href = processedUrl;
    a.download = `watermarked_${file.name}`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
  };

  // 格式化文件大小
  const formatFileSize = (bytes: number): string => {
    if (bytes < 1024) return bytes + ' B';
    else if (bytes < 1024 * 1024) return (bytes / 1024).toFixed(2) + ' KB';
    else return (bytes / (1024 * 1024)).toFixed(2) + ' MB';
  };

  // 重置所有
  const resetAll = () => {
    setFile(null);
    setWatermarkFile(null);
    setPreviewUrl(null);
    setProcessedUrl(null);
    setError(null);
    setWatermarkSettings({
      type: 'text',
      text: '版权所有',
      textColor: '#ffffff',
      textSize: 24,
      fontFamily: 'Arial',
      position: 'bottom-right',
      opacity: 50,
      padding: 20
    });
  };

  // 返回上一页
  const navigateBack = () => {
    router.back();
  };

  // 触发文件选择
  const triggerFileInput = () => {
    fileInputRef.current?.click();
  };

  // 触发水印图片选择
  const triggerWatermarkFileInput = () => {
    watermarkFileInputRef.current?.click();
  };

  // 设置文字位置的辅助函数
  const getPositionButtonClass = (position: WatermarkPosition) => {
    return `p-2 rounded ${watermarkSettings.position === position ? 'bg-blue-500 text-white' : 'bg-gray-200 dark:bg-gray-700'}`;
  };

  return (
    <div className="container mx-auto p-6">
      {/* 头部 */}
      <div className="flex items-center mb-6">
        <button 
          onClick={navigateBack}
          className="p-2 mr-4 rounded-full hover:bg-gray-100 dark:hover:bg-gray-800"
        >
          <ArrowLeft size={24} />
        </button>
        <h1 className="text-2xl font-bold">图片添加水印</h1>
      </div>

      {/* 工具说明 */}
      <div className="mb-8 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
        <h2 className="text-lg font-medium mb-2">工具说明</h2>
        <p className="text-gray-700 dark:text-gray-300">
          本工具可以帮助您给图片添加文字或图片水印，支持自定义水印位置、透明度等效果。
          所有处理过程均在您的浏览器本地完成，不会上传至服务器，保证您的数据安全。
        </p>
      </div>

      {/* 主要内容区 */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {/* 左侧：上传和预览区 */}
        <div className="md:col-span-2">
          <div className="mb-6 p-4 border border-gray-200 dark:border-gray-700 rounded-lg">
            <h2 className="text-lg font-medium mb-4">选择图片</h2>
            
            {/* 文件上传区 */}
            <div 
              onClick={triggerFileInput}
              className="border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg p-8 text-center cursor-pointer hover:border-blue-500 dark:hover:border-blue-400 mb-4"
            >
              <input
                ref={fileInputRef}
                type="file"
                onChange={handleFileChange}
                accept="image/*"
                className="hidden"
              />
              
              {!file ? (
                <div>
                  <Upload className="mx-auto h-12 w-12 text-gray-400" />
                  <p className="mt-2 text-sm text-gray-500 dark:text-gray-400">点击或拖拽图片到此区域</p>
                  <p className="text-xs text-gray-500 dark:text-gray-400">支持 JPG、PNG、WebP 等格式</p>
                </div>
              ) : (
                <div className="text-left">
                  <div className="flex items-center">
                    <FileType className="mr-2 text-blue-500" />
                    <span className="font-medium">{file.name}</span>
                  </div>
                  <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
                    {formatFileSize(file.size)}
                  </p>
                  <button 
                    onClick={(e) => {
                      e.stopPropagation();
                      setFile(null);
                      setPreviewUrl(null);
                      setProcessedUrl(null);
                    }}
                    className="mt-2 text-red-500 text-sm flex items-center"
                  >
                    <Trash size={16} className="mr-1" />
                    删除
                  </button>
                </div>
              )}
            </div>
            
            {/* 预览区 */}
            {previewUrl && (
              <div className="mt-6">
                <h3 className="text-md font-medium mb-2">图片预览</h3>
                <div className="relative border border-gray-200 dark:border-gray-700 rounded-lg overflow-hidden">
                  <div className="relative w-full h-auto" style={{ minHeight: '200px' }}>
                    {(processedUrl || previewUrl) && (
                      <Image 
                        src={processedUrl || previewUrl || ''} 
                        alt="图片预览" 
                        className="max-w-full h-auto"
                        fill
                        style={{ objectFit: 'contain' }}
                      />
                    )}
                  </div>
                  <canvas 
                    ref={canvasRef} 
                    className="hidden"
                  />
                </div>
              </div>
            )}
          </div>
        </div>
        
        {/* 右侧：水印设置 */}
        <div>
          <div className="mb-6 p-4 border border-gray-200 dark:border-gray-700 rounded-lg">
            <div className="flex items-center mb-4">
              <Settings size={18} className="mr-2" />
              <h2 className="text-lg font-medium">水印设置</h2>
            </div>
            
            {/* 水印类型选择 */}
            <div className="mb-4">
              <label className="block mb-2 text-sm font-medium">
                水印类型
              </label>
              <div className="flex space-x-4">
                <button
                  onClick={() => handleSettingsChange('type', 'text')}
                  className={`flex-1 py-2 px-4 rounded-md ${watermarkSettings.type === 'text' ? 'bg-blue-500 text-white' : 'bg-gray-200 dark:bg-gray-700'}`}
                >
                  <Type size={16} className="inline mr-1" />
                  文字水印
                </button>
                <button
                  onClick={() => handleSettingsChange('type', 'image')}
                  className={`flex-1 py-2 px-4 rounded-md ${watermarkSettings.type === 'image' ? 'bg-blue-500 text-white' : 'bg-gray-200 dark:bg-gray-700'}`}
                >
                  <ImageIcon size={16} className="inline mr-1" />
                  图片水印
                </button>
              </div>
            </div>
            
            {/* 文字水印设置 */}
            {watermarkSettings.type === 'text' && (
              <>
                <div className="mb-4">
                  <label className="block mb-2 text-sm font-medium">
                    水印文字
                  </label>
                  <input
                    type="text"
                    value={watermarkSettings.text}
                    onChange={(e) => handleSettingsChange('text', e.target.value)}
                    className="w-full p-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-800"
                  />
                </div>
                
                <div className="mb-4">
                  <label className="block mb-2 text-sm font-medium">
                    文字颜色
                  </label>
                  <input
                    type="color"
                    value={watermarkSettings.textColor}
                    onChange={(e) => handleSettingsChange('textColor', e.target.value)}
                    className="p-1 border border-gray-300 dark:border-gray-600 rounded w-full h-10"
                  />
                </div>
                
                <div className="mb-4">
                  <label className="block mb-2 text-sm font-medium">
                    文字大小: {watermarkSettings.textSize}px
                  </label>
                  <input
                    type="range"
                    min="8"
                    max="72"
                    value={watermarkSettings.textSize}
                    onChange={(e) => handleSettingsChange('textSize', parseInt(e.target.value))}
                    className="w-full"
                  />
                </div>
                
                <div className="mb-4">
                  <label className="block mb-2 text-sm font-medium">
                    字体选择
                  </label>
                  <select
                    value={watermarkSettings.fontFamily}
                    onChange={(e) => handleSettingsChange('fontFamily', e.target.value)}
                    className="w-full p-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-800"
                  >
                    <option value="Arial">Arial</option>
                    <option value="Helvetica">Helvetica</option>
                    <option value="Times New Roman">Times New Roman</option>
                    <option value="Courier New">Courier New</option>
                    <option value="Georgia">Georgia</option>
                    <option value="Verdana">Verdana</option>
                  </select>
                </div>
              </>
            )}
            
            {/* 图片水印设置 */}
            {watermarkSettings.type === 'image' && (
              <div className="mb-4">
                <label className="block mb-2 text-sm font-medium">
                  水印图片
                </label>
                <button
                  onClick={triggerWatermarkFileInput}
                  className="w-full p-4 border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg text-center cursor-pointer hover:border-blue-500 dark:hover:border-blue-400"
                >
                  <input
                    ref={watermarkFileInputRef}
                    type="file"
                    onChange={handleWatermarkFileChange}
                    accept="image/*"
                    className="hidden"
                  />
                  
                  {!watermarkFile ? (
                    <div>
                      <Upload className="mx-auto h-8 w-8 text-gray-400" />
                      <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">选择水印图片</p>
                    </div>
                  ) : (
                    <div className="flex items-center">
                      <div className="relative h-10 w-10 mr-2">
                        <Image 
                          src={watermarkSettings.imageUrl || ''} 
                          alt="水印预览" 
                          className="object-contain"
                          fill
                        />
                      </div>
                      <span className="text-sm truncate">{watermarkFile.name}</span>
                    </div>
                  )}
                </button>
              </div>
            )}
            
            {/* 通用水印设置 */}
            <div className="mb-4">
              <label className="block mb-2 text-sm font-medium">
                水印位置
              </label>
              <div className="grid grid-cols-3 gap-2">
                <button onClick={() => handleSettingsChange('position', 'top-left')} className={getPositionButtonClass('top-left')}>↖️</button>
                <button onClick={() => handleSettingsChange('position', 'top-center')} className={getPositionButtonClass('top-center')}>⬆️</button>
                <button onClick={() => handleSettingsChange('position', 'top-right')} className={getPositionButtonClass('top-right')}>↗️</button>
                
                <button onClick={() => handleSettingsChange('position', 'middle-left')} className={getPositionButtonClass('middle-left')}>⬅️</button>
                <button onClick={() => handleSettingsChange('position', 'middle-center')} className={getPositionButtonClass('middle-center')}>⚫</button>
                <button onClick={() => handleSettingsChange('position', 'middle-right')} className={getPositionButtonClass('middle-right')}>➡️</button>
                
                <button onClick={() => handleSettingsChange('position', 'bottom-left')} className={getPositionButtonClass('bottom-left')}>↙️</button>
                <button onClick={() => handleSettingsChange('position', 'bottom-center')} className={getPositionButtonClass('bottom-center')}>⬇️</button>
                <button onClick={() => handleSettingsChange('position', 'bottom-right')} className={getPositionButtonClass('bottom-right')}>↘️</button>
              </div>
            </div>
            
            <div className="mb-4">
              <label className="block mb-2 text-sm font-medium">
                水印透明度: {watermarkSettings.opacity}%
              </label>
              <input
                type="range"
                min="10"
                max="100"
                value={watermarkSettings.opacity}
                onChange={(e) => handleSettingsChange('opacity', parseInt(e.target.value))}
                className="w-full"
              />
            </div>
            
            <div className="mb-4">
              <label className="block mb-2 text-sm font-medium">
                边距: {watermarkSettings.padding}px
              </label>
              <input
                type="range"
                min="0"
                max="100"
                value={watermarkSettings.padding}
                onChange={(e) => handleSettingsChange('padding', parseInt(e.target.value))}
                className="w-full"
              />
            </div>
          </div>
        </div>
      </div>
      
      {/* 操作按钮 */}
      <div className="flex flex-wrap justify-center gap-4 mt-6">
        <button
          onClick={resetAll}
          className="px-4 py-2 bg-gray-200 dark:bg-gray-700 rounded-md flex items-center"
          disabled={loading}
        >
          <RefreshCw size={18} className="mr-2" />
          重置
        </button>
        
        <button
          onClick={applyWatermark}
          className="px-4 py-2 bg-blue-500 text-white rounded-md flex items-center"
          disabled={!file || loading}
        >
          {loading ? (
            <>
              <RefreshCw size={18} className="mr-2 animate-spin" />
              处理中...
            </>
          ) : (
            <>
              <ImageIcon size={18} className="mr-2" />
              应用水印
            </>
          )}
        </button>
        
        <button
          onClick={downloadProcessedImage}
          className="px-4 py-2 bg-green-500 text-white rounded-md flex items-center"
          disabled={!processedUrl || loading}
        >
          <Download size={18} className="mr-2" />
          下载图片
        </button>
      </div>
      
      {/* 错误提示 */}
      {error && (
        <div className="mt-6 p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg flex items-start">
          <AlertTriangle className="text-red-500 mr-2 flex-shrink-0 mt-0.5" size={16} />
          <p className="text-red-600 dark:text-red-400">{error}</p>
        </div>
      )}
    </div>
  );
}
