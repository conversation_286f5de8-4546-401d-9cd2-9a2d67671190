'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { ArrowLeft, Copy, Key, Lock, Unlock, AlertTriangle, RefreshCw } from 'lucide-react';
import crypto from 'crypto';
import { useLocale, useTranslations } from 'next-intl';

export default function DesEncryptionPage() {
  const locale = useLocale();
  const t = useTranslations('tools.desEncryption');
  
  const [input, setInput] = useState('');
  const [output, setOutput] = useState('');
  const [key, setKey] = useState('');
  const [iv, setIv] = useState('');
  const [mode, setMode] = useState('cbc');
  const [variant, setVariant] = useState('des');
  const [action, setAction] = useState<'encrypt' | 'decrypt'>('encrypt');
  const [padding, setPadding] = useState('pkcs7');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const router = useRouter();

  // 支持的DES变体
  const supportedVariants = [
    { value: 'des', label: t('settings.variant.des') },
    { value: 'des-ede3', label: t('settings.variant.des3') },
  ];

  // 支持的加密模式
  const supportedModes = [
    { value: 'cbc', label: 'CBC' },
    { value: 'ecb', label: 'ECB' },
    { value: 'cfb', label: 'CFB' },
    { value: 'ofb', label: 'OFB' },
  ];

  // 支持的填充模式
  const supportedPaddings = [
    { value: 'pkcs7', label: 'PKCS#7' },
    { value: 'nopadding', label: t('settings.padding.noPadding') }
  ];

  // 生成随机密钥
  const generateRandomKey = () => {
    // DES: 64位密钥，实际有效56位（8个字节）
    // 3DES: 192位密钥，实际有效168位（24个字节）
    const keyBytes = variant === 'des' ? 8 : 24;
    const randomKey = crypto.randomBytes(keyBytes).toString('hex');
    setKey(randomKey);
  };

  // 生成随机IV
  const generateRandomIv = () => {
    // DES块大小是64位（8个字节）
    const randomIv = crypto.randomBytes(8).toString('hex');
    setIv(randomIv);
  };

  // 加密处理
  const encrypt = () => {
    if (!input.trim()) {
      setError(t('errors.emptyEncryptText'));
      return;
    }

    if (!key.trim()) {
      setError(t('errors.emptyKey'));
      return;
    }

    // 对于需要IV的模式，检查IV
    if (mode !== 'ecb' && !iv.trim()) {
      setError(t('errors.emptyIv'));
      return;
    }

    setLoading(true);
    setError(null);

    try {
      // 准备密钥和IV
      const keyBuffer = Buffer.from(key, 'hex');
      let ivBuffer = undefined;
      
      if (mode !== 'ecb') {
        ivBuffer = Buffer.from(iv, 'hex');
      }
      
      // 创建密码器
      const algorithm = `${variant}-${mode}`;
      const cipher = crypto.createCipheriv(
        algorithm, 
        keyBuffer, 
        ivBuffer || null
      );
      
      // 指定填充
      if (padding === 'nopadding') {
        cipher.setAutoPadding(false);
      }
      
      // 加密数据
      let encrypted = cipher.update(input, 'utf8', 'base64');
      encrypted += cipher.final('base64');
      
      setOutput(encrypted);
    } catch (err) {
      setError(err instanceof Error ? `${t('errors.encryptError')}: ${err.message}` : t('errors.unknownEncryptError'));
    } finally {
      setLoading(false);
    }
  };

  // 解密处理
  const decrypt = () => {
    if (!input.trim()) {
      setError(t('errors.emptyDecryptText'));
      return;
    }

    if (!key.trim()) {
      setError(t('errors.emptyKey'));
      return;
    }

    // 对于需要IV的模式，检查IV
    if (mode !== 'ecb' && !iv.trim()) {
      setError(t('errors.emptyIv'));
      return;
    }

    setLoading(true);
    setError(null);

    try {
      // 准备密钥和IV
      const keyBuffer = Buffer.from(key, 'hex');
      let ivBuffer = undefined;
      
      if (mode !== 'ecb') {
        ivBuffer = Buffer.from(iv, 'hex');
      }
      
      // 创建解密器
      const algorithm = `${variant}-${mode}`;
      const decipher = crypto.createDecipheriv(
        algorithm, 
        keyBuffer, 
        ivBuffer || null
      );
      
      // 指定填充
      if (padding === 'nopadding') {
        decipher.setAutoPadding(false);
      }
      
      // 解密数据
      let decrypted = decipher.update(input, 'base64', 'utf8');
      decrypted += decipher.final('utf8');
      
      setOutput(decrypted);
    } catch (err) {
      setError(err instanceof Error ? `${t('errors.decryptError')}: ${err.message}` : t('errors.unknownDecryptError'));
    } finally {
      setLoading(false);
    }
  };

  // 执行加密或解密
  const handleProcess = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (action === 'encrypt') {
      encrypt();
    } else {
      decrypt();
    }
  };

  // 复制到剪贴板
  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
  };

  // 清空输入
  const clearInput = () => {
    setInput('');
    setOutput('');
    setError(null);
  };

  // 加载示例
  const loadExample = () => {
    setInput(action === 'encrypt' ? t('examples.encryptText') : t('examples.decryptText'));
    generateRandomKey();
    generateRandomIv();
    setError(null);
  };

  // 切换加密/解密模式
  const toggleAction = (newAction: 'encrypt' | 'decrypt') => {
    setAction(newAction);
    setInput('');
    setOutput('');
    setError(null);
  };

  // 变换DES算法时提示密钥要求
  const handleVariantChange = (newVariant: string) => {
    setVariant(newVariant);
    setKey('');
    setError(null);
  };

  return (
    <div className="min-h-screen bg-background p-6">
      <div className="max-w-6xl mx-auto">
        {/* 返回按钮 */}
        <button 
          onClick={() => router.back()}
          className="mb-6 flex items-center gap-2 text-primary hover:underline"
        >
          <ArrowLeft size={16} />
          <span>{t('navigation.back')}</span>
        </button>

        {/* 工具标题 */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold">{t('title')}</h1>
          <p className="text-muted-foreground mt-2">
            {t('description')}
          </p>
        </div>

        {/* 操作切换按钮 */}
        <div className="mb-6 bg-card border border-border rounded-lg p-2 inline-flex">
          <button
            onClick={() => toggleAction('encrypt')}
            className={`py-2 px-4 rounded-md flex items-center gap-2 ${
              action === 'encrypt'
                ? 'bg-primary text-primary-foreground'
                : 'text-muted-foreground hover:bg-accent'
            }`}
          >
            <Lock size={16} />
            <span>{t('modes.encrypt')}</span>
          </button>
          <button
            onClick={() => toggleAction('decrypt')}
            className={`py-2 px-4 rounded-md flex items-center gap-2 ${
              action === 'decrypt'
                ? 'bg-primary text-primary-foreground'
                : 'text-muted-foreground hover:bg-accent'
            }`}
          >
            <Unlock size={16} />
            <span>{t('modes.decrypt')}</span>
          </button>
        </div>

        {/* 加密/解密设置 */}
        <div className="mb-6 bg-card border border-border rounded-lg p-4">
          <h2 className="font-medium mb-4">DES设置</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {/* DES变体 */}
            <div>
              <label className="block text-sm mb-2">DES变体</label>
              <select
                value={variant}
                onChange={(e) => handleVariantChange(e.target.value)}
                className="w-full h-10 px-3 rounded bg-accent border border-border focus:outline-none focus:ring-2 focus:ring-primary"
              >
                {supportedVariants.map((variant) => (
                  <option key={variant.value} value={variant.value}>
                    {variant.label}
                  </option>
                ))}
              </select>
            </div>
            
            {/* 加密模式 */}
            <div>
              <label className="block text-sm mb-2">加密模式</label>
              <select
                value={mode}
                onChange={(e) => setMode(e.target.value)}
                className="w-full h-10 px-3 rounded bg-accent border border-border focus:outline-none focus:ring-2 focus:ring-primary"
              >
                {supportedModes.map((mode) => (
                  <option key={mode.value} value={mode.value}>
                    {mode.label}
                  </option>
                ))}
              </select>
            </div>
            
            {/* 填充模式 */}
            <div>
              <label className="block text-sm mb-2">填充模式</label>
              <select
                value={padding}
                onChange={(e) => setPadding(e.target.value)}
                className="w-full h-10 px-3 rounded bg-accent border border-border focus:outline-none focus:ring-2 focus:ring-primary"
              >
                {supportedPaddings.map((padding) => (
                  <option key={padding.value} value={padding.value}>
                    {padding.label}
                  </option>
                ))}
              </select>
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
          {/* 输入区 */}
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <h2 className="font-medium">
                {action === 'encrypt' ? '待加密文本' : '待解密文本'}
              </h2>
              <div className="flex items-center gap-2">
                <button 
                  onClick={clearInput}
                  className="text-xs px-2 py-1 rounded bg-accent hover:bg-accent/80 text-accent-foreground"
                >
                  清空
                </button>
                <button 
                  onClick={loadExample}
                  className="text-xs px-2 py-1 rounded bg-accent hover:bg-accent/80 text-accent-foreground"
                >
                  加载示例
                </button>
                <button 
                  onClick={() => copyToClipboard(input)}
                  className="p-1 rounded hover:bg-accent"
                  title="复制"
                >
                  <Copy size={14} />
                </button>
              </div>
            </div>
            <textarea
              value={input}
              onChange={(e) => setInput(e.target.value)}
              placeholder={action === 'encrypt' ? '在此输入需要加密的文本...' : '在此输入需要解密的文本...'}
              className="w-full h-[200px] p-4 font-mono text-sm rounded-lg bg-accent border border-border focus:outline-none focus:ring-2 focus:ring-primary resize-none"
            ></textarea>
            
            {/* 密钥输入 */}
            <div>
              <div className="flex items-center justify-between mb-2">
                <label className="text-sm font-medium">密钥 (Hex格式)</label>
                <div className="flex items-center gap-2">
                  <button 
                    onClick={generateRandomKey}
                    className="text-xs px-2 py-1 rounded bg-accent hover:bg-accent/80 text-accent-foreground flex items-center gap-1"
                  >
                    <RefreshCw size={12} />
                    <span>生成随机密钥</span>
                  </button>
                  <button 
                    onClick={() => copyToClipboard(key)}
                    className="p-1 rounded hover:bg-accent"
                    title="复制"
                  >
                    <Copy size={14} />
                  </button>
                </div>
              </div>
              <input
                type="text"
                value={key}
                onChange={(e) => setKey(e.target.value)}
                placeholder="输入16进制格式的密钥..."
                className="w-full h-10 px-3 font-mono text-sm rounded bg-accent border border-border focus:outline-none focus:ring-2 focus:ring-primary"
              />
              <p className="text-xs text-muted-foreground mt-1">
                {variant === 'des' 
                  ? 'DES需要16个十六进制字符 (8字节)' 
                  : '3DES需要48个十六进制字符 (24字节)'}
              </p>
            </div>
            
            {/* IV输入 (仅对需要IV的模式显示) */}
            {mode !== 'ecb' && (
              <div>
                <div className="flex items-center justify-between mb-2">
                  <label className="text-sm font-medium">初始化向量 IV (Hex格式)</label>
                  <div className="flex items-center gap-2">
                    <button 
                      onClick={generateRandomIv}
                      className="text-xs px-2 py-1 rounded bg-accent hover:bg-accent/80 text-accent-foreground flex items-center gap-1"
                    >
                      <RefreshCw size={12} />
                      <span>生成随机IV</span>
                    </button>
                    <button 
                      onClick={() => copyToClipboard(iv)}
                      className="p-1 rounded hover:bg-accent"
                      title="复制"
                    >
                      <Copy size={14} />
                    </button>
                  </div>
                </div>
                <input
                  type="text"
                  value={iv}
                  onChange={(e) => setIv(e.target.value)}
                  placeholder="输入16进制格式的初始化向量..."
                  className="w-full h-10 px-3 font-mono text-sm rounded bg-accent border border-border focus:outline-none focus:ring-2 focus:ring-primary"
                />
                <p className="text-xs text-muted-foreground mt-1">
                  IV需要16个十六进制字符 (8字节)
                </p>
              </div>
            )}
            
            {error && (
              <div className="flex items-center gap-2 text-red-500 text-sm">
                <AlertTriangle size={14} />
                <span>{error}</span>
              </div>
            )}
          </div>

          {/* 结果区 */}
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <h2 className="font-medium">
                {action === 'encrypt' ? '加密结果' : '解密结果'}
              </h2>
              {output && (
                <button 
                  onClick={() => copyToClipboard(output)}
                  className="text-xs px-2 py-1 rounded bg-primary hover:bg-primary/90 text-primary-foreground flex items-center gap-1"
                >
                  <Copy size={14} />
                  <span>复制结果</span>
                </button>
              )}
            </div>
            <div className="h-[350px] overflow-y-auto rounded-lg bg-accent border border-border p-4">
              {output ? (
                <div className="font-mono text-sm break-all whitespace-pre-wrap">
                  {output}
                </div>
              ) : (
                <div className="h-full flex items-center justify-center text-center text-muted-foreground">
                  <div>
                    {action === 'encrypt' ? (
                      <Lock size={28} className="mx-auto mb-2 opacity-40" />
                    ) : (
                      <Unlock size={28} className="mx-auto mb-2 opacity-40" />
                    )}
                    <p>{action === 'encrypt' ? '加密结果将显示在这里' : '解密结果将显示在这里'}</p>
                    <p className="text-xs mt-1">点击下方按钮开始{action === 'encrypt' ? '加密' : '解密'}操作</p>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* 执行按钮 */}
        <div className="flex justify-center mb-8">
          <button
            onClick={handleProcess}
            disabled={loading || !input.trim() || !key.trim() || (mode !== 'ecb' && !iv.trim())}
            className="h-12 px-8 rounded-lg bg-primary text-primary-foreground hover:bg-primary/90 flex items-center gap-3 disabled:opacity-70 disabled:cursor-not-allowed"
          >
            {loading ? (
              <>
                <span className="animate-spin h-5 w-5 border-2 border-white border-t-transparent rounded-full"></span>
                <span>{action === 'encrypt' ? '加密中...' : '解密中...'}</span>
              </>
            ) : (
              <>
                {action === 'encrypt' ? <Lock size={18} /> : <Unlock size={18} />}
                <span>{action === 'encrypt' ? '加密' : '解密'}</span>
              </>
            )}
          </button>
        </div>

        {/* 信息说明 */}
        <div className="bg-card border border-border rounded-lg p-6">
          <h3 className="font-medium mb-4">关于DES加密</h3>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h4 className="font-medium mb-2">DES算法特点</h4>
              <ul className="list-disc list-inside mb-4 text-muted-foreground text-sm space-y-1">
                <li>数据加密标准(Data Encryption Standard)</li>
                <li>1977年由美国联邦政府发布的对称加密算法</li>
                <li>使用56位有效密钥长度（实际64位，含8位校验位）</li>
                <li>使用Feistel网络结构，包含16轮运算</li>
                <li>由于密钥长度较短，不再被视为安全算法</li>
                <li>3DES（三重DES）是DES的增强版本，提高了安全性</li>
              </ul>
            </div>
            
            <div>
              <h4 className="font-medium mb-2">加密模式和3DES</h4>
              <ul className="list-disc list-inside mb-4 text-muted-foreground text-sm space-y-1">
                <li>ECB (电子密码本): 最简单模式，但安全性低，不推荐</li>
                <li>CBC (密码块链接): 需要IV，安全性较高，常用</li>
                <li>CFB (密码反馈): 将块密码转换为流密码</li>
                <li>OFB (输出反馈): 生成独立于明文的密钥流</li>
                <li>3DES: 使用3个DES密钥，对数据进行加密-解密-加密操作</li>
                <li>3DES有效密钥长度为168位，安全性更高但速度较慢</li>
              </ul>
            </div>
          </div>
          
          <div className="mt-4 text-sm text-muted-foreground">
            <p>安全使用提示：</p>
            <ul className="list-disc list-inside mt-2 space-y-1">
              <li>普通DES已不再被推荐用于新系统，密钥长度过短</li>
              <li>3DES是DES的加强版，但速度较慢，新系统推荐使用AES</li>
              <li>避免使用ECB模式，因其无法隐藏数据模式</li>
              <li>CBC是最常用的模式，需要随机IV确保安全</li>
              <li>DES使用64位(8字节)数据块，所有模式都需要考虑填充</li>
              <li>对于需要兼容旧系统的场景，3DES是较好的选择</li>
              <li>使用随机生成的密钥和IV，不要使用可预测的值</li>
              <li>如无特殊需求，应优先选择现代加密算法如AES</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
}
