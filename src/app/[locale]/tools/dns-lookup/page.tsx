'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { ArrowLeft, Search, Download, Copy, RefreshCw, Globe, Activity, File } from 'lucide-react';

// DNS记录类型选项
const recordTypes = [
  { value: 'A', label: 'A (IPv4地址)' },
  { value: 'AAAA', label: 'AAAA (IPv6地址)' },
  { value: 'MX', label: 'MX (邮件交换)' },
  { value: 'TXT', label: 'TXT (文本记录)' },
  { value: 'NS', label: 'NS (名称服务器)' },
  { value: 'CNAME', label: 'CNAME (规范名称)' },
  { value: 'SOA', label: 'SOA (权威记录)' },
  { value: 'CAA', label: 'CAA (证书颁发机构授权)' },
  { value: 'ALL', label: '全部记录' },
];

export default function DnsLookupPage() {
  const [domain, setDomain] = useState('');
  const [recordType, setRecordType] = useState('A');
  const [loading, setLoading] = useState(false);
  const [result, setResult] = useState<any | null>(null);
  const [error, setError] = useState<string | null>(null);
  const router = useRouter();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!domain) {
      setError('请输入域名');
      return;
    }
    
    // 域名格式验证
    const domainRegex = /^([a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?\.)+[a-zA-Z]{2,}$/;
    if (!domainRegex.test(domain)) {
      setError('请输入有效的域名，如：wenhaofree.com');
      return;
    }
    
    setLoading(true);
    setError(null);
    
    try {
      const response = await fetch(`/api/dns-lookup?domain=${encodeURIComponent(domain)}&type=${encodeURIComponent(recordType)}`);
      const data = await response.json();
      
      if (!response.ok) {
        throw new Error(data.error || '查询失败');
      }
      
      setResult(data);
    } catch (err) {
      setError(err instanceof Error ? err.message : '查询失败，请稍后重试');
    } finally {
      setLoading(false);
    }
  };

  const copyToClipboard = () => {
    if (result) {
      navigator.clipboard.writeText(JSON.stringify(result, null, 2));
      // 可以添加一个通知提示复制成功
    }
  };

  const downloadResult = () => {
    if (result) {
      // 创建要下载的JSON字符串
      const jsonString = JSON.stringify(result, null, 2);
      const blob = new Blob([jsonString], { type: 'application/json' });
      const url = URL.createObjectURL(blob);
      
      // 创建一个下载链接并触发点击
      const a = document.createElement('a');
      a.href = url;
      a.download = `dns-lookup-${domain}-${recordType}.json`;
      document.body.appendChild(a);
      a.click();
      
      // 清理
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
    }
  };

  const refreshQuery = () => {
    if (domain) {
      handleSubmit({ preventDefault: () => {} } as React.FormEvent);
    }
  };

  const renderRecordDetails = (records: any[], type: string) => {
    if (!records || records.length === 0) {
      return <p className="text-muted-foreground text-sm">没有找到记录</p>;
    }

    // 根据记录类型渲染不同的内容
    switch (type) {
      case 'A':
      case 'AAAA':
        return (
          <div className="space-y-2">
            {records.map((record, index) => (
              <div key={index} className="bg-accent/50 p-3 rounded-md">
                <div className="flex justify-between">
                  <span className="text-muted-foreground">IP地址</span>
                  <span>{record.address}</span>
                </div>
                {record.ttl && (
                  <div className="flex justify-between mt-1">
                    <span className="text-muted-foreground">TTL</span>
                    <span>{record.ttl}秒</span>
                  </div>
                )}
              </div>
            ))}
          </div>
        );
      
      case 'MX':
        return (
          <div className="space-y-2">
            {records.map((record, index) => (
              <div key={index} className="bg-accent/50 p-3 rounded-md">
                <div className="flex justify-between">
                  <span className="text-muted-foreground">邮件服务器</span>
                  <span>{record.exchange}</span>
                </div>
                <div className="flex justify-between mt-1">
                  <span className="text-muted-foreground">优先级</span>
                  <span>{record.priority}</span>
                </div>
                {record.ttl && (
                  <div className="flex justify-between mt-1">
                    <span className="text-muted-foreground">TTL</span>
                    <span>{record.ttl}秒</span>
                  </div>
                )}
              </div>
            ))}
          </div>
        );
      
      case 'TXT':
        return (
          <div className="space-y-2">
            {records.map((record, index) => (
              <div key={index} className="bg-accent/50 p-3 rounded-md">
                <div className="flex flex-col">
                  <span className="text-muted-foreground mb-1">文本内容</span>
                  <span className="font-mono text-sm break-all">{record.value}</span>
                </div>
                {record.ttl && (
                  <div className="flex justify-between mt-2">
                    <span className="text-muted-foreground">TTL</span>
                    <span>{record.ttl}秒</span>
                  </div>
                )}
              </div>
            ))}
          </div>
        );
      
      case 'NS':
        return (
          <div className="space-y-2">
            {records.map((record, index) => (
              <div key={index} className="bg-accent/50 p-3 rounded-md">
                <div className="flex justify-between">
                  <span className="text-muted-foreground">名称服务器</span>
                  <span>{record.value}</span>
                </div>
                {record.ttl && (
                  <div className="flex justify-between mt-1">
                    <span className="text-muted-foreground">TTL</span>
                    <span>{record.ttl}秒</span>
                  </div>
                )}
              </div>
            ))}
          </div>
        );
      
      case 'CNAME':
        return (
          <div className="space-y-2">
            {records.map((record, index) => (
              <div key={index} className="bg-accent/50 p-3 rounded-md">
                <div className="flex justify-between">
                  <span className="text-muted-foreground">规范名称</span>
                  <span>{record.value}</span>
                </div>
                {record.ttl && (
                  <div className="flex justify-between mt-1">
                    <span className="text-muted-foreground">TTL</span>
                    <span>{record.ttl}秒</span>
                  </div>
                )}
              </div>
            ))}
          </div>
        );
      
      case 'SOA':
        return (
          <div className="space-y-2">
            {records.map((record, index) => (
              <div key={index} className="bg-accent/50 p-3 rounded-md">
                <div className="flex justify-between">
                  <span className="text-muted-foreground">主域名服务器</span>
                  <span>{record.nsname}</span>
                </div>
                <div className="flex justify-between mt-1">
                  <span className="text-muted-foreground">管理员邮箱</span>
                  <span>{record.hostmaster}</span>
                </div>
                <div className="flex justify-between mt-1">
                  <span className="text-muted-foreground">序列号</span>
                  <span>{record.serial}</span>
                </div>
                <div className="flex justify-between mt-1">
                  <span className="text-muted-foreground">刷新间隔</span>
                  <span>{record.refresh}秒</span>
                </div>
                <div className="flex justify-between mt-1">
                  <span className="text-muted-foreground">重试间隔</span>
                  <span>{record.retry}秒</span>
                </div>
                <div className="flex justify-between mt-1">
                  <span className="text-muted-foreground">过期时间</span>
                  <span>{record.expire}秒</span>
                </div>
                <div className="flex justify-between mt-1">
                  <span className="text-muted-foreground">最小TTL</span>
                  <span>{record.minttl}秒</span>
                </div>
              </div>
            ))}
          </div>
        );
      
      case 'CAA':
        return (
          <div className="space-y-2">
            {records.map((record, index) => (
              <div key={index} className="bg-accent/50 p-3 rounded-md">
                <div className="flex justify-between">
                  <span className="text-muted-foreground">标记</span>
                  <span>{record.flags}</span>
                </div>
                <div className="flex justify-between mt-1">
                  <span className="text-muted-foreground">标签</span>
                  <span>{record.tag}</span>
                </div>
                <div className="flex justify-between mt-1">
                  <span className="text-muted-foreground">值</span>
                  <span>{record.value}</span>
                </div>
                {record.ttl && (
                  <div className="flex justify-between mt-1">
                    <span className="text-muted-foreground">TTL</span>
                    <span>{record.ttl}秒</span>
                  </div>
                )}
              </div>
            ))}
          </div>
        );
      
      default:
        return (
          <pre className="bg-accent/50 p-3 rounded-md overflow-x-auto">
            <code>{JSON.stringify(records, null, 2)}</code>
          </pre>
        );
    }
  };

  return (
    <div className="min-h-screen bg-background p-6">
      <div className="max-w-5xl mx-auto">
        {/* 返回按钮 */}
        <button 
          onClick={() => router.back()}
          className="mb-6 flex items-center gap-2 text-primary hover:underline"
        >
          <ArrowLeft size={16} />
          <span>返回工具列表</span>
        </button>

        {/* 工具标题 */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold">DNS解析</h1>
          <p className="text-muted-foreground mt-2">
            查询域名的DNS记录，包括A、AAAA、MX、TXT、NS等类型记录
          </p>
        </div>

        {/* 查询表单 */}
        <div className="bg-card border border-border rounded-lg p-6 mb-6">
          <form onSubmit={handleSubmit} className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label htmlFor="domain" className="block text-sm font-medium mb-2">
                  域名
                </label>
                <input
                  id="domain"
                  type="text"
                  placeholder="输入域名，如: wenhaofree.com"
                  value={domain}
                  onChange={(e) => setDomain(e.target.value)}
                  className="w-full h-10 px-4 rounded-lg bg-accent border border-border focus:outline-none focus:ring-2 focus:ring-primary"
                />
              </div>
              
              <div>
                <label htmlFor="recordType" className="block text-sm font-medium mb-2">
                  记录类型
                </label>
                <select
                  id="recordType"
                  value={recordType}
                  onChange={(e) => setRecordType(e.target.value)}
                  className="w-full h-10 px-4 rounded-lg bg-accent border border-border focus:outline-none focus:ring-2 focus:ring-primary"
                >
                  {recordTypes.map((type) => (
                    <option key={type.value} value={type.value}>
                      {type.label}
                    </option>
                  ))}
                </select>
              </div>
            </div>
              
            {error && <p className="text-red-500 text-sm">{error}</p>}
            
            <div className="flex justify-end">
              <button
                type="submit"
                disabled={loading}
                className="h-10 px-6 rounded-lg bg-primary text-primary-foreground hover:bg-primary/90 flex items-center gap-2 disabled:opacity-70"
              >
                {loading ? (
                  <>
                    <span className="animate-spin h-4 w-4 border-2 border-white border-t-transparent rounded-full"></span>
                    <span>查询中...</span>
                  </>
                ) : (
                  <>
                    <Search size={16} />
                    <span>查询</span>
                  </>
                )}
              </button>
            </div>
          </form>
        </div>

        {/* 结果显示 */}
        {result && (
          <div className="bg-card border border-border rounded-lg mb-6">
            <div className="p-4 border-b border-border flex items-center justify-between">
              <h3 className="font-medium">查询结果: {domain} ({recordType === 'ALL' ? '全部记录' : recordType})</h3>
              <div className="flex items-center gap-2">
                <button 
                  onClick={refreshQuery}
                  className="p-2 rounded hover:bg-accent"
                  title="刷新"
                >
                  <RefreshCw size={16} />
                </button>
                <button 
                  onClick={copyToClipboard}
                  className="p-2 rounded hover:bg-accent"
                  title="复制到剪贴板"
                >
                  <Copy size={16} />
                </button>
                <button 
                  onClick={downloadResult}
                  className="p-2 rounded hover:bg-accent"
                  title="下载结果"
                >
                  <Download size={16} />
                </button>
              </div>
            </div>
            
            <div className="p-6">
              {recordType === 'ALL' ? (
                <div className="space-y-6">
                  {Object.entries(result).map(([type, records]) => 
                    records && (records as any[]).length > 0 ? (
                      <div key={type} className="space-y-3">
                        <h4 className="font-medium flex items-center gap-2">
                          <Activity size={16} className="text-primary" />
                          {type} 记录
                        </h4>
                        {renderRecordDetails(records as any[], type)}
                      </div>
                    ) : null
                  )}
                </div>
              ) : (
                <div className="space-y-3">
                  <h4 className="font-medium flex items-center gap-2">
                    <Activity size={16} className="text-primary" />
                    {recordType} 记录
                  </h4>
                  {renderRecordDetails(result.records || [], recordType)}
                </div>
              )}
            </div>
            
            <div className="p-4 text-sm text-muted-foreground bg-accent/30 rounded-b-lg">
              注意：DNS查询结果可能会因为DNS传播延迟、缓存或权限问题而有所不同。
            </div>
          </div>
        )}

        {/* 使用说明 */}
        <div className="mt-8">
          <h2 className="text-xl font-semibold mb-4">使用说明</h2>
          <div className="bg-card border border-border rounded-lg p-6">
            <ol className="list-decimal list-inside space-y-2">
              <li>在"域名"输入框中输入您想要查询的域名（例如：google.com）</li>
              <li>从下拉菜单中选择您想要查询的DNS记录类型</li>
              <li>点击"查询"按钮获取DNS记录</li>
              <li>查询结果将显示所选记录类型的详细信息</li>
              <li>您可以使用工具栏上的按钮复制或下载查询结果</li>
            </ol>
            <div className="mt-4 p-4 bg-accent/50 rounded-lg">
              <h3 className="text-sm font-semibold mb-2">常见DNS记录类型说明：</h3>
              <ul className="text-sm text-muted-foreground space-y-1">
                <li><strong>A记录</strong>：将域名映射到IPv4地址</li>
                <li><strong>AAAA记录</strong>：将域名映射到IPv6地址</li>
                <li><strong>MX记录</strong>：指定接收邮件的服务器</li>
                <li><strong>TXT记录</strong>：存储文本信息，通常用于验证域名所有权或SPF记录</li>
                <li><strong>NS记录</strong>：指定域名的DNS服务器</li>
                <li><strong>CNAME记录</strong>：为域名创建别名</li>
                <li><strong>SOA记录</strong>：包含域名的管理信息</li>
                <li><strong>CAA记录</strong>：指定哪些证书颁发机构可以为域名颁发SSL证书</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
