'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { ArrowLeft, Copy, Upload, Trash, Check, RefreshCw } from 'lucide-react';

type CaseType = 'uppercase' | 'lowercase' | 'capitalize' | 'title' | 'alternating' | 'inverse' | 'sentence';

export default function CaseConverterPage() {
  const [text, setText] = useState<string>('');
  const [convertedText, setConvertedText] = useState<string>('');
  const [copySuccess, setCopySuccess] = useState<boolean>(false);
  const router = useRouter();

  // 示例文本
  const exampleText = `这是一个示例文本，用于演示大小写转换功能。
This is an example text for demonstrating case conversion.
包含中文、English和数字123。`;

  // 处理文本输入变化
  const handleTextChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setText(e.target.value);
  };

  // 加载示例
  const loadExample = () => {
    setText(exampleText);
  };

  // 清空输入
  const clearText = () => {
    setText('');
    setConvertedText('');
  };

  // 处理文件上传
  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    const reader = new FileReader();
    reader.onload = (e) => {
      const content = e.target?.result as string;
      setText(content);
    };
    reader.readAsText(file);
  };

  // 复制转换结果
  const copyResult = () => {
    navigator.clipboard.writeText(convertedText)
      .then(() => {
        setCopySuccess(true);
        setTimeout(() => setCopySuccess(false), 2000);
      })
      .catch(err => {
        console.error('复制失败:', err);
      });
  };

  // 转换大小写
  const convertCase = (type: CaseType) => {
    if (!text) {
      setConvertedText('');
      return;
    }

    let result = text;

    switch (type) {
      case 'uppercase':
        result = text.toUpperCase();
        break;
      case 'lowercase':
        result = text.toLowerCase();
        break;
      case 'capitalize':
        result = text.split(' ').map(word => 
          word.charAt(0).toUpperCase() + word.slice(1).toLowerCase()
        ).join(' ');
        break;
      case 'title':
        result = text.split(' ').map(word => 
          word.charAt(0).toUpperCase() + word.slice(1).toLowerCase()
        ).join(' ');
        break;
      case 'alternating':
        result = text.split('').map((char, index) => 
          index % 2 === 0 ? char.toLowerCase() : char.toUpperCase()
        ).join('');
        break;
      case 'inverse':
        result = text.split('').map(char => 
          char === char.toUpperCase() ? char.toLowerCase() : char.toUpperCase()
        ).join('');
        break;
      case 'sentence':
        result = text.split('. ').map(sentence => 
          sentence.charAt(0).toUpperCase() + sentence.slice(1).toLowerCase()
        ).join('. ');
        break;
      default:
        result = text;
    }

    setConvertedText(result);
  };

  return (
    <div className="container mx-auto p-6">
      {/* 头部 */}
      <div className="flex items-center mb-6">
        <button 
          onClick={() => router.back()}
          className="p-2 mr-4 rounded-full hover:bg-gray-100 dark:hover:bg-gray-800"
        >
          <ArrowLeft size={24} />
        </button>
        <h1 className="text-2xl font-bold">大小写转换工具</h1>
      </div>

      {/* 工具说明 */}
      <div className="mb-8 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
        <h2 className="text-lg font-medium mb-2">工具说明</h2>
        <p className="text-gray-700 dark:text-gray-300">
          本工具可以帮助您快速转换文本的大小写格式，支持多种转换模式，包括全大写、全小写、首字母大写、
          标题格式、交替大小写、大小写反转和句子格式。所有处理都在浏览器本地完成，不会上传您的文本。
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {/* 输入区域 */}
        <div className="md:col-span-2 space-y-4">
          <div className="flex justify-between items-center mb-2">
            <h2 className="text-lg font-medium">输入文本</h2>
            <div className="flex space-x-2">
              <button
                onClick={loadExample}
                className="text-xs p-1 px-2 rounded bg-gray-100 hover:bg-gray-200 dark:bg-gray-800 dark:hover:bg-gray-700"
              >
                加载示例
              </button>
              <button
                onClick={clearText}
                className="text-xs p-1 px-2 rounded bg-gray-100 hover:bg-gray-200 dark:bg-gray-800 dark:hover:bg-gray-700 flex items-center"
              >
                <Trash size={12} className="mr-1" />
                清空
              </button>
              <label className="text-xs p-1 px-2 rounded bg-gray-100 hover:bg-gray-200 dark:bg-gray-800 dark:hover:bg-gray-700 flex items-center cursor-pointer">
                <Upload size={12} className="mr-1" />
                上传文件
                <input
                  type="file"
                  accept=".txt,.md,.csv,.json,.html,.xml,.js,.css,.ts"
                  onChange={handleFileUpload}
                  className="hidden"
                />
              </label>
            </div>
          </div>
          
          <textarea
            value={text}
            onChange={handleTextChange}
            placeholder="在此输入或粘贴文本..."
            className="w-full h-[400px] p-4 border border-gray-300 dark:border-gray-700 rounded-lg bg-white dark:bg-gray-900 focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
        </div>

        {/* 转换选项和结果 */}
        <div className="space-y-4">
          <div className="flex justify-between items-center mb-2">
            <h2 className="text-lg font-medium">转换结果</h2>
            <button
              onClick={copyResult}
              className="text-xs p-1 px-2 rounded bg-gray-100 hover:bg-gray-200 dark:bg-gray-800 dark:hover:bg-gray-700 flex items-center"
              disabled={!convertedText}
            >
              {copySuccess ? <Check size={12} className="mr-1" /> : <Copy size={12} className="mr-1" />}
              {copySuccess ? '已复制' : '复制结果'}
            </button>
          </div>

          {/* 转换选项 */}
          <div className="bg-white dark:bg-gray-900 border border-gray-300 dark:border-gray-700 rounded-lg p-4 mb-4">
            <h3 className="text-base font-medium mb-3">转换选项</h3>
            <div className="grid grid-cols-1 gap-2">
              <button
                onClick={() => convertCase('uppercase')}
                className="text-left p-2 rounded hover:bg-gray-100 dark:hover:bg-gray-800"
              >
                全大写 (UPPERCASE)
              </button>
              <button
                onClick={() => convertCase('lowercase')}
                className="text-left p-2 rounded hover:bg-gray-100 dark:hover:bg-gray-800"
              >
                全小写 (lowercase)
              </button>
              <button
                onClick={() => convertCase('capitalize')}
                className="text-left p-2 rounded hover:bg-gray-100 dark:hover:bg-gray-800"
              >
                首字母大写 (Capitalize)
              </button>
              <button
                onClick={() => convertCase('title')}
                className="text-left p-2 rounded hover:bg-gray-100 dark:hover:bg-gray-800"
              >
                标题格式 (Title Case)
              </button>
              <button
                onClick={() => convertCase('alternating')}
                className="text-left p-2 rounded hover:bg-gray-100 dark:hover:bg-gray-800"
              >
                交替大小写 (aLtErNaTiNg)
              </button>
              <button
                onClick={() => convertCase('inverse')}
                className="text-left p-2 rounded hover:bg-gray-100 dark:hover:bg-gray-800"
              >
                大小写反转 (iNvErSe)
              </button>
              <button
                onClick={() => convertCase('sentence')}
                className="text-left p-2 rounded hover:bg-gray-100 dark:hover:bg-gray-800"
              >
                句子格式 (Sentence case)
              </button>
            </div>
          </div>

          {/* 转换结果 */}
          <div className="bg-white dark:bg-gray-900 border border-gray-300 dark:border-gray-700 rounded-lg p-4">
            <h3 className="text-base font-medium mb-3">转换结果</h3>
            <div className="font-mono text-sm whitespace-pre-wrap">
              {convertedText || '转换结果将显示在这里...'}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
} 