'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { ArrowLeft, Search, Download, Copy, RefreshCw, ArrowRightLeft, AlertTriangle } from 'lucide-react';
import * as yaml from 'js-yaml';

export default function JsonToYamlPage() {
  const [jsonInput, setJsonInput] = useState('');
  const [yamlOutput, setYamlOutput] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const router = useRouter();

  // JSON格式化函数
  const formatJson = (jsonString: string): string => {
    try {
      const obj = JSON.parse(jsonString);
      return JSON.stringify(obj, null, 2);
    } catch (err) {
      return jsonString;
    }
  };

  // 转换处理函数
  const handleConvert = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!jsonInput.trim()) {
      setError('请输入JSON数据');
      return;
    }
    
    setLoading(true);
    setError(null);
    
    try {
      // 解析JSON
      const jsonObj = JSON.parse(jsonInput);
      
      // 转换为YAML
      const yamlStr = yaml.dump(jsonObj, {
        indent: 2,
        lineWidth: -1, // 不限制行宽
        noRefs: true, // 避免YAML引用标记
        noCompatMode: true, // 使用更现代的YAML格式
      });
      
      setYamlOutput(yamlStr);
    } catch (err) {
      setError(err instanceof Error ? `JSON解析错误: ${err.message}` : '无效的JSON格式');
    } finally {
      setLoading(false);
    }
  };

  // 示例JSON数据
  const loadExample = () => {
    const exampleJson = {
      person: {
        name: "张三",
        age: 28,
        email: "<EMAIL>",
        isActive: true,
        address: {
          city: "北京",
          street: "朝阳区",
          postcode: "100000"
        },
        hobbies: ["读书", "旅行", "编程"],
        education: [
          {
            degree: "学士",
            major: "计算机科学",
            year: 2018
          },
          {
            degree: "硕士",
            major: "人工智能",
            year: 2020
          }
        ]
      },
      company: {
        name: "科技有限公司",
        employees: 250,
        departments: ["研发", "市场", "销售", "人力资源"]
      },
      settings: {
        theme: "dark",
        notifications: {
          email: true,
          sms: false,
          push: true
        }
      }
    };
    
    setJsonInput(JSON.stringify(exampleJson, null, 2));
    setError(null);
  };

  // 复制到剪贴板
  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
  };

  // 下载YAML结果
  const downloadYaml = () => {
    if (yamlOutput) {
      const blob = new Blob([yamlOutput], { type: 'text/yaml' });
      const url = URL.createObjectURL(blob);
      
      const a = document.createElement('a');
      a.href = url;
      a.download = 'converted.yaml';
      document.body.appendChild(a);
      a.click();
      
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
    }
  };

  // 格式化JSON输入
  const beautifyJson = () => {
    if (!jsonInput.trim()) {
      setError('请输入JSON数据以进行格式化');
      return;
    }
    
    try {
      const formattedJson = formatJson(jsonInput);
      setJsonInput(formattedJson);
      setError(null);
    } catch (err) {
      setError(err instanceof Error ? `格式化错误: ${err.message}` : '格式化时发生错误');
    }
  };

  return (
    <div className="min-h-screen bg-background p-6">
      <div className="max-w-6xl mx-auto">
        {/* 返回按钮 */}
        <button 
          onClick={() => router.back()}
          className="mb-6 flex items-center gap-2 text-primary hover:underline"
        >
          <ArrowLeft size={16} />
          <span>返回工具列表</span>
        </button>

        {/* 工具标题 */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold">JSON转YAML</h1>
          <p className="text-muted-foreground mt-2">
            将JSON数据格式转换为YAML格式，保持数据结构不变
          </p>
        </div>

        {/* 转换区域 */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
          {/* JSON输入区 */}
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <h2 className="font-medium">JSON输入</h2>
              <div className="flex items-center gap-2">
                <button 
                  onClick={beautifyJson}
                  className="text-xs px-2 py-1 rounded bg-accent hover:bg-accent/80 text-accent-foreground"
                >
                  格式化
                </button>
                <button 
                  onClick={loadExample}
                  className="text-xs px-2 py-1 rounded bg-accent hover:bg-accent/80 text-accent-foreground"
                >
                  加载示例
                </button>
                <button 
                  onClick={() => copyToClipboard(jsonInput)}
                  className="p-1 rounded hover:bg-accent"
                  title="复制"
                >
                  <Copy size={14} />
                </button>
              </div>
            </div>
            <textarea
              value={jsonInput}
              onChange={(e) => setJsonInput(e.target.value)}
              placeholder="在此粘贴JSON数据..."
              className="w-full h-[500px] p-4 font-mono text-sm rounded-lg bg-accent border border-border focus:outline-none focus:ring-2 focus:ring-primary resize-none"
            ></textarea>
            {error && (
              <div className="flex items-center gap-2 text-red-500 text-sm">
                <AlertTriangle size={14} />
                <span>{error}</span>
              </div>
            )}
          </div>

          {/* YAML输出区 */}
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <h2 className="font-medium">YAML输出</h2>
              <div className="flex items-center gap-2">
                {yamlOutput && (
                  <>
                    <button 
                      onClick={() => copyToClipboard(yamlOutput)}
                      className="text-xs px-2 py-1 rounded bg-primary hover:bg-primary/90 text-primary-foreground flex items-center gap-1"
                    >
                      <Copy size={14} />
                      <span>复制</span>
                    </button>
                    <button 
                      onClick={downloadYaml}
                      className="text-xs px-2 py-1 rounded bg-primary hover:bg-primary/90 text-primary-foreground flex items-center gap-1"
                    >
                      <Download size={14} />
                      <span>下载</span>
                    </button>
                  </>
                )}
              </div>
            </div>
            <div className="relative h-[500px]">
              <textarea
                value={yamlOutput}
                readOnly
                placeholder="YAML输出将显示在这里..."
                className="w-full h-full p-4 font-mono text-sm rounded-lg bg-accent border border-border focus:outline-none resize-none"
              ></textarea>
              {!yamlOutput && (
                <div className="absolute inset-0 flex items-center justify-center">
                  <div className="text-center text-muted-foreground p-4">
                    <ArrowRightLeft size={28} className="mx-auto mb-2 opacity-40" />
                    <p>转换后的YAML将显示在这里</p>
                    <p className="text-xs mt-1">点击下方按钮开始转换</p>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* 转换按钮 */}
        <div className="flex justify-center mb-8">
          <button
            onClick={handleConvert}
            disabled={loading || !jsonInput.trim()}
            className="h-12 px-8 rounded-lg bg-primary text-primary-foreground hover:bg-primary/90 flex items-center gap-3 disabled:opacity-70 disabled:cursor-not-allowed"
          >
            {loading ? (
              <>
                <span className="animate-spin h-5 w-5 border-2 border-white border-t-transparent rounded-full"></span>
                <span>转换中...</span>
              </>
            ) : (
              <>
                <ArrowRightLeft size={18} />
                <span>转换 JSON 到 YAML</span>
              </>
            )}
          </button>
        </div>

        {/* 信息说明 */}
        <div className="bg-card border border-border rounded-lg p-6">
          <h3 className="font-medium mb-4">关于JSON和YAML</h3>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h4 className="font-medium mb-2">JSON (JavaScript Object Notation)</h4>
              <ul className="list-disc list-inside mb-4 text-muted-foreground text-sm space-y-1">
                <li>基于JavaScript的轻量级数据交换格式</li>
                <li>使用键值对结构，格式紧凑</li>
                <li>常用于Web API和配置文件</li>
                <li>支持字符串、数字、布尔值、对象、数组、null</li>
                <li>必须使用双引号表示键和字符串值</li>
              </ul>
            </div>
            
            <div>
              <h4 className="font-medium mb-2">YAML (YAML Ain't Markup Language)</h4>
              <ul className="list-disc list-inside mb-4 text-muted-foreground text-sm space-y-1">
                <li>人类友好的数据序列化格式</li>
                <li>使用缩进表示层次结构，更易读</li>
                <li>常用于配置文件和文档</li>
                <li>支持与JSON相同的数据类型，还额外支持更复杂的数据类型</li>
                <li>通常不需要引号或大括号等语法符号</li>
                <li>支持注释、锚点引用和多行字符串</li>
              </ul>
            </div>
          </div>
          
          <div className="mt-4 text-sm text-muted-foreground">
            <p>转换提示：</p>
            <ul className="list-disc list-inside mt-2 space-y-1">
              <li>确保您的JSON格式有效，否则转换将失败</li>
              <li>复杂的嵌套结构在YAML中会使用适当的缩进表示</li>
              <li>YAML对空格和缩进敏感，请勿随意修改缩进</li>
              <li>YAML格式通常比JSON更易于人类阅读和编辑</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
}
