'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { ArrowLeft, Search, Download, Copy, RefreshCw, Globe, MapPin, Network } from 'lucide-react';
import { useLocale, useTranslations } from 'next-intl';

export default function IpLocationPage() {
  const locale = useLocale();
  const t = useTranslations('tools.ipLocation');
  const [ip, setIp] = useState('');
  const [loading, setLoading] = useState(false);
  const [result, setResult] = useState<any | null>(null);
  const [error, setError] = useState<string | null>(null);
  const router = useRouter();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!ip) {
      setError(t('errors.enterIpAddress'));
      return;
    }
    
    // IP地址格式验证 (支持IPv4和IPv6)
    const ipv4Regex = /^(\d{1,3})\.(\d{1,3})\.(\d{1,3})\.(\d{1,3})$/;
    const ipv6Regex = /^(([0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,7}:|([0-9a-fA-F]{1,4}:){1,6}:[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,5}(:[0-9a-fA-F]{1,4}){1,2}|([0-9a-fA-F]{1,4}:){1,4}(:[0-9a-fA-F]{1,4}){1,3}|([0-9a-fA-F]{1,4}:){1,3}(:[0-9a-fA-F]{1,4}){1,4}|([0-9a-fA-F]{1,4}:){1,2}(:[0-9a-fA-F]{1,4}){1,5}|[0-9a-fA-F]{1,4}:((:[0-9a-fA-F]{1,4}){1,6})|:((:[0-9a-fA-F]{1,4}){1,7}|:)|fe80:(:[0-9a-fA-F]{0,4}){0,4}%[0-9a-zA-Z]+|::(ffff(:0{1,4})?:)?((25[0-5]|(2[0-4]|1?[0-9])?[0-9])\.){3}(25[0-5]|(2[0-4]|1?[0-9])?[0-9])|([0-9a-fA-F]{1,4}:){1,4}:((25[0-5]|(2[0-4]|1?[0-9])?[0-9])\.){3}(25[0-5]|(2[0-4]|1?[0-9])?[0-9]))$/;
    
    if (!ipv4Regex.test(ip) && !ipv6Regex.test(ip)) {
      setError(t('errors.invalidIpAddress'));
      return;
    }
    
    // 如果是IPv4，进一步验证每个段的范围
    if (ipv4Regex.test(ip)) {
      const parts = ip.split('.').map(part => parseInt(part, 10));
      const isValidRange = parts.every(part => part >= 0 && part <= 255);
      if (!isValidRange) {
        setError(t('errors.invalidIpv4Range'));
        return;
      }
    }
    
    setLoading(true);
    setError(null);
    
    try {
      const response = await fetch(`/api/ip-location?ip=${encodeURIComponent(ip)}`);
      const data = await response.json();
      
      if (!response.ok) {
        throw new Error(data.error || t('errors.queryFailed'));
      }
      
      setResult(data);
    } catch (err) {
      setError(err instanceof Error ? err.message : t('errors.retryLater'));
    } finally {
      setLoading(false);
    }
  };

  const copyToClipboard = () => {
    if (result) {
      navigator.clipboard.writeText(JSON.stringify(result, null, 2));
      // 可以添加一个通知提示复制成功
    }
  };

  const downloadResult = () => {
    if (result) {
      // 创建要下载的JSON字符串
      const jsonString = JSON.stringify(result, null, 2);
      const blob = new Blob([jsonString], { type: 'application/json' });
      const url = URL.createObjectURL(blob);
      
      // 创建一个下载链接并触发点击
      const a = document.createElement('a');
      a.href = url;
      a.download = `ip-location-${result.ip}.json`;
      document.body.appendChild(a);
      a.click();
      
      // 清理
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
    }
  };

  const refreshQuery = () => {
    if (ip) {
      handleSubmit({ preventDefault: () => {} } as React.FormEvent);
    }
  };

  return (
    <div className="min-h-screen bg-background p-6">
      <div className="max-w-5xl mx-auto">
        {/* 返回按钮 */}
        <button 
          onClick={() => router.back()}
          className="mb-6 flex items-center gap-2 text-primary hover:underline"
        >
          <ArrowLeft size={16} />
          <span>{t('navigation.back')}</span>
        </button>

        {/* 工具标题 */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold">{t('title')}</h1>
          <p className="text-muted-foreground mt-2">
            {t('description')}
          </p>
        </div>

        {/* 查询表单 */}
        <div className="bg-card border border-border rounded-lg p-6 mb-6">
          <form onSubmit={handleSubmit} className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1">
              <label htmlFor="ip" className="block text-sm font-medium mb-2">
                {t('form.ipAddress')}
              </label>
              <div className="relative">
                <input
                  id="ip"
                  type="text"
                  placeholder={t('form.placeholder')}
                  value={ip}
                  onChange={(e) => setIp(e.target.value)}
                  className="w-full h-10 pl-4 pr-10 rounded-lg bg-accent border border-border focus:outline-none focus:ring-2 focus:ring-primary"
                />
              </div>
              {error && <p className="text-red-500 text-sm mt-1">{error}</p>}
            </div>
            <div className="flex items-end">
              <button
                type="submit"
                disabled={loading}
                className="h-10 px-6 rounded-lg bg-primary text-primary-foreground hover:bg-primary/90 flex items-center gap-2 disabled:opacity-70"
              >
                {loading ? (
                  <>
                    <span className="animate-spin h-4 w-4 border-2 border-white border-t-transparent rounded-full"></span>
                    <span>{t('form.searching')}</span>
                  </>
                ) : (
                  <>
                    <Search size={16} />
                    <span>{t('form.submit')}</span>
                  </>
                )}
              </button>
            </div>
          </form>
        </div>

        {/* 结果显示 */}
        {result && (
          <div className="bg-card border border-border rounded-lg mb-6">
            <div className="p-4 border-b border-border flex items-center justify-between">
              <h3 className="font-medium">{t('results.title')}</h3>
              <div className="flex items-center gap-2">
                <button 
                  onClick={refreshQuery}
                  className="p-2 rounded hover:bg-accent"
                  title={t('results.actions.refresh')}
                >
                  <RefreshCw size={16} />
                </button>
                <button 
                  onClick={copyToClipboard}
                  className="p-2 rounded hover:bg-accent"
                  title={t('results.actions.copy')}
                >
                  <Copy size={16} />
                </button>
                <button 
                  onClick={downloadResult}
                  className="p-2 rounded hover:bg-accent"
                  title={t('results.actions.download')}
                >
                  <Download size={16} />
                </button>
              </div>
            </div>
            
            <div className="p-6 flex flex-col md:flex-row gap-6">
              {/* 地理位置 - 左侧显示 */}
              <div className="space-y-6 md:w-1/2">
                {/* 全球定位信息 */}
                <div className="space-y-4">
                  <div className="flex items-center gap-2">
                    <MapPin size={16} className="text-muted-foreground" />
                    <span className="font-medium">{t('results.geoLocation.title')}</span>
                  </div>
                  <div className="space-y-2">
                    {result.country && (
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">{t('results.geoLocation.country')}</span>
                        <span>{result.country.name}</span>
                      </div>
                    )}
                    {result.registered_country && result.registered_country.code !== result.country?.code && (
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">{t('results.geoLocation.registeredCountry')}</span>
                        <span>{result.registered_country.name}</span>
                      </div>
                    )}
                    {result.regions && result.regions.length > 0 && (
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">{t('results.geoLocation.region')}</span>
                        <span>{result.regions.join(' ')}</span>
                      </div>
                    )}
                    {result.location && (
                      <>
                        <div className="flex justify-between">
                          <span className="text-muted-foreground">{t('results.geoLocation.latitude')}</span>
                          <span>{result.location.latitude}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-muted-foreground">{t('results.geoLocation.longitude')}</span>
                          <span>{result.location.longitude}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-muted-foreground">{t('results.geoLocation.timezone')}</span>
                          <span>{result.location.timezone}</span>
                        </div>
                      </>
                    )}
                  </div>
                </div>
                
                {/* 中国IP查询结果 */}
                {result.country?.name === '中国' && result.chinaIpInfo && (
                  <div className="space-y-4 mt-4 pt-4 border-t border-border">
                    <div className="flex items-center gap-2">
                      <MapPin size={16} className="text-muted-foreground" />
                      <span className="font-medium">{t('results.chinaIpInfo.title')}</span>
                    </div>
                    <div className="space-y-2">
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">{t('results.chinaIpInfo.isp')}</span>
                        <span>{result.chinaIpInfo.isp}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">{t('results.chinaIpInfo.networkType')}</span>
                        <span>{result.chinaIpInfo.net}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">{t('results.chinaIpInfo.province')}</span>
                        <span>{result.chinaIpInfo.province}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">{t('results.chinaIpInfo.city')}</span>
                        <span>{result.chinaIpInfo.city}</span>
                      </div>
                      {result.chinaIpInfo.districts && (
                        <div className="flex justify-between">
                          <span className="text-muted-foreground">{t('results.chinaIpInfo.district')}</span>
                          <span>{result.chinaIpInfo.districts}</span>
                        </div>
                      )}
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">{t('results.chinaIpInfo.areaCode')}</span>
                        <span>
                          {result.chinaIpInfo.provinceCode}-
                          {result.chinaIpInfo.cityCode}-
                          {result.chinaIpInfo.districtsCode}
                        </span>
                      </div>
                    </div>
                  </div>
                )}
              </div>

              {/* 右侧垂直排列的内容 */}
              <div className="space-y-6 md:w-1/2">
                {/* 结果概览 */}
                <div className="space-y-4">
                  <div className="flex items-center gap-2">
                    <Globe size={16} className="text-muted-foreground" />
                    <span className="font-medium">{t('results.basicInfo.title')}</span>
                  </div>
                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">{t('results.basicInfo.ipAddress')}</span>
                      <span>{result.ip}</span>
                    </div>
                    {result.addr && (
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">{t('results.basicInfo.subnet')}</span>
                        <span>{result.addr}</span>
                      </div>
                    )}
                    {result.type && (
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">{t('results.basicInfo.networkType')}</span>
                        <span>{result.type}</span>
                      </div>
                    )}
                  </div>
                </div>

                {/* ISP信息 */}
                {result.as && (
                  <div className="space-y-4">
                    <div className="flex items-center gap-2">
                      <Network size={16} className="text-muted-foreground" />
                      <span className="font-medium">{t('results.ispInfo.title')}</span>
                    </div>
                    <div className="space-y-2">
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">{t('results.ispInfo.provider')}</span>
                        <span>{result.as.info}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">{t('results.ispInfo.asn')}</span>
                        <span>{result.as.number}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">{t('results.ispInfo.organization')}</span>
                        <span>{result.as.name}</span>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </div>
            
            <div className="p-4 text-sm text-muted-foreground bg-accent/30 rounded-b-lg">
              {t('results.disclaimer')}
            </div>
          </div>
        )}

        {/* 使用说明 */}
        <div className="mt-8">
          <h2 className="text-xl font-semibold mb-4">{t('instructions.title')}</h2>
          <div className="bg-card border border-border rounded-lg p-6">
            <ol className="list-decimal list-inside space-y-2">
              {Array.isArray(t.raw('instructions.steps')) && 
                t.raw('instructions.steps').map((step: string, index: number) => (
                  <li key={index}>{step}</li>
                ))
              }
            </ol>
            <div className="mt-4 p-4 bg-accent/50 rounded-lg">
              <p className="text-sm text-muted-foreground">
                <strong>{locale === 'zh' ? '提示：' : 'Tip:'}</strong> {t('instructions.tip')}
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
} 