'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { ArrowLeft, Download, Copy, RefreshCw, ArrowRightLeft, AlertTriangle, FileText } from 'lucide-react';

export default function CsvToJsonPage() {
  const [csvInput, setCsvInput] = useState('');
  const [jsonOutput, setJsonOutput] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [firstRowAsHeader, setFirstRowAsHeader] = useState(true);
  const [delimiter, setDelimiter] = useState(',');
  const router = useRouter();

  // CSV解析函数
  const parseCsvToArray = (csvText: string, delimiter: string) => {
    // 分割行
    const rows = csvText.split(/\r?\n/).filter(row => row.trim() !== '');
    
    if (rows.length === 0) {
      return [];
    }

    // 解析每行为值数组
    const parsedRows = rows.map(row => {
      const values: string[] = [];
      let currentValue = '';
      let insideQuotes = false;
      
      for (let i = 0; i < row.length; i++) {
        const char = row[i];
        const nextChar = i < row.length - 1 ? row[i + 1] : null;
        
        if (char === '"' && !insideQuotes) {
          // 开始引号
          insideQuotes = true;
        } else if (char === '"' && insideQuotes) {
          // 引号内的引号
          if (nextChar === '"') {
            // 双引号转义
            currentValue += '"';
            i++; // 跳过下一个引号
          } else {
            // 结束引号
            insideQuotes = false;
          }
        } else if (char === delimiter && !insideQuotes) {
          // 分隔符
          values.push(currentValue);
          currentValue = '';
        } else {
          // 普通字符
          currentValue += char;
        }
      }
      
      // 添加最后一个值
      values.push(currentValue);
      
      return values;
    });
    
    return parsedRows;
  };

  // 将CSV转换为JSON
  const convertCsvToJson = (csvData: string, firstRowAsHeader: boolean, delimiter: string) => {
    try {
      const parsedRows = parseCsvToArray(csvData, delimiter);
      
      if (parsedRows.length === 0) {
        return [];
      }
      
      // 如果第一行作为标题
      if (firstRowAsHeader) {
        const headers = parsedRows[0];
        const jsonArray = [];
        
        // 从第二行开始创建对象
        for (let i = 1; i < parsedRows.length; i++) {
          const row = parsedRows[i];
          const obj: Record<string, string> = {};
          
          // 确保行数据不为空
          if (row.length > 0) {
            // 用标题和值创建对象
            for (let j = 0; j < headers.length; j++) {
              const header = headers[j].trim();
              // 跳过空标题
              if (header) {
                obj[header] = j < row.length ? row[j] : '';
              }
            }
            jsonArray.push(obj);
          }
        }
        
        return jsonArray;
      } else {
        // 如果没有标题行，则创建带索引的数组对象
        return parsedRows.map(row => {
          const obj: Record<string, string> = {};
          row.forEach((value, index) => {
            obj[`field${index + 1}`] = value;
          });
          return obj;
        });
      }
    } catch (error) {
      throw new Error(`CSV解析错误: ${error instanceof Error ? error.message : '未知错误'}`);
    }
  };

  // 转换处理函数
  const handleConvert = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!csvInput.trim()) {
      setError('请输入CSV数据');
      return;
    }
    
    setLoading(true);
    setError(null);
    
    try {
      // 转换为JSON
      const jsonData = convertCsvToJson(csvInput, firstRowAsHeader, delimiter);
      
      // 格式化JSON输出
      const formattedJson = JSON.stringify(jsonData, null, 2);
      
      setJsonOutput(formattedJson);
    } catch (err) {
      setError(err instanceof Error ? err.message : '无效的CSV格式');
    } finally {
      setLoading(false);
    }
  };

  // 示例CSV数据
  const loadExample = () => {
    const exampleCsv = `id,name,email,age,department,isActive
1,张三,<EMAIL>,28,研发部,true
2,李四,<EMAIL>,32,市场部,true
3,王五,<EMAIL>,25,设计部,false
4,赵六,<EMAIL>,35,人力资源,true
5,钱七,<EMAIL>,29,财务部,true`;
    
    setCsvInput(exampleCsv);
    setError(null);
  };

  // 复制到剪贴板
  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
  };

  // 下载JSON结果
  const downloadJson = () => {
    if (jsonOutput) {
      const blob = new Blob([jsonOutput], { type: 'application/json' });
      const url = URL.createObjectURL(blob);
      
      const a = document.createElement('a');
      a.href = url;
      a.download = 'converted.json';
      document.body.appendChild(a);
      a.click();
      
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
    }
  };

  // 清空输入
  const clearInput = () => {
    setCsvInput('');
    setJsonOutput('');
    setError(null);
  };

  return (
    <div className="min-h-screen bg-background p-6">
      <div className="max-w-6xl mx-auto">
        {/* 返回按钮 */}
        <button 
          onClick={() => router.back()}
          className="mb-6 flex items-center gap-2 text-primary hover:underline"
        >
          <ArrowLeft size={16} />
          <span>返回工具列表</span>
        </button>

        {/* 工具标题 */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold">CSV转JSON</h1>
          <p className="text-muted-foreground mt-2">
            将CSV格式数据转换为JSON格式，便于程序处理和数据交换
          </p>
        </div>

        {/* 选项设置 */}
        <div className="mb-6">
          <h2 className="font-medium mb-3">转换选项</h2>
          <div className="flex flex-wrap items-center gap-6">
            <div className="flex items-center gap-2">
              <input
                type="checkbox"
                id="firstRowAsHeader"
                checked={firstRowAsHeader}
                onChange={(e) => setFirstRowAsHeader(e.target.checked)}
                className="h-4 w-4 rounded border-border text-primary focus:ring-primary"
              />
              <label htmlFor="firstRowAsHeader" className="text-sm text-muted-foreground">
                第一行作为标题
              </label>
            </div>
            
            <div>
              <label className="text-sm text-muted-foreground block mb-1">分隔符</label>
              <select 
                value={delimiter}
                onChange={(e) => setDelimiter(e.target.value)}
                className="h-9 px-3 rounded bg-accent border border-border focus:outline-none focus:ring-2 focus:ring-primary"
              >
                <option value=",">逗号 (,)</option>
                <option value=";">分号 (;)</option>
                <option value="\t">制表符 (Tab)</option>
                <option value="|">竖线 (|)</option>
              </select>
            </div>
          </div>
        </div>

        {/* 转换区域 */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
          {/* CSV输入区 */}
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <h2 className="font-medium">CSV输入</h2>
              <div className="flex items-center gap-2">
                <button 
                  onClick={clearInput}
                  className="text-xs px-2 py-1 rounded bg-accent hover:bg-accent/80 text-accent-foreground"
                >
                  清空
                </button>
                <button 
                  onClick={loadExample}
                  className="text-xs px-2 py-1 rounded bg-accent hover:bg-accent/80 text-accent-foreground"
                >
                  加载示例
                </button>
                <button 
                  onClick={() => copyToClipboard(csvInput)}
                  className="p-1 rounded hover:bg-accent"
                  title="复制"
                >
                  <Copy size={14} />
                </button>
              </div>
            </div>
            <textarea
              value={csvInput}
              onChange={(e) => setCsvInput(e.target.value)}
              placeholder="在此粘贴CSV数据..."
              className="w-full h-[500px] p-4 font-mono text-sm rounded-lg bg-accent border border-border focus:outline-none focus:ring-2 focus:ring-primary resize-none"
            ></textarea>
            {error && (
              <div className="flex items-center gap-2 text-red-500 text-sm">
                <AlertTriangle size={14} />
                <span>{error}</span>
              </div>
            )}
          </div>

          {/* JSON输出区 */}
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <h2 className="font-medium">JSON输出</h2>
              <div className="flex items-center gap-2">
                {jsonOutput && (
                  <>
                    <button 
                      onClick={() => copyToClipboard(jsonOutput)}
                      className="text-xs px-2 py-1 rounded bg-primary hover:bg-primary/90 text-primary-foreground flex items-center gap-1"
                    >
                      <Copy size={14} />
                      <span>复制</span>
                    </button>
                    <button 
                      onClick={downloadJson}
                      className="text-xs px-2 py-1 rounded bg-primary hover:bg-primary/90 text-primary-foreground flex items-center gap-1"
                    >
                      <Download size={14} />
                      <span>下载JSON</span>
                    </button>
                  </>
                )}
              </div>
            </div>
            <div className="relative h-[500px]">
              <textarea
                value={jsonOutput}
                readOnly
                placeholder="JSON输出将显示在这里..."
                className="w-full h-full p-4 font-mono text-sm rounded-lg bg-accent border border-border focus:outline-none resize-none"
              ></textarea>
              {!jsonOutput && (
                <div className="absolute inset-0 flex items-center justify-center">
                  <div className="text-center text-muted-foreground p-4">
                    <FileText size={28} className="mx-auto mb-2 opacity-40" />
                    <p>转换后的JSON将显示在这里</p>
                    <p className="text-xs mt-1">点击下方按钮开始转换</p>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* 转换按钮 */}
        <div className="flex justify-center mb-8">
          <button
            onClick={handleConvert}
            disabled={loading || !csvInput.trim()}
            className="h-12 px-8 rounded-lg bg-primary text-primary-foreground hover:bg-primary/90 flex items-center gap-3 disabled:opacity-70 disabled:cursor-not-allowed"
          >
            {loading ? (
              <>
                <span className="animate-spin h-5 w-5 border-2 border-white border-t-transparent rounded-full"></span>
                <span>转换中...</span>
              </>
            ) : (
              <>
                <ArrowRightLeft size={18} />
                <span>转换 CSV 到 JSON</span>
              </>
            )}
          </button>
        </div>

        {/* 信息说明 */}
        <div className="bg-card border border-border rounded-lg p-6">
          <h3 className="font-medium mb-4">关于CSV和JSON</h3>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h4 className="font-medium mb-2">CSV (Comma-Separated Values)</h4>
              <ul className="list-disc list-inside mb-4 text-muted-foreground text-sm space-y-1">
                <li>简单的表格数据格式，用分隔符分隔值</li>
                <li>可以直接在Excel、Google Sheets等电子表格软件中打开</li>
                <li>每行代表一条记录，每列代表一个字段</li>
                <li>不支持嵌套数据结构，只有扁平结构</li>
                <li>常用于数据交换和导入/导出功能</li>
              </ul>
            </div>
            
            <div>
              <h4 className="font-medium mb-2">JSON (JavaScript Object Notation)</h4>
              <ul className="list-disc list-inside mb-4 text-muted-foreground text-sm space-y-1">
                <li>基于JavaScript的轻量级数据交换格式</li>
                <li>使用键值对结构，支持嵌套对象和数组</li>
                <li>常用于Web API和配置文件</li>
                <li>支持复杂的数据结构和数据类型</li>
                <li>易于程序解析和处理</li>
              </ul>
            </div>
          </div>
          
          <div className="mt-4 text-sm text-muted-foreground">
            <p>转换提示：</p>
            <ul className="list-disc list-inside mt-2 space-y-1">
              <li>CSV的第一行通常包含列标题，可以选择是否使用它们作为JSON对象的键</li>
              <li>如果未将第一行作为标题，将使用自动生成的字段名称(field1, field2, ...)</li>
              <li>CSV值会被转换为字符串，可能需要在转换后手动调整数据类型</li>
              <li>本工具可以处理带引号的CSV值，包括包含逗号和换行符的值</li>
              <li>支持多种分隔符，可根据CSV格式选择合适的分隔符</li>
              <li>转换后的JSON可以直接复制或下载为文件</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
}
