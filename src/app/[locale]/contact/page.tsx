'use client';

import { useState } from 'react';
import { ArrowLeft, Mail, MessagesSquare, Phone, MapPin } from 'lucide-react';
import { useRouter, usePathname } from 'next/navigation';

// 定义表单状态类型
type FormStatus = 'idle' | 'submitting' | 'success' | 'error';

export default function ContactPage() {
  const router = useRouter();
  const pathname = usePathname();
  const locale = pathname.split('/')[1]; // 获取当前locale
  
  // 表单数据状态
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: '',
    subject: '',
    message: ''
  });
  
  // 表单状态
  const [formStatus, setFormStatus] = useState<FormStatus>('idle');
  
  // 验证错误
  const [errors, setErrors] = useState<Record<string, string>>({});
  
  // 处理输入变化
  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData({
      ...formData,
      [name]: value
    });
    
    // 清除该字段的错误
    if (errors[name]) {
      setErrors({
        ...errors,
        [name]: ''
      });
    }
  };
  
  // 验证表单
  const validateForm = () => {
    const newErrors: Record<string, string> = {};
    
    // 验证必填字段
    if (!formData.name.trim()) {
      newErrors.name = '请输入您的姓名';
    }
    
    if (!formData.email.trim()) {
      newErrors.email = '请输入您的电子邮箱';
    } else if (!/^\S+@\S+\.\S+$/.test(formData.email)) {
      newErrors.email = '请输入有效的电子邮箱地址';
    }
    
    if (!formData.subject) {
      newErrors.subject = '请选择咨询主题';
    }
    
    if (!formData.message.trim()) {
      newErrors.message = '请输入您的留言内容';
    } else if (formData.message.length < 10) {
      newErrors.message = '留言内容至少需要10个字符';
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };
  
  // 处理表单提交
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    // 表单验证
    if (!validateForm()) {
      return;
    }
    
    setFormStatus('submitting');
    
    try {
      // 模拟API请求
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      // 成功状态
      setFormStatus('success');
      
      // 重置表单
      setFormData({
        name: '',
        email: '',
        phone: '',
        subject: '',
        message: ''
      });
      
      // 3秒后重置状态
      setTimeout(() => {
        setFormStatus('idle');
      }, 3000);
    } catch (error) {
      console.error('提交表单时出错:', error);
      setFormStatus('error');
      
      // 3秒后重置错误状态
      setTimeout(() => {
        setFormStatus('idle');
      }, 3000);
    }
  };
  
  return (
    <div className="min-h-screen bg-background p-6">
      <div className="max-w-5xl mx-auto">
        {/* 导航 */}
        <nav aria-label="返回导航">
          <button 
            onClick={() => router.push(`/${locale}`)}
            className="mb-6 flex items-center gap-2 text-primary hover:underline"
            aria-label="返回首页"
          >
            <ArrowLeft size={16} />
            <span>返回首页</span>
          </button>
        </nav>
        
        {/* 页面标题 */}
        <header className="mb-10 text-center">
          <h1 className="text-3xl font-bold">联系我们</h1>
          <p className="text-muted-foreground mt-2">
            我们很乐意听取您的意见和建议，请通过以下方式联系我们
          </p>
        </header>
        
        {/* 联系信息与表单 */}
        <div className="grid md:grid-cols-2 gap-8">
          {/* 联系信息 */}
          <div className="space-y-8">
            <div className="bg-card border border-border rounded-lg p-6">
              <h2 className="text-xl font-semibold mb-6">联系方式</h2>
              
              {/* 电子邮件 */}
              <div className="flex items-start gap-4 mb-6">
                <div className="bg-primary/10 p-3 rounded-full">
                  <Mail size={20} className="text-primary" />
                </div>
                <div>
                  <h3 className="font-medium">电子邮件</h3>
                  <p className="text-muted-foreground"><EMAIL></p>
                  <p className="text-sm text-muted-foreground mt-1">我们会在1-2个工作日内回复您</p>
                </div>
              </div>
              
              {/* 在线支持 */}
              <div className="flex items-start gap-4 mb-6">
                <div className="bg-primary/10 p-3 rounded-full">
                  <MessagesSquare size={20} className="text-primary" />
                </div>
                <div>
                  <h3 className="font-medium">在线客服</h3>
                  <p className="text-muted-foreground">周一至周五 9:00-18:00</p>
                  <p className="text-sm text-muted-foreground mt-1">节假日休息</p>
                </div>
              </div>
              
              {/* 电话支持 */}
              <div className="flex items-start gap-4 mb-6">
                <div className="bg-primary/10 p-3 rounded-full">
                  <Phone size={20} className="text-primary" />
                </div>
                <div>
                  <h3 className="font-medium">电话支持</h3>
                  <p className="text-muted-foreground">+86 10 1234 5678</p>
                  <p className="text-sm text-muted-foreground mt-1">周一至周五 9:00-17:00</p>
                </div>
              </div>
              
              {/* 公司地址 */}
              <div className="flex items-start gap-4">
                <div className="bg-primary/10 p-3 rounded-full">
                  <MapPin size={20} className="text-primary" />
                </div>
                <div>
                  <h3 className="font-medium">办公地址</h3>
                  <p className="text-muted-foreground">北京市海淀区中关村科技园</p>
                  <p className="text-sm text-muted-foreground mt-1">邮编: 100080</p>
                </div>
              </div>
            </div>
            
            {/* 常见问题链接 */}
            <div className="bg-card border border-border rounded-lg p-6">
              <h2 className="text-xl font-semibold mb-4">常见问题</h2>
              <p className="text-muted-foreground mb-4">
                在联系我们之前，您可以查阅我们的帮助中心，那里可能已经有您问题的答案。
              </p>
              <button
                onClick={() => router.push(`/${locale}/help`)}
                className="text-primary hover:underline"
              >
                浏览帮助中心 →
              </button>
            </div>
          </div>
          
          {/* 联系表单 */}
          <div className="bg-card border border-border rounded-lg p-6">
            <h2 className="text-xl font-semibold mb-6">发送消息</h2>
            
            {formStatus === 'success' ? (
              <div className="bg-green-50 border border-green-200 rounded-lg p-4 text-green-700">
                <h3 className="font-medium">消息已发送!</h3>
                <p>感谢您的留言，我们会尽快回复您。</p>
              </div>
            ) : formStatus === 'error' ? (
              <div className="bg-red-50 border border-red-200 rounded-lg p-4 text-red-700">
                <h3 className="font-medium">发送失败</h3>
                <p>抱歉，消息发送失败，请稍后再试或通过其他方式联系我们。</p>
              </div>
            ) : (
              <form onSubmit={handleSubmit} className="space-y-4">
                {/* 姓名 */}
                <div>
                  <label htmlFor="name" className="block mb-2 text-sm font-medium">
                    姓名 <span className="text-red-500">*</span>
                  </label>
                  <input
                    type="text"
                    id="name"
                    name="name"
                    value={formData.name}
                    onChange={handleChange}
                    className={`w-full px-4 py-2 rounded-lg bg-background border ${
                      errors.name ? 'border-red-500' : 'border-border'
                    } focus:outline-none focus:ring-2 focus:ring-primary`}
                  />
                  {errors.name && <p className="mt-1 text-sm text-red-500">{errors.name}</p>}
                </div>
                
                {/* 电子邮箱 */}
                <div>
                  <label htmlFor="email" className="block mb-2 text-sm font-medium">
                    电子邮箱 <span className="text-red-500">*</span>
                  </label>
                  <input
                    type="email"
                    id="email"
                    name="email"
                    value={formData.email}
                    onChange={handleChange}
                    className={`w-full px-4 py-2 rounded-lg bg-background border ${
                      errors.email ? 'border-red-500' : 'border-border'
                    } focus:outline-none focus:ring-2 focus:ring-primary`}
                  />
                  {errors.email && <p className="mt-1 text-sm text-red-500">{errors.email}</p>}
                </div>
                
                {/* 电话 */}
                <div>
                  <label htmlFor="phone" className="block mb-2 text-sm font-medium">
                    电话 (选填)
                  </label>
                  <input
                    type="tel"
                    id="phone"
                    name="phone"
                    value={formData.phone}
                    onChange={handleChange}
                    className="w-full px-4 py-2 rounded-lg bg-background border border-border focus:outline-none focus:ring-2 focus:ring-primary"
                  />
                </div>
                
                {/* 主题 */}
                <div>
                  <label htmlFor="subject" className="block mb-2 text-sm font-medium">
                    咨询主题 <span className="text-red-500">*</span>
                  </label>
                  <select
                    id="subject"
                    name="subject"
                    value={formData.subject}
                    onChange={handleChange}
                    className={`w-full px-4 py-2 rounded-lg bg-background border ${
                      errors.subject ? 'border-red-500' : 'border-border'
                    } focus:outline-none focus:ring-2 focus:ring-primary`}
                  >
                    <option value="">请选择咨询主题</option>
                    <option value="技术支持">技术支持</option>
                    <option value="功能建议">功能建议</option>
                    <option value="问题反馈">问题反馈</option>
                    <option value="商务合作">商务合作</option>
                    <option value="其他问题">其他问题</option>
                  </select>
                  {errors.subject && <p className="mt-1 text-sm text-red-500">{errors.subject}</p>}
                </div>
                
                {/* 留言内容 */}
                <div>
                  <label htmlFor="message" className="block mb-2 text-sm font-medium">
                    留言内容 <span className="text-red-500">*</span>
                  </label>
                  <textarea
                    id="message"
                    name="message"
                    rows={5}
                    value={formData.message}
                    onChange={handleChange}
                    className={`w-full px-4 py-2 rounded-lg bg-background border ${
                      errors.message ? 'border-red-500' : 'border-border'
                    } focus:outline-none focus:ring-2 focus:ring-primary resize-none`}
                  ></textarea>
                  {errors.message && <p className="mt-1 text-sm text-red-500">{errors.message}</p>}
                </div>
                
                {/* 提交按钮 */}
                <button
                  type="submit"
                  disabled={formStatus === 'submitting'}
                  className="w-full px-6 py-3 bg-primary text-primary-foreground rounded-lg hover:bg-primary/90 disabled:opacity-70 disabled:cursor-not-allowed"
                >
                  {formStatus === 'submitting' ? '发送中...' : '发送消息'}
                </button>
              </form>
            )}
          </div>
        </div>
      </div>
    </div>
  );
} 