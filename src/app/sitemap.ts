import { MetadataRoute } from 'next';
import { routing } from '@/i18n/routing';

// 定义工具类型
type ToolInfo = {
  slug: string;
  lastModified: string;
};

// 获取所有工具路径
function getAllToolSlugs(): ToolInfo[] {
  return [
    { slug: 'ip-location', lastModified: new Date().toISOString() },
    { slug: 'img-watermark', lastModified: new Date().toISOString() },
    { slug: 'des-encryption', lastModified: new Date().toISOString() },
    { slug: 'aes-encryption', lastModified: new Date().toISOString() },
    { slug: 'md5-collision', lastModified: new Date().toISOString() },
    { slug: 'sha-calculator', lastModified: new Date().toISOString() },
    { slug: 'csv-to-json', lastModified: new Date().toISOString() },
    { slug: 'json-to-csv', lastModified: new Date().toISOString() },
    { slug: 'yaml-to-properties', lastModified: new Date().toISOString() },
    { slug: 'yaml-formatter', lastModified: new Date().toISOString() },
    { slug: 'yaml-to-json', lastModified: new Date().toISOString() },
    { slug: 'json-to-yaml', lastModified: new Date().toISOString() },
    { slug: 'jwt-decoder', lastModified: new Date().toISOString() },
    { slug: 'url-encode', lastModified: new Date().toISOString() },
    { slug: 'qrcode', lastModified: new Date().toISOString() },
    { slug: 'random-ip', lastModified: new Date().toISOString() },
    { slug: 'base64-encode', lastModified: new Date().toISOString() },
    { slug: 'case-converter', lastModified: new Date().toISOString() },
    { slug: 'color-converter', lastModified: new Date().toISOString() },
    { slug: 'text-diff', lastModified: new Date().toISOString() },
    { slug: 'word-count', lastModified: new Date().toISOString() },
    { slug: 'random-generator', lastModified: new Date().toISOString() },
    { slug: 'regex-test', lastModified: new Date().toISOString() },
    { slug: 'img-compress', lastModified: new Date().toISOString() },
    { slug: 'json-formatter', lastModified: new Date().toISOString() },
    { slug: 'code-compress', lastModified: new Date().toISOString() },
    { slug: 'domain-info', lastModified: new Date().toISOString() },
    { slug: 'dns-lookup', lastModified: new Date().toISOString() },
    { slug: 'whois', lastModified: new Date().toISOString() },
    { slug: 'color-extract', lastModified: new Date().toISOString() },
  ];
}

// 生成站点地图
export default function sitemap(): MetadataRoute.Sitemap {
  const baseUrl = 'https://bili-tool.com';
  const currentDate = new Date().toISOString();
  
  // 获取所有工具页面
  const tools = getAllToolSlugs();
  
  // 定义基本页面列表
  const basePages = [
    'about',
    'blog',
    'help',
    'contact',
    'privacy',
    'terms',
  ];
  
  // 生成所有语言版本的基础页面URL
  const localeBasePages = routing.locales.flatMap(locale => 
    basePages.map(page => ({
      url: `${baseUrl}/${locale}/${page}`,
      lastModified: currentDate,
      changeFrequency: 'monthly' as const,
      priority: 0.7,
    }))
  );
  
  // 生成所有语言版本的工具页面URL
  const localeToolPages = routing.locales.flatMap(locale => 
    tools.map(tool => ({
      url: `${baseUrl}/${locale}/tools/${tool.slug}`,
      lastModified: tool.lastModified,
      changeFrequency: 'weekly' as const,
      priority: 0.8,
    }))
  );
  
  // 生成所有语言版本的工具列表页URL
  const localeToolsIndexPages = routing.locales.map(locale => ({
    url: `${baseUrl}/${locale}/tools`,
    lastModified: currentDate,
    changeFrequency: 'daily' as const,
    priority: 0.9,
  }));
  
  // 生成所有语言版本的主页URL
  const localeHomePages = routing.locales.map(locale => ({
    url: `${baseUrl}/${locale}`,
    lastModified: currentDate,
    changeFrequency: 'daily' as const,
    priority: 1.0,
  }));
  
  // 合并所有URL
  return [
    // 主页（各语言版本）
    ...localeHomePages,
    // 工具索引页（各语言版本）
    ...localeToolsIndexPages,
    // 工具详情页（各语言版本）
    ...localeToolPages,
    // 基础页面（各语言版本）
    ...localeBasePages,
  ];
} 