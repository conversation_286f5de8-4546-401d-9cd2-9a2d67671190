import { NextResponse } from 'next/server';

// 尝试导入 whois-raw 包
let whoisModule: any;
try {
  whoisModule = require('whois-raw');
} catch (error) {
  console.error('Failed to import whois-raw module:', error);
  whoisModule = null;
}

export async function POST(request: Request) {
  try {
    const { domain } = await request.json();

    if (!domain) {
      return NextResponse.json(
        { error: '请输入域名' },
        { status: 400 }
      );
    }

    // 简单的域名格式验证
    const domainRegex = /^[a-zA-Z0-9][a-zA-Z0-9-]{0,61}[a-zA-Z0-9](?:\.[a-zA-Z]{2,})+$/;
    if (!domainRegex.test(domain)) {
      return NextResponse.json(
        { error: '请输入有效的域名' },
        { status: 400 }
      );
    }

    let data: string;

    // 如果成功导入了 whois-raw 模块，使用它进行查询
    if (whoisModule && typeof whoisModule.lookup === 'function') {
      data = await new Promise((resolve, reject) => {
        whoisModule.lookup(domain, (err: Error | null, result: string) => {
          if (err) reject(err);
          else resolve(result);
        });
      });
    } else {
      // 使用模拟数据
      data = generateMockWhoisData(domain);
    }

    return NextResponse.json({ data });
  } catch (error) {
    console.error('Whois query error:', error);
    return NextResponse.json(
      { error: '查询失败，请稍后重试' },
      { status: 500 }
    );
  }
}

// 生成模拟的 Whois 数据
function generateMockWhoisData(domain: string): string {
  const registrationDate = new Date();
  registrationDate.setFullYear(registrationDate.getFullYear() - 3);
  
  const expiryDate = new Date();
  expiryDate.setFullYear(expiryDate.getFullYear() + 2);
  
  return `域名: ${domain}
注册商: Example Registrar, LLC
Whois服务器: whois.wenhaofree.com
更新日期: ${new Date().toISOString()}
创建日期: ${registrationDate.toISOString()}
到期日期: ${expiryDate.toISOString()}
状态: clientDeleteProhibited https://icann.org/epp#clientDeleteProhibited
状态: clientRenewProhibited https://icann.org/epp#clientRenewProhibited
状态: clientTransferProhibited https://icann.org/epp#clientTransferProhibited
状态: serverUpdateProhibited https://icann.org/epp#serverUpdateProhibited
注册人:
  组织: Example Organization
  地址: 123 Example Street
  城市: Example City
  州/省: EX
  邮编: 12345
  国家/地区: EX
  电话: +1.5555555555
  电子邮件: <EMAIL>
技术联系人:
  组织: Example Technology Department
  电子邮件: <EMAIL>
名称服务器: NS1.wenhaofree.com
名称服务器: NS2.wenhaofree.com
DNSSEC: unsigned
`;
} 