import { NextResponse } from 'next/server';
import { Reader, City, Asn } from '@maxmind/geoip2-node';
import * as fs from 'fs';
import path from 'path';

// 初始化GeoIP2 readers
const cityDbPath = path.join(process.cwd(), 'public/db/GeoLite2-City.mmdb');
const asnDbPath = path.join(process.cwd(), 'public/db/GeoLite2-ASN.mmdb');
const cnDbPath = path.join(process.cwd(), 'public/db/GeoCN.mmdb');

// 读取文件内容
const cityDbBuffer = fs.readFileSync(cityDbPath);
const asnDbBuffer = fs.readFileSync(asnDbPath);
const cnDbBuffer = fs.readFileSync(cnDbPath);

// 创建Reader实例
const cityReader = Reader.openBuffer(cityDbBuffer);
const asnReader = Reader.openBuffer(asnDbBuffer);

// 使用nodejs-maxmind库直接读取GeoCN数据库
const maxmind = require('maxmind');
let cnReader: any = null;
try {
  cnReader = new maxmind.Reader(cnDbBuffer);
} catch (error) {
  console.error('初始化GeoCN数据库失败:', error);
}

// ISP映射表
const asnMap: { [key: number]: string } = {
  9812: "东方有线", 9389: "中国长城", 17962: "天威视讯", 17429: "歌华有线",
  7497: "科技网", 24139: "华数", 9801: "中关村", 4538: "教育网", 24151: "CNNIC",
  
  // 中国移动
  38019: "中国移动", 139080: "中国移动", 9808: "中国移动", 24400: "中国移动",
  134810: "中国移动", 24547: "中国移动", 56040: "中国移动", 56041: "中国移动",
  56042: "中国移动", 56044: "中国移动", 132525: "中国移动", 56046: "中国移动",
  56047: "中国移动", 56048: "中国移动", 59257: "中国移动", 24444: "中国移动",
  24445: "中国移动", 137872: "中国移动", 9231: "中国移动", 58453: "中国移动",
  
  // 中国电信
  4134: "中国电信", 4812: "中国电信", 23724: "中国电信", 136188: "中国电信",
  137693: "中国电信", 17638: "中国电信", 140553: "中国电信", 4847: "中国电信",
  140061: "中国电信", 136195: "中国电信", 17799: "中国电信", 139018: "中国电信",
  133776: "中国电信", 58772: "中国电信", 146966: "中国电信", 63527: "中国电信",
  58539: "中国电信", 58540: "中国电信", 141998: "中国电信", 138169: "中国电信",
  139203: "中国电信", 58563: "中国电信", 137690: "中国电信", 63838: "中国电信",
  137694: "中国电信", 137698: "中国电信", 136167: "中国电信", 148969: "中国电信",
  134764: "中国电信", 134770: "中国电信", 148981: "中国电信", 134774: "中国电信",
  136190: "中国电信", 140647: "中国电信", 132225: "中国电信", 140485: "中国电信",
  4811: "中国电信", 131285: "中国电信", 137689: "中国电信", 137692: "中国电信",
  140636: "中国电信", 140638: "中国电信", 140345: "中国电信", 38283: "中国电信",
  140292: "中国电信", 140903: "中国电信", 17897: "中国电信", 134762: "中国电信",
  139019: "中国电信", 141739: "中国电信", 141771: "中国电信", 134419: "中国电信",
  140276: "中国电信", 58542: "中国电信", 140278: "中国电信", 139767: "中国电信",
  137688: "中国电信", 137691: "中国电信", 4809: "中国电信", 58466: "中国电信",
  137687: "中国电信", 134756: "中国电信", 134760: "中国电信", 133774: "中国电信",
  133775: "中国电信", 4816: "中国电信", 134768: "中国电信", 58461: "中国电信",
  58520: "中国电信", 131325: "中国电信",

  // 中国联通
  4837: "中国联通", 4808: "中国联通", 134542: "中国联通", 134543: "中国联通",
  10099: "中国联通", 140979: "中国联通", 138421: "中国联通", 17621: "中国联通",
  17622: "中国联通", 17816: "中国联通", 140726: "中国联通", 17623: "中国联通",
  136958: "中国联通", 9929: "中国联通", 140716: "中国联通",
  136959: "中国联通", 135061: "中国联通", 139007: "中国联通",

  // 云服务商
  59019: "金山云", 135377: "优刻云", 45062: "网易云", 137718: "火山引擎",
  37963: "阿里云", 45102: "阿里云国际", 45090: "腾讯云", 132203: "腾讯云国际",
  55967: "百度云", 38365: "百度云", 55990: "华为云", 136907: "华为云",
  4609: "澳門電訊", 134773: "珠江宽频", 1659: "台湾教育网", 8075: "微软云",
  17421: "中华电信", 3462: "HiNet", 13335: "Cloudflare", 55960: "亚马逊云",
  14618: "亚马逊云", 16509: "亚马逊云", 15169: "谷歌云", 396982: "谷歌云", 36492: "谷歌云",
  
  // 移除了重复的 58519，华为云与电信互联的ASN
  58519: "华为云/电信互联",
};

// 省份匹配
const provinces = ['内蒙古','黑龙江','河北','山西','吉林','辽宁','江苏','浙江','安徽','福建','江西','山东','河南','湖北','湖南','广东','海南','四川','贵州','云南','陕西','甘肃','青海','广西','西藏','宁夏','新疆','北京','天津','上海','重庆'];

function getAsInfo(number: number): string | undefined {
  return asnMap[number];
}

// 修改类型定义，使其接受Names类型或普通对象
function getDescription(names: any): string {
  if (!names) return '未知';
  return names['zh-CN'] || names.en || '未知';
}

function getCountry(country: any): string {
  const name = getDescription(country.names);
  if (['香港', '澳门', '台湾'].includes(name)) {
    return '中国' + name;
  }
  return name;
}

function provinceMatch(s: string): string {
  for (const province of provinces) {
    if (s.includes(province)) {
      return province;
    }
  }
  return '';
}

function deduplicate(regions: string[]): string[] {
  return Array.from(new Set(regions.filter(Boolean)));
}

function getAddr(ip: string, mask: number): string {
  return `${ip}/${mask}`;
}

export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url);
    const ip = searchParams.get('ip');

    if (!ip) {
      return NextResponse.json(
        { error: '请提供IP地址' },
        { status: 400 }
      );
    }

    // 验证IP地址格式 (支持IPv4和IPv6)
    const ipv4Regex = /^(\d{1,3})\.(\d{1,3})\.(\d{1,3})\.(\d{1,3})$/;
    const ipv6Regex = /^(([0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,7}:|([0-9a-fA-F]{1,4}:){1,6}:[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,5}(:[0-9a-fA-F]{1,4}){1,2}|([0-9a-fA-F]{1,4}:){1,4}(:[0-9a-fA-F]{1,4}){1,3}|([0-9a-fA-F]{1,4}:){1,3}(:[0-9a-fA-F]{1,4}){1,4}|([0-9a-fA-F]{1,4}:){1,2}(:[0-9a-fA-F]{1,4}){1,5}|[0-9a-fA-F]{1,4}:((:[0-9a-fA-F]{1,4}){1,6})|:((:[0-9a-fA-F]{1,4}){1,7}|:)|fe80:(:[0-9a-fA-F]{0,4}){0,4}%[0-9a-zA-Z]+|::(ffff(:0{1,4})?:)?((25[0-5]|(2[0-4]|1?[0-9])?[0-9])\.){3}(25[0-5]|(2[0-4]|1?[0-9])?[0-9])|([0-9a-fA-F]{1,4}:){1,4}:((25[0-5]|(2[0-4]|1?[0-9])?[0-9])\.){3}(25[0-5]|(2[0-4]|1?[0-9])?[0-9]))$/;
    
    if (!ipv4Regex.test(ip) && !ipv6Regex.test(ip)) {
      return NextResponse.json(
        { error: '请输入有效的IP地址 (IPv4或IPv6)' },
        { status: 400 }
      );
    }

    // 如果是IPv4，进一步验证每个段的范围
    if (ipv4Regex.test(ip)) {
      const parts = ip.split('.').map(part => parseInt(part, 10));
      const isValidRange = parts.every(part => part >= 0 && part <= 255);
      if (!isValidRange) {
        return NextResponse.json(
          { error: 'IPv4地址格式不正确，数值范围应为0-255' },
          { status: 400 }
        );
      }
    }

    const response: any = { ip };

    // 获取ASN信息
    try {
      const asnResult = await asnReader.asn(ip);
      if (asnResult) {
        const asNumber = asnResult.autonomousSystemNumber;
        const asName = asnResult.autonomousSystemOrganization;
        // 确保asNumber不为undefined
        if (typeof asNumber === 'number') {
          const asInfo = getAsInfo(asNumber);
          
          response.as = {
            number: asNumber,
            name: asName,
            info: asInfo || asName
          };
        } else {
          response.as = {
            name: asName,
            info: asName
          };
        }
      }
    } catch (error) {
      console.error('ASN查询错误:', error);
    }

    // 获取城市信息
    try {
      const cityResult = await cityReader.city(ip);
      if (cityResult) {
        // 获取网段信息 - 使用可选链和默认值处理可能的undefined
        let prefix = 32; // 默认值
        // network信息可能在traits中
        if (cityResult.traits && typeof cityResult.traits.network === 'string') {
          // 尝试从network字符串中提取前缀长度
          const match = /\/(\d+)$/.exec(cityResult.traits.network);
          if (match && match[1]) {
            prefix = parseInt(match[1], 10);
          }
        }
        response.addr = getAddr(ip, prefix);

        // 国家信息
        if (cityResult.country) {
          response.country = {
            code: cityResult.country.isoCode,
            name: getCountry(cityResult.country)
          };
        }

        // 注册国家信息
        if (cityResult.registeredCountry) {
          response.registered_country = {
            code: cityResult.registeredCountry.isoCode,
            name: getCountry(cityResult.registeredCountry)
          };
        }

        // 地区信息
        const regions = cityResult.subdivisions?.map(sub => getDescription(sub.names)) || [];
        if (cityResult.city) {
          const cityName = getDescription(cityResult.city.names);
          if ((!regions.length || !regions[regions.length - 1].includes(cityName)) && 
              !response.country?.name.includes(cityName)) {
            regions.push(cityName);
          }
        }
        response.regions = deduplicate(regions);

        // 地理位置信息
        if (cityResult.location) {
          response.location = {
            latitude: cityResult.location.latitude,
            longitude: cityResult.location.longitude,
            timezone: cityResult.location.timeZone
          };
        }
      }
    } catch (error) {
      console.error('城市信息查询错误:', error);
    }

    // 如果是中国IP，获取更详细的信息
    if (response.country?.code === 'CN' && 
        (!response.registered_country || response.registered_country.code === 'CN')) {
      try {
        if (cnReader) {
          // 使用maxmind原生库查询
          const cnResult = cnReader.get(ip);
          if (cnResult) {
            console.log('中国IP查询结果:', JSON.stringify(cnResult, null, 2));
            
            // 更新网段信息
            if (cnResult.network) {
              const parts = cnResult.network.split('/');
              if (parts.length === 2) {
                const prefix = parseInt(parts[1], 10);
                response.addr = getAddr(ip, prefix);
              }
            }

            // 更新地区信息
            const regions = deduplicate([
              cnResult.province,
              cnResult.city,
              cnResult.districts
            ].filter(Boolean));
            
            if (regions.length) {
              response.regions = regions;
              response.regions_short = deduplicate([
                provinceMatch(cnResult.province || ''),
                cnResult.city?.replace('市', ''),
                cnResult.districts
              ].filter(Boolean));
            }

            // 更新ISP信息
            if (!response.as) {
              response.as = {};
            }
            response.as.info = cnResult.isp;
            if (cnResult.net) {
              response.type = cnResult.net;
            }
          }
        }
      } catch (error) {
        console.error('中国IP信息查询错误:', error);
      }
    }

    return NextResponse.json(response);
  } catch (error) {
    console.error('IP查询错误:', error);
    return NextResponse.json(
      { error: 'IP查询失败，请稍后重试' },
      { status: 500 }
    );
  }
} 