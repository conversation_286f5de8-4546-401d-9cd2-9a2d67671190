import { NextResponse } from 'next/server';
import { whoisDomain } from 'whoiser';
import dns from 'dns';
import { promisify } from 'util';

// 将DNS解析方法转换为Promise形式
const dnsLookup = promisify(dns.lookup);
const dnsResolve = promisify(dns.resolve);

// 注意：在实际生产环境中，应该安装和配置适当的WHOIS查询包
// 例如 whois-json 或 whoiser 等
// 目前使用模拟数据用于演示

// 尝试通过DNS解析判断域名是否已注册
async function checkDomainByDNS(domain: string): Promise<boolean> {
  try {
    // 尝试解析A记录
    await dnsResolve(domain, 'A');
    // 如果成功解析到IP，认为域名已被注册
    return false; // 返回false表示不可用(已注册)
  } catch (error) {
    try {
      // 再尝试使用lookup方法（有些域名可能没有A记录但有其他记录）
      await dnsLookup(domain);
      // 如果成功，认为域名已被注册
      return false; // 返回false表示不可用(已注册)
    } catch (error) {
      // 如果DNS解析失败，很可能域名未被注册
      return true; // 返回true表示可用(未注册)
    }
  }
}

export async function POST(request: Request) {
  try {
    const { domain } = await request.json();

    if (!domain) {
      return NextResponse.json(
        { error: '请输入域名' },
        { status: 400 }
      );
    }

    // 简单的域名格式验证
    const domainRegex = /^[a-zA-Z0-9][a-zA-Z0-9-]{0,61}[a-zA-Z0-9](?:\.[a-zA-Z]{2,})+$/;
    if (!domainRegex.test(domain)) {
      return NextResponse.json(
        { error: '请输入有效的域名' },
        { status: 400 }
      );
    }

    try {
      // 查询域名的WHOIS信息
      const result = await whoisDomain(domain, { 
        timeout: 5000, // 设置超时时间
        follow: 1      // 设置重定向次数
      });
      
      // 分析WHOIS响应来确定域名是否可注册
      const available = isDomainAvailable(result, domain);
      
      return NextResponse.json({ domain, available });
    } catch (error) {
      console.error(`查询域名 ${domain} 时出错:`, error);
      
      // 对于某些错误，可能意味着域名可能可用
      // 通常当域名不存在时，某些WHOIS服务器会返回错误
      if (error instanceof Error && 
          (error.message.includes('No match') || 
           error.message.includes('not found') || 
           error.message.includes('No Data Found') ||
           error.message.includes('Domain not found'))) {
        return NextResponse.json({ domain, available: true });
      }
      
      // 如果是超时错误或其他错误，尝试使用DNS解析作为备选方案
      try {
        console.log(`WHOIS查询失败，尝试通过DNS解析检查域名 ${domain}...`);
        const available = await checkDomainByDNS(domain);
        return NextResponse.json({ 
          domain, 
          available,
          source: 'DNS'  // 标记数据来源为DNS解析
        });
      } catch (dnsError) {
        console.error(`DNS解析域名 ${domain} 也失败:`, dnsError);
        // 两种方法都失败，返回错误状态
        return NextResponse.json({ 
          domain, 
          error: true, 
          errorMessage: error instanceof Error ? error.message : '查询超时或服务不可用' 
        });
      }
    }
  } catch (error) {
    console.error('Domain check error:', error);
    return NextResponse.json(
      { error: '查询失败，请稍后重试' },
      { status: 500 }
    );
  }
}

/**
 * 分析WHOIS响应来确定域名是否可注册
 * @param whoisData WHOIS查询结果
 * @param domain 查询的域名
 * @returns 域名是否可用于注册
 */
function isDomainAvailable(whoisData: any, domain: string): boolean {
  // 不同TLD的WHOIS服务器返回格式不同，需要进行多种判断
  
  // 检查常见的"域名不存在"响应
  if (typeof whoisData === 'string') {
    const lowerData = whoisData.toLowerCase();
    if (
      lowerData.includes('no match') ||
      lowerData.includes('not found') ||
      lowerData.includes('no data found') ||
      lowerData.includes('domain not found') ||
      lowerData.includes('no entries found')
    ) {
      return true;
    }
  }
  
  // 处理对象格式的响应
  if (typeof whoisData === 'object') {
    // 检查域名状态
    const domainStatus = extractDomainStatus(whoisData);
    if (domainStatus) {
      // 如果状态中包含这些关键词，通常表示域名已被注册
      const registeredKeywords = ['registered', 'active', 'clienttransferprohibited', 'clientdeleteprohibited'];
      for (const keyword of registeredKeywords) {
        if (domainStatus.toLowerCase().includes(keyword)) {
          return false;
        }
      }
    }
    
    // 检查是否有注册日期/到期日期，有的话通常表示域名已被注册
    if (
      hasPropertyDeep(whoisData, 'creationDate') ||
      hasPropertyDeep(whoisData, 'registrarRegistrationExpirationDate') ||
      hasPropertyDeep(whoisData, 'expiryDate') ||
      hasPropertyDeep(whoisData, 'registrar')
    ) {
      return false;
    }
    
    // 对于某些TLD，如果WHOIS响应中包含特定的"不存在"消息
    for (const key in whoisData) {
      if (typeof whoisData[key] === 'string') {
        const value = whoisData[key].toLowerCase();
        if (
          value.includes('no match') ||
          value.includes('not found') ||
          value.includes('no data found') ||
          value.includes('domain not found') ||
          value.includes('no entries found')
        ) {
          return true;
        }
      }
    }
  }
  
  // 对于未能明确判断的情况，默认为不可注册（保守策略）
  return false;
}

/**
 * 从WHOIS数据中提取域名状态
 */
function extractDomainStatus(whoisData: any): string | null {
  if (!whoisData) return null;
  
  // 尝试不同的可能属性名
  const statusProperties = [
    'domainStatus',
    'status',
    'Domain Status'
  ];
  
  for (const prop of statusProperties) {
    if (hasPropertyDeep(whoisData, prop)) {
      const status = getPropertyDeep(whoisData, prop);
      if (status) {
        return Array.isArray(status) ? status.join(' ') : String(status);
      }
    }
  }
  
  return null;
}

/**
 * 检查对象是否包含指定的深层属性
 */
function hasPropertyDeep(obj: any, propertyPath: string): boolean {
  if (!obj || typeof obj !== 'object') return false;
  
  const parts = propertyPath.split('.');
  let current = obj;
  
  for (const part of parts) {
    if (!current || typeof current !== 'object' || !(part in current)) {
      return false;
    }
    current = current[part];
  }
  
  return true;
}

/**
 * 获取对象的深层属性值
 */
function getPropertyDeep(obj: any, propertyPath: string): any {
  if (!obj || typeof obj !== 'object') return undefined;
  
  const parts = propertyPath.split('.');
  let current = obj;
  
  for (const part of parts) {
    if (!current || typeof current !== 'object' || !(part in current)) {
      return undefined;
    }
    current = current[part];
  }
  
  return current;
}

// 模拟域名可用性检查
function simulateDomainAvailability(domain: string): boolean {
  // 使用域名作为种子生成确定性结果，这样同一个域名每次查询都会得到相同结果
  const seed = hashString(domain);
  const randomValue = seedRandom(seed);
  
  // 常见流行域名大概率已被注册
  if (
    domain.endsWith('.com') || 
    domain.endsWith('.net') || 
    domain.endsWith('.org')
  ) {
    // 短域名几乎都已被注册
    if (domain.replace(/\.[^.]+$/, '').length < 8) {
      return false;
    }
    
    // 包含常见单词的域名可能已被注册
    const commonWords = ['web', 'site', 'online', 'app', 'digital', 'tech', 'cloud', 'shop', 'store'];
    if (commonWords.some(word => domain.includes(word))) {
      return randomValue < 0.2; // 20%概率可用
    }
    
    // 其他.com/.net/.org域名
    return randomValue < 0.5; // 50%概率可用
  }
  
  // 新顶级域名有更大概率可用
  if (
    domain.endsWith('.app') || 
    domain.endsWith('.io') || 
    domain.endsWith('.dev') || 
    domain.endsWith('.ai')
  ) {
    return randomValue < 0.7; // 70%概率可用
  }
  
  // 其他顶级域名
  return randomValue < 0.8; // 80%概率可用
}

// 简单的字符串哈希函数，用于生成确定性的随机数
function hashString(str: string): number {
  let hash = 0;
  for (let i = 0; i < str.length; i++) {
    const char = str.charCodeAt(i);
    hash = ((hash << 5) - hash) + char;
    hash = hash & hash; // 转换为32bit整数
  }
  return hash;
}

// 基于种子的简单伪随机数生成器
function seedRandom(seed: number): number {
  const x = Math.sin(seed) * 10000;
  return x - Math.floor(x);
} 