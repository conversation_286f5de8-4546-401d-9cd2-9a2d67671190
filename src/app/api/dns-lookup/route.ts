import { NextRequest, NextResponse } from 'next/server';
import dns from 'dns';
import { promisify } from 'util';

// DNS解析方法的Promise化
const resolve4 = promisify(dns.resolve4);
const resolve6 = promisify(dns.resolve6);
const resolveMx = promisify(dns.resolveMx);
const resolveTxt = promisify(dns.resolveTxt);
const resolveNs = promisify(dns.resolveNs);
const resolveCname = promisify(dns.resolveCname);
const resolveSoa = promisify(dns.resolveSoa);
const resolveCaa = promisify(dns.resolveCaa);

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    
    const domain = searchParams.get('domain');
    const type = searchParams.get('type') || 'A';
    
    if (!domain) {
      return NextResponse.json(
        { error: '请提供域名参数' },
        { status: 400 }
      );
    }
    
    // 处理域名格式以确保其有效性
    const sanitizedDomain = domain.trim().toLowerCase();
    
    if (type === 'ALL') {
      // 处理所有类型记录
      const results: any = {};
      
      try { results.A = await resolve4(sanitizedDomain); } catch (e) { results.A = []; }
      try { results.AAAA = await resolve6(sanitizedDomain); } catch (e) { results.AAAA = []; }
      try { results.MX = await resolveMx(sanitizedDomain); } catch (e) { results.MX = []; }
      try { 
        const txtRecords = await resolveTxt(sanitizedDomain);
        results.TXT = txtRecords.map(record => ({ value: record.join('') }));
      } catch (e) { 
        results.TXT = []; 
      }
      try { 
        const nsRecords = await resolveNs(sanitizedDomain);
        results.NS = nsRecords.map(ns => ({ value: ns }));
      } catch (e) { 
        results.NS = []; 
      }
      try { 
        const cnameRecords = await resolveCname(sanitizedDomain);
        results.CNAME = cnameRecords.map(cname => ({ value: cname }));
      } catch (e) { 
        results.CNAME = []; 
      }
      try { results.SOA = [await resolveSoa(sanitizedDomain)]; } catch (e) { results.SOA = []; }
      try { results.CAA = await resolveCaa(sanitizedDomain); } catch (e) { results.CAA = []; }
      
      // 过滤出非空结果
      Object.keys(results).forEach(key => {
        if (results[key].length === 0) {
          delete results[key];
        }
      });
      
      return NextResponse.json(results);
    } else {
      // 处理单一类型记录
      let records;
      
      switch (type) {
        case 'A':
          records = await resolve4(sanitizedDomain);
          records = records.map(ip => ({ address: ip }));
          break;
          
        case 'AAAA':
          records = await resolve6(sanitizedDomain);
          records = records.map(ip => ({ address: ip }));
          break;
          
        case 'MX':
          records = await resolveMx(sanitizedDomain);
          break;
          
        case 'TXT':
          records = await resolveTxt(sanitizedDomain);
          records = records.map(parts => ({ value: parts.join('') }));
          break;
          
        case 'NS':
          const nsRecords = await resolveNs(sanitizedDomain);
          records = nsRecords.map(ns => ({ value: ns }));
          break;
          
        case 'CNAME':
          const cnameRecords = await resolveCname(sanitizedDomain);
          records = cnameRecords.map(cname => ({ value: cname }));
          break;
          
        case 'SOA':
          const soaRecord = await resolveSoa(sanitizedDomain);
          records = [soaRecord];
          break;
          
        case 'CAA':
          records = await resolveCaa(sanitizedDomain);
          break;
          
        default:
          return NextResponse.json(
            { error: '不支持的DNS记录类型' },
            { status: 400 }
          );
      }
      
      return NextResponse.json({ records });
    }
  } catch (error) {
    console.error('DNS lookup error:', error);
    
    // 处理常见的DNS错误
    if (error instanceof Error) {
      if (error.message.includes('ENOTFOUND')) {
        return NextResponse.json(
          { error: '找不到域名，请检查域名是否正确' },
          { status: 404 }
        );
      } else if (error.message.includes('ENODATA')) {
        return NextResponse.json(
          { error: '没有找到请求的DNS记录类型' },
          { status: 404 }
        );
      }
    }
    
    return NextResponse.json(
      { error: '查询DNS记录时出错' },
      { status: 500 }
    );
  }
}
