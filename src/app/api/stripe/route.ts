import { NextResponse } from 'next/server';
// import Strip<PERSON> from 'stripe';
import { prisma } from '@/lib/prisma';
import { getServerSession } from 'next-auth';
import { config as authOptions } from '@/auth.config';

import { v4 as uuidv4 } from 'uuid';

// 注释掉Stripe初始化代码
/*
if (!process.env.STRIPE_PRIVATE_KEY) {
  throw new Error('STRIPE_PRIVATE_KEY is not set');
}

const stripe = new Stripe(process.env.STRIPE_PRIVATE_KEY, {
  apiVersion: '2025-02-24.acacia',
});
*/

export async function POST(request: Request) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.email) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { price, successUrl, cancelUrl, email, productName } = body;

    // Print received request data
    console.log('Received payment request:', {
      price,
      email,
      successUrl,
      cancelUrl,
      productName
    });

    // Ensure price is a number and convert to cents
    const amount = Math.round(parseFloat(price) * 100);

    if (isNaN(amount)) {
      return NextResponse.json(
        { error: 'Invalid price amount' },
        { status: 400 }
      );
    }

    // Get user from database
    const user = await prisma.user.findFirst({
      where: {
        email: session.user.email,
      },
    });

    if (!user) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      );
    }

    // 注释掉Stripe相关功能
    /*
    // Create Stripe checkout session
    const stripeSession = await stripe.checkout.sessions.create({
      payment_method_types: ['card'],
      customer_email: email,
      line_items: [
        {
          price_data: {
            currency: 'usd',
            product_data: {
              name: productName || 'Purchase',
            },
            unit_amount: amount,
          },
          quantity: 1,
        },
      ],
      mode: 'payment',
      success_url: successUrl,
      cancel_url: cancelUrl,
    });
    */

    // 创建一个临时回调URL，替代Stripe支付流程
    const temporaryUrl = successUrl || '/';

    // Create order in database
    await prisma.order.create({
      data: {
        orderNo: uuidv4(),
        userUuid: user.uuid,
        userEmail: user.email,
        amount: amount,
        status: 'pending',
        stripeSessionId: 'temporary-disabled-' + Date.now(), // 临时ID
        credits: 1,
        currency: 'usd',
        productName: productName || 'Purchase',
        createdAt: new Date(),
      },
    });

    // 返回临时URL代替Stripe支付页面
    return NextResponse.json({ url: temporaryUrl });
  } catch (error: any) {
    console.error('Error processing payment:', error);
    return NextResponse.json(
      { error: error.message },
      { status: 500 }
    );
  }
}
