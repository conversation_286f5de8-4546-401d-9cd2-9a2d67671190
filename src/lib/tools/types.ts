// 工具接口定义
export interface Tool {
  id: string;
  name: string; // 默认名称（回退使用）
  en_name?: string; // 英文名称（可选）
  zh_name?: string; // 中文名称（可选）
  description: string; // 默认描述（回退使用）
  en_description?: string; // 英文描述（可选）
  zh_description?: string; // 中文描述（可选）
  icon: string;
  category: string;
  subCategory?: string;
  isFavorite: boolean;
}

// 最近使用的工具接口
export interface RecentTool {
  id: string;
  name: string;
  en_name?: string; // 英文名称（可选）
  zh_name?: string; // 中文名称（可选）
  lastUsed: string;
  en_lastUsed?: string; // 英文时间表示（可选）
  zh_lastUsed?: string; // 中文时间表示（可选）
}

// 侧边栏菜单项接口
export interface MenuItem {
  id: string;
  name: string;
  en_name?: string; // 英文名称（可选）
  zh_name?: string; // 中文名称（可选）
  icon: string;
}

// 侧边栏子菜单项接口
export interface SubMenuItem {
  id: string;
  name: string;
  en_name?: string; // 英文名称（可选）
  zh_name?: string; // 中文名称（可选）
  icon: string;
  parentId: string; // 父级分类ID
}

// 侧边栏菜单分类接口
export interface MenuCategory {
  category: string;
  en_category?: string; // 英文分类名称（可选）
  zh_category?: string; // 中文分类名称（可选）
  items: MenuItem[];
  subItems?: Record<string, SubMenuItem[]>; // 添加子菜单字段，按父级ID分组
}

// 通知接口
export interface Notification {
  id: string;
  title: string;
  message: string;
  type: 'success' | 'error' | 'info';
} 