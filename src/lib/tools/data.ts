import { Tool, RecentTool, MenuCategory } from './types';

// 工具数据
export const tools: Tool[] = [
  {
    id: 'whois',
    name: 'Whois查询',
    zh_name: 'Whois查询',
    en_name: 'Whois Lookup',
    description: '查询域名注册信息，包括所有者、注册商、注册日期和到期日期等详细信息',
    zh_description: '查询域名注册信息，包括所有者、注册商、注册日期和到期日期等详细信息',
    en_description: 'Query domain registration information, including owner, registrar, registration date and expiration date',
    icon: '🔍',
    category: '域名/IP',
    isFavorite: false
  },
  {
    id: 'ip-location',
    name: 'IP定位',
    zh_name: 'IP定位',
    en_name: 'IP Geolocation',
    description: '通过IP地址查询地理位置，包括国家、城市、经纬度和ISP提供商信息',
    zh_description: '通过IP地址查询地理位置，包括国家、城市、经纬度和ISP提供商信息',
    en_description: 'Lookup geographic location by IP address, including country, city, coordinates and ISP information',
    icon: '📍',
    category: '域名/IP',
    isFavorite: false
  },
  {
    id: 'dns-lookup',
    name: 'DNS解析',
    zh_name: 'DNS解析',
    en_name: 'DNS Lookup',
    description: '查询域名的DNS记录，包括A、AAAA、MX、TXT、NS等类型记录',
    zh_description: '查询域名的DNS记录，包括A、AAAA、MX、TXT、NS等类型记录',
    en_description: 'Query DNS records for a domain, including A, AAAA, MX, TXT, NS and other record types',
    icon: '🌐',
    category: '域名/IP',
    isFavorite: true
  },
  {
    id: 'domain-info',
    name: '域名信息',
    zh_name: '域名信息',
    en_name: 'Domain Info',
    description: '全面分析域名信息，包括服务器类型、HTTP头、SSL证书详情等',
    zh_description: '全面分析域名信息，包括服务器类型、HTTP头、SSL证书详情等',
    en_description: 'Comprehensive domain analysis including server type, HTTP headers, SSL certificate details and more',
    icon: '🔗',
    category: '域名/IP',
    isFavorite: false
  },
  {
    id: 'json-formatter',
    name: 'JSON格式化',
    zh_name: 'JSON格式化',
    en_name: 'JSON Formatter',
    description: '格式化和美化JSON数据，提高可读性，支持缩进和语法高亮',
    zh_description: '格式化和美化JSON数据，提高可读性，支持缩进和语法高亮',
    en_description: 'Format and beautify JSON data to improve readability, with indentation and syntax highlighting',
    icon: '📝',
    category: 'JSON工具',
    isFavorite: false
  },
  {
    id: 'json-js-convert',
    name: 'JSON与JS互转',
    zh_name: 'JSON与JS互转',
    en_name: 'JSON-JS Converter',
    description: 'JSON与JavaScript对象互相转换工具，支持格式化和语法高亮',
    zh_description: 'JSON与JavaScript对象互相转换工具，支持格式化和语法高亮',
    en_description: 'Convert between JSON and JavaScript objects, with formatting and syntax highlighting',
    icon: '🔄',
    category: 'JSON工具',
    isFavorite: false
  },
  {
    id: 'json-to-csv',
    name: 'JSON转CSV',
    zh_name: 'JSON转CSV',
    en_name: 'JSON to CSV',
    description: '将JSON数据转换为CSV格式和EXCEL格式，支持复杂结构转换',
    zh_description: '将JSON数据转换为CSV格式和EXCEL格式，支持复杂结构转换',
    en_description: 'Convert JSON data to CSV and EXCEL formats, supporting complex structure conversion',
    icon: '📊',
    category: 'JSON工具',
    isFavorite: false
  },
  {
    id: 'json-to-yaml',
    name: 'JSON转YAML',
    zh_name: 'JSON转YAML',
    en_name: 'JSON to YAML',
    description: '将JSON数据转换为YAML格式，保持数据结构不变',
    zh_description: '将JSON数据转换为YAML格式，保持数据结构不变',
    en_description: 'Convert JSON data to YAML format while preserving the data structure',
    icon: '📄',
    category: 'JSON工具',
    isFavorite: false
  },
  {
    id: 'code-compress',
    name: '代码压缩',
    zh_name: '代码压缩',
    en_name: 'Code Minifier',
    description: '压缩JavaScript、CSS、HTML等代码，减小文件体积',
    zh_description: '压缩JavaScript、CSS、HTML等代码，减小文件体积',
    en_description: 'Minify JavaScript, CSS, HTML and other code to reduce file size',
    icon: '📦',
    category: '开发工具',
    isFavorite: false
  },
  {
    id: 'api-test',
    name: 'API测试',
    zh_name: 'API测试',
    en_name: 'API Tester',
    description: '测试API接口，支持多种请求方法、参数设置和响应分析',
    zh_description: '测试API接口，支持多种请求方法、参数设置和响应分析',
    en_description: 'Test API endpoints with support for various request methods, parameter settings and response analysis',
    icon: '🔌',
    category: '开发工具',
    isFavorite: false
  },
  {
    id: 'regex-test',
    name: '正则测试',
    zh_name: '正则测试',
    en_name: 'Regex Tester',
    description: '测试和验证正则表达式，支持多种编程语言的正则语法',
    zh_description: '测试和验证正则表达式，支持多种编程语言的正则语法',
    en_description: 'Test and validate regular expressions with support for regex syntax in various programming languages',
    icon: '🔍',
    category: '开发工具',
    isFavorite: false
  },
  {
    id: 'proxy-check',
    name: 'IP代理检测',
    zh_name: 'IP代理检测',
    en_name: 'IP Proxy Check',
    description: '检测IP地址是否为代理IP，支持多种代理类型识别',
    zh_description: '检测IP地址是否为代理IP，支持多种代理类型识别',
    en_description: 'Detect if an IP address is a proxy, with support for identifying various proxy types',
    icon: '🛡️',
    category: '安全工具',
    subCategory: '安全检测',
    isFavorite: false
  },
  {
    id: 'ssl-generator',
    name: 'SSL证书生成',
    zh_name: 'SSL证书生成',
    en_name: 'SSL Certificate Generator',
    description: '生成自签名SSL证书，支持多种证书类型和加密算法',
    zh_description: '生成自签名SSL证书，支持多种证书类型和加密算法',
    en_description: 'Generate self-signed SSL certificates with support for various certificate types and encryption algorithms',
    icon: '🔒',
    category: '安全工具',
    subCategory: '安全检测',
    isFavorite: false
  },
  {
    id: 'privacy-check',
    name: '隐私检测',
    zh_name: '隐私检测',
    en_name: 'Privacy Check',
    description: '检测网站隐私保护措施，包括Cookie使用、数据收集等',
    zh_description: '检测网站隐私保护措施，包括Cookie使用、数据收集等',
    en_description: 'Analyze website privacy protection measures including cookie usage, data collection methods and more',
    icon: '👤',
    category: '安全工具',
    subCategory: '安全检测',
    isFavorite: false
  },
  {
    id: 'file-convert',
    name: '文件格式转换',
    zh_name: '文件格式转换',
    en_name: 'File Format Converter',
    description: '支持多种文件格式之间的转换，包括图片、文档、音频等',
    zh_description: '支持多种文件格式之间的转换，包括图片、文档、音频等',
    en_description: 'Convert between various file formats, including images, documents, audio files and more',
    icon: '📄',
    category: '实用工具',
    isFavorite: false
  },
  {
    id: 'text-process',
    name: '文本处理',
    zh_name: '文本处理',
    en_name: 'Text Processing',
    description: '提供多种文本处理功能，包括格式化、编码转换等',
    zh_description: '提供多种文本处理功能，包括格式化、编码转换等',
    en_description: 'Provides various text processing functions including formatting, encoding conversion and more',
    icon: '📋',
    category: '实用工具',
    isFavorite: false
  },
  {
    id: 'ip-generator',
    name: 'IP地址生成器',
    zh_name: 'IP地址生成器',
    en_name: 'IP Address Generator',
    description: '生成随机IP地址，支持IPv4和IPv6格式',
    zh_description: '生成随机IP地址，支持IPv4和IPv6格式',
    en_description: 'Generate random IP addresses in both IPv4 and IPv6 formats',
    icon: '🌍',
    category: '实用工具',
    isFavorite: false
  },
  {
    id: 'image-process',
    name: '图片处理',
    zh_name: '图片处理',
    en_name: 'Image Processing',
    description: '提供图片编辑、压缩、格式转换等功能',
    zh_description: '提供图片编辑、压缩、格式转换等功能',
    en_description: 'Provides image editing, compression, format conversion and other functions',
    icon: '🖼️',
    category: '实用工具',
    isFavorite: false
  },
  {
    id: 'color-extract',
    name: '颜色提取',
    zh_name: '颜色提取',
    en_name: 'Color Extractor',
    description: '从图片中提取主色调，支持RGB格式显示和颜色板生成',
    zh_description: '从图片中提取主色调，支持RGB格式显示和颜色板生成',
    en_description: 'Extract main color palettes from images, with RGB format display and color palette generation',
    icon: '🎨',
    category: '设计工具',
    isFavorite: false
  },
  {
    id: 'yaml-to-json',
    name: 'YAML转JSON',
    zh_name: 'YAML转JSON',
    en_name: 'YAML to JSON',
    description: '将YAML数据转换为JSON格式',
    zh_description: '将YAML数据转换为JSON格式',
    en_description: 'Convert YAML data to JSON format',
    icon: '📄',
    category: 'YAML工具',
    isFavorite: false
  },
  {
    id: 'yaml-to-typescript',
    name: 'YAML转TypeScript',
    zh_name: 'YAML转TypeScript',
    en_name: 'YAML to TypeScript',
    description: '将YAML数据转换为TypeScript接口定义',
    zh_description: '将YAML数据转换为TypeScript接口定义',
    en_description: 'Convert YAML data to TypeScript interface definitions',
    icon: '⚡',
    category: 'YAML工具',
    isFavorite: false
  },
  {
    id: 'yaml-to-xml',
    name: 'YAML转XML',
    zh_name: 'YAML转XML',
    en_name: 'YAML to XML',
    description: '将YAML数据转换为XML格式',
    zh_description: '将YAML数据转换为XML格式',
    en_description: 'Convert YAML data to XML format',
    icon: '🔄',
    category: 'YAML工具',
    isFavorite: false
  },
  {
    id: 'yaml-to-csv',
    name: 'YAML转CSV',
    zh_name: 'YAML转CSV',
    en_name: 'YAML to CSV',
    description: '将YAML数据转换为CSV格式',
    zh_description: '将YAML数据转换为CSV格式',
    en_description: 'Convert YAML data to CSV format',
    icon: '📊',
    category: 'YAML工具',
    isFavorite: false
  },

  {
    id: 'yaml-to-sql',
    name: 'YAML转SQL Schema',
    zh_name: 'YAML转SQL Schema',
    en_name: 'YAML to SQL Schema',
    description: '将YAML数据转换为SQL数据库模式',
    zh_description: '将YAML数据转换为SQL数据库模式',
    en_description: 'Convert YAML data to SQL database schema',
    icon: '💾',
    category: 'YAML工具',
    isFavorite: false
  },
  // {
  //   id: 'yaml-to-bigquery',
  //   name: 'YAML转BigQuery',
  //   description: '将YAML数据转换为BigQuery模式',
  //   icon: '🔍',
  //   category: 'YAML工具',
  //   isFavorite: false
  // },
  // {
  //   id: 'yaml-to-iots',
  //   name: 'YAML转io-ts',
  //   description: '将YAML数据转换为io-ts TypeScript运行时类型',
  //   icon: '🔰',
  //   category: 'YAML工具',
  //   isFavorite: false
  // },
  // {
  //   id: 'toml-to-yaml',
  //   name: 'TOML转YAML',
  //   description: '将TOML数据转换为YAML格式',
  //   icon: '🔀',
  //   category: 'YAML工具',
  //   isFavorite: false
  // },
  // {
  //   id: 'yaml-to-toml',
  //   name: 'YAML转TOML',
  //   description: '将YAML数据转换为TOML格式',
  //   icon: '🔁',
  //   category: 'YAML工具',
  //   isFavorite: false
  // },
  {
    id: 'properties-to-yaml',
    name: 'Properties转YAML',
    zh_name: 'Properties转YAML',
    en_name: 'Properties to YAML',
    description: '将Java Properties文件转换为YAML格式',
    zh_description: '将Java Properties文件转换为YAML格式',
    en_description: 'Convert Java Properties files to YAML format',
    icon: '📑',
    category: 'YAML工具',
    isFavorite: false
  },
  {
    id: 'yaml-to-properties',
    name: 'YAML转Properties',
    zh_name: 'YAML转Properties',
    en_name: 'YAML to Properties',
    description: '将YAML数据转换为Java Properties格式',
    zh_description: '将YAML数据转换为Java Properties格式',
    en_description: 'Convert YAML data to Java Properties format',
    icon: '📝',
    category: 'YAML工具',
    isFavorite: false
  },
  {
    id: 'yaml-formatter',
    name: 'YAML格式化',
    zh_name: 'YAML格式化',
    en_name: 'YAML Formatter',
    description: '格式化并美化YAML数据，提高可读性',
    zh_description: '格式化并美化YAML数据，提高可读性',
    en_description: 'Format and beautify YAML data to improve readability',
    icon: '✨',
    category: 'YAML工具',
    isFavorite: false
  },

  // 文字处理工具
  {
    id: 'word-count',
    name: '字数统计',
    zh_name: '字数统计',
    en_name: 'Word Count',
    description: '字数统计小工具，支持中文、英文、数字、标点符号等的统计',
    zh_description: '字数统计小工具，支持中文、英文、数字、标点符号等的统计',
    en_description: 'Word counting tool with support for Chinese, English, numbers, punctuation and more',
    icon: '🔢',
    category: '文字处理',
    isFavorite: false
  },
  {
    id: 'case-converter',
    name: '大小写转换',
    zh_name: '大小写转换',
    en_name: 'Case Converter',
    description: '大小写转换小工具，支持大写、小写、首字母大写、大小写互转等',
    zh_description: '大小写转换小工具，支持大写、小写、首字母大写、大小写互转等',
    en_description: 'Case conversion tool supporting uppercase, lowercase, title case, and other case transformations',
    icon: '🔠',
    category: '文字处理',
    isFavorite: false
  },
  {
    id: 'camel-underscore',
    name: '下划线驼峰互转',
    zh_name: '下划线驼峰互转',
    en_name: 'Camel/Snake Case Converter',
    description: '下划线驼峰互转小工具，支持各种命名规则之间的转换',
    zh_description: '下划线驼峰互转小工具，支持各种命名规则之间的转换',
    en_description: 'Convert between camelCase, snake_case, and other naming conventions',
    icon: '🐫',
    category: '文字处理',
    isFavorite: false
  },
  {
    id: 'cn-en-spacing',
    name: '中英文加空格',
    zh_name: '中英文加空格',
    en_name: 'Chinese/English Spacing',
    description: '中英文之间自动添加空格，提高文本可读性',
    zh_description: '中英文之间自动添加空格，提高文本可读性',
    en_description: 'Automatically add spaces between Chinese and English text to improve readability',
    icon: '📝',
    category: '文字处理',
    isFavorite: false
  },
  {
    id: 'leet-converter',
    name: 'Leet转换器',
    zh_name: 'Leet转换器',
    en_name: 'Leet Converter',
    description: '将普通文本转换为Leet文本（黑客语），如Hello转换为H3110',
    zh_description: '将普通文本转换为Leet文本（黑客语），如Hello转换为H3110',
    en_description: 'Convert normal text to Leet speak (hacker language), e.g. Hello to H3110',
    icon: '🤖',
    category: '文字处理',
    isFavorite: false
  },
  {
    id: 'text-diff',
    name: '文本对比diff',
    zh_name: '文本对比diff',
    en_name: 'Text Diff Comparison',
    description: '在线文本对比工具，支持多种对比模式，差异部分高亮显示',
    zh_description: '在线文本对比工具，支持多种对比模式，差异部分高亮显示',
    en_description: 'Online text comparison tool with multiple diff modes and highlighted differences',
    icon: '📊',
    category: '文字处理',
    isFavorite: false
  },
  {
    id: 'figlet',
    name: 'Figlet',
    zh_name: 'Figlet',
    en_name: 'Figlet',
    description: '将字符转换为大型艺术字，常用于浏览器控制台和终端',
    zh_description: '将字符转换为大型艺术字，常用于浏览器控制台和终端',
    en_description: 'Convert text to large ASCII art text, commonly used in browser consoles and terminals',
    icon: '🎨',
    category: '文字处理',
    isFavorite: false
  },
  {
    id: 'add-line-numbers',
    name: '在线添加行号',
    zh_name: '在线添加行号',
    en_name: 'Add Line Numbers',
    description: '在线批量有序添加行号，支持自定义起始行号和格式',
    zh_description: '在线批量有序添加行号，支持自定义起始行号和格式',
    en_description: 'Add line numbers to text with customizable starting number and format',
    icon: '🔢',
    category: '文字处理',
    isFavorite: false
  },
  {
    id: 'text-scrambler',
    name: '乱序文字生成器',
    zh_name: '乱序文字生成器',
    en_name: 'Text Scrambler',
    description: '乱序文字生成器可以打乱文字和段落顺序，保持原文风格',
    zh_description: '乱序文字生成器可以打乱文字和段落顺序，保持原文风格',
    en_description: 'Scramble text and paragraph order while maintaining the original style',
    icon: '🔀',
    category: '文字处理',
    isFavorite: false
  },
  // 加解密工具
  {
    id: 'md5-hash',
    name: 'MD5哈希计算',
    zh_name: 'MD5哈希计算',
    en_name: 'MD5 Hash Calculator',
    description: '快速计算字符串、文件的MD5哈希值，支持批量处理',
    zh_description: '快速计算字符串、文件的MD5哈希值，支持批量处理',
    en_description: 'Quickly calculate MD5 hash values for strings and files, with batch processing support',
    icon: '🔢',
    category: '安全工具',
    subCategory: '哈希计算',
    isFavorite: false
  },
  {
    id: 'sha-hash',
    name: 'SHA哈希计算',
    zh_name: 'SHA哈希计算',
    en_name: 'SHA Hash Calculator',
    description: '计算SHA1、SHA256、SHA512等多种哈希算法，提供结果对比',
    zh_description: '计算SHA1、SHA256、SHA512等多种哈希算法，提供结果对比',
    en_description: 'Calculate SHA1, SHA256, SHA512 and other hash algorithms with result comparison',
    icon: '🔣',
    category: '安全工具',
    subCategory: '哈希计算',
    isFavorite: false
  },
  {
    id: 'md5-collision',
    name: 'MD5在线碰撞',
    zh_name: 'MD5在线碰撞',
    en_name: 'MD5 Collision Finder',
    description: '生成MD5哈希相同的不同原始文本，基于md5 fastcall实现',
    zh_description: '生成MD5哈希相同的不同原始文本，基于md5 fastcall实现',
    en_description: 'Generate different original texts with identical MD5 hash values using md5 fastcall technique',
    icon: '💥',
    category: '安全工具',
    subCategory: '哈希计算',
    isFavorite: false
  },
  {
    id: 'aes-encryption',
    name: 'AES加解密',
    zh_name: 'AES加解密',
    en_name: 'AES Encryption/Decryption',
    description: '在线AES算法加解密工具，支持多种密钥长度和加密模式',
    zh_description: '在线AES算法加解密工具，支持多种密钥长度和加密模式',
    en_description: 'Online AES algorithm encryption/decryption tool with support for various key lengths and encryption modes',
    icon: '🔐',
    category: '安全工具',
    subCategory: '加解密工具',
    isFavorite: false
  },
  {
    id: 'des-encryption',
    name: 'DES加解密',
    zh_name: 'DES加解密',
    en_name: 'DES Encryption/Decryption',
    description: '在线DES算法加解密工具，支持多种加密模式和填充方式',
    zh_description: '在线DES算法加解密工具，支持多种加密模式和填充方式',
    en_description: 'Online DES algorithm encryption/decryption tool with various encryption modes and padding methods',
    icon: '🔏',
    category: '安全工具',
    subCategory: '加解密工具',
    isFavorite: false
  },
  {
    id: '3des-encryption',
    name: '3DES加解密',
    zh_name: '3DES加解密',
    en_name: '3DES Encryption/Decryption',
    description: '在线三重DES算法加解密工具，提供更高安全级别的加密保护',
    zh_description: '在线三重DES算法加解密工具，提供更高安全级别的加密保护',
    en_description: 'Online Triple DES algorithm tool providing higher security level encryption protection',
    icon: '🔏',
    category: '安全工具',
    subCategory: '加解密工具',
    isFavorite: false
  },
  {
    id: 'rsa-encryption',
    name: 'RSA加解密',
    zh_name: 'RSA加解密',
    en_name: 'RSA Encryption/Decryption',
    description: '在线RSA算法加解密工具，支持公钥私钥生成和管理',
    zh_description: '在线RSA算法加解密工具，支持公钥私钥生成和管理',
    en_description: 'Online RSA algorithm tool with public and private key generation and management',
    icon: '🔒',
    category: '安全工具',
    subCategory: '加解密工具',
    isFavorite: false
  },
  {
    id: 'morse-code',
    name: '摩斯电码转换',
    zh_name: '摩斯电码转换',
    en_name: 'Morse Code Converter',
    description: '在线摩斯电码加解密工具，支持文本转摩斯码和摩斯码转文本',
    zh_description: '在线摩斯电码加解密工具，支持文本转摩斯码和摩斯码转文本',
    en_description: 'Online Morse code tool supporting text to Morse code and Morse code to text conversion',
    icon: '📡',
    category: '安全工具',
    subCategory: '加解密工具',
    isFavorite: false
  },
  {
    id: 'base64-encode',
    name: 'Base64编解码',
    zh_name: 'Base64编解码',
    en_name: 'Base64 Encoding/Decoding',
    description: '在线Base64编码解码工具，支持文本和文件的Base64转换',
    zh_description: '在线Base64编码解码工具，支持文本和文件的Base64转换',
    en_description: 'Online Base64 encoding/decoding tool for text and files',
    icon: '📝',
    category: '编解码工具',
    subCategory: '文本编解码',
    isFavorite: false
  },
  {
    id: 'url-encode',
    name: 'URL编解码',
    zh_name: 'URL编解码',
    en_name: 'URL Encoding/Decoding',
    description: 'URL编码解码工具，支持批量处理和特殊字符处理',
    zh_description: 'URL编码解码工具，支持批量处理和特殊字符处理',
    en_description: 'URL encoding/decoding tool with batch processing and special character handling',
    icon: '🔗',
    category: '编解码工具',
    subCategory: '文本编解码',
    isFavorite: false
  },
  {
    id: 'image-to-base64',
    name: '图片转Base64',
    zh_name: '图片转Base64',
    en_name: 'Image to Base64',
    description: '将图片转换为Base64编码字符串，支持多种图片格式',
    zh_description: '将图片转换为Base64编码字符串，支持多种图片格式',
    en_description: 'Convert images to Base64 encoded strings, supporting various image formats',
    icon: '🖼️',
    category: '编解码工具',
    subCategory: '媒体编解码',
    isFavorite: false
  },
  {
    id: 'hex-encode',
    name: 'Hex编解码',
    zh_name: 'Hex编解码',
    en_name: 'Hex Encoding/Decoding',
    description: '字符串与十六进制编码互相转换工具，支持多种编码方式',
    zh_description: '字符串与十六进制编码互相转换工具，支持多种编码方式',
    en_description: 'Convert between strings and hexadecimal encoding with support for various encoding methods',
    icon: '🔠',
    category: '编解码工具',
    subCategory: '文本编解码',
    isFavorite: false
  },
  {
    id: 'html-entity',
    name: 'HTML实体编解码',
    zh_name: 'HTML实体编解码',
    en_name: 'HTML Entity Converter',
    description: 'HTML特殊字符和实体编码互相转换工具，防止XSS等安全问题',
    zh_description: 'HTML特殊字符和实体编码互相转换工具，防止XSS等安全问题',
    en_description: 'Convert between HTML special characters and entity encoding to prevent security issues like XSS',
    icon: '📄',
    category: '编解码工具',
    subCategory: '文本编解码',
    isFavorite: false
  },
  {
    id: 'password-generator',
    name: '密码生成器',
    zh_name: '密码生成器',
    en_name: 'Password Generator',
    description: '在线生成强密码，可自定义长度、复杂度和特殊字符',
    zh_description: '在线生成强密码，可自定义长度、复杂度和特殊字符',
    en_description: 'Generate strong passwords online with customizable length, complexity and special characters',
    icon: '🎲',
    category: '安全工具',
    subCategory: '密码工具',
    isFavorite: false
  },
  {
    id: 'password-strength',
    name: '密码强度检测',
    zh_name: '密码强度检测',
    en_name: 'Password Strength Checker',
    description: '在线检测密码的强度，分析脆弱性并提供改进建议',
    zh_description: '在线检测密码的强度，分析脆弱性并提供改进建议',
    en_description: 'Check password strength online, analyze vulnerabilities and provide improvement suggestions',
    icon: '💪',
    category: '安全工具',
    subCategory: '密码工具',
    isFavorite: false
  },
  {
    id: 'zip-password-crack',
    name: 'ZIP密码破解',
    zh_name: 'ZIP密码破解',
    en_name: 'ZIP Password Cracker',
    description: '在线破解ZIP压缩包的文件密码，支持多种破解方法',
    zh_description: '在线破解ZIP压缩包的文件密码，支持多种破解方法',
    en_description: 'Crack ZIP file passwords online using various cracking methods',
    icon: '📦',
    category: '安全工具',
    subCategory: '密码工具',
    isFavorite: false
  },
  {
    id: 'htpasswd-generator',
    name: 'Htpasswd生成器',
    zh_name: 'Htpasswd生成器',
    en_name: 'Htpasswd Generator',
    description: '在线生成Apache/Nginx服务器的Basic Auth密码文件，支持多种加密算法',
    zh_description: '在线生成Apache/Nginx服务器的Basic Auth密码文件，支持多种加密算法',
    en_description: 'Generate Basic Auth password files for Apache/Nginx servers with support for various encryption algorithms',
    icon: '🔑',
    category: '编解码工具',
    subCategory: '服务器工具',
    isFavorite: false
  },
  {
    id: 'jsfuck-encoder',
    name: 'JSFuck加密',
    zh_name: 'JSFuck加密',
    en_name: 'JSFuck Encoder',
    description: '将JavaScript代码转换为仅使用6个字符的等效代码，用于代码混淆',
    zh_description: '将JavaScript代码转换为仅使用6个字符的等效代码，用于代码混淆',
    en_description: 'Convert JavaScript code to equivalent code using only 6 characters for code obfuscation',
    icon: '🤯',
    category: '编解码工具',
    subCategory: '代码转换',
    isFavorite: false
  },
  {
    id: 'unicode-converter',
    name: 'Unicode转换',
    zh_name: 'Unicode转换',
    en_name: 'Unicode Converter',
    description: '文本与Unicode编码互相转换，支持各种Unicode格式',
    zh_description: '文本与Unicode编码互相转换，支持各种Unicode格式',
    en_description: 'Convert between text and Unicode encoding with support for various Unicode formats',
    icon: '🌐',
    category: '编解码工具',
    subCategory: '文本编解码',
    isFavorite: false
  },
  {
    id: 'jwt-decoder',
    name: 'JWT解析',
    zh_name: 'JWT解析',
    en_name: 'JWT Decoder',
    description: '在线解析和验证JSON Web Token，显示头部和载荷信息',
    zh_description: '在线解析和验证JSON Web Token，显示头部和载荷信息',
    en_description: 'Parse and validate JSON Web Tokens online, displaying header and payload information',
    icon: '🔍',
    category: '编解码工具',
    subCategory: '开发工具',
    isFavorite: false
  },
  // 图片加工工具
  {
    id: 'img-compress',
    name: '图片压缩',
    zh_name: '图片压缩',
    en_name: 'Image Compression',
    description: '压缩图片文件大小，支持多种压缩算法和参数设置',
    zh_description: '压缩图片文件大小，支持多种压缩算法和参数设置',
    en_description: 'Compress image file size with support for various compression algorithms and parameters',
    icon: '📦',
    category: '图片加工',
    isFavorite: false
  },
  {
    id: 'img-resize',
    name: '图片调整大小',
    zh_name: '图片调整大小',
    en_name: 'Image Resize',
    description: '支持按像素或百分比调整图片大小，保持图片质量',
    zh_description: '支持按像素或百分比调整图片大小，保持图片质量',
    en_description: 'Resize images by pixels or percentage while maintaining quality',
    icon: '🖼️',
    category: '图片加工',
    isFavorite: false
  },
  {
    id: 'img-to-base64',
    name: '图片转Base64',
    zh_name: '图片转Base64',
    en_name: 'Image to Base64',
    description: '图片转换为BASE64编码，使用字符串代替图片地址',
    zh_description: '图片转换为BASE64编码，使用字符串代替图片地址',
    en_description: 'Convert images to BASE64 encoding, using strings instead of image URLs',
    icon: '🖼️',
    category: '图片加工',
    isFavorite: false
  },
  {
    id: 'img-watermark',
    name: '图片添加水印',
    zh_name: '图片添加水印',
    en_name: 'Add Watermark',
    description: '给图片添加文字或图片水印，支持自定义位置、透明度',
    zh_description: '给图片添加文字或图片水印，支持自定义位置、透明度',
    en_description: 'Add text or image watermarks to pictures, with custom positioning and transparency',
    icon: '💧',
    category: '图片加工',
    isFavorite: false
  },
  {
    id: 'img-rounded',
    name: '图片添加圆角',
    zh_name: '图片添加圆角',
    en_name: 'Add Rounded Corners',
    description: '给图片添加圆角效果，支持自定义圆角半径',
    zh_description: '给图片添加圆角效果，支持自定义圆角半径',
    en_description: 'Add rounded corner effects to images, with customizable radius',
    icon: '⭕',
    category: '图片加工',
    isFavorite: false
  },
  {
    id: 'img-grayscale',
    name: '图片去色',
    zh_name: '图片去色',
    en_name: 'Grayscale Image',
    description: '将彩色图片转换为黑白图片，支持调整对比度',
    zh_description: '将彩色图片转换为黑白图片，支持调整对比度',
    en_description: 'Convert color images to black and white, with adjustable contrast',
    icon: '⚪',
    category: '图片加工',
    isFavorite: false
  },
  {
    id: 'img-pixelate',
    name: '图片像素化',
    zh_name: '图片像素化',
    en_name: 'Pixelate Image',
    description: '将图片转换为像素风格，支持调整像素大小',
    zh_description: '将图片转换为像素风格，支持调整像素大小',
    en_description: 'Convert images to pixel art style, with adjustable pixel size',
    icon: '🔳',
    category: '图片加工',
    isFavorite: false
  },
  {
    id: 'img-converter',
    name: '图片格式转换',
    zh_name: '图片格式转换',
    en_name: 'Image Format Converter',
    description: '支持png、jpg、jpeg、webp、bmp、gif、ico等格式互转',
    zh_description: '支持png、jpg、jpeg、webp、bmp、gif、ico等格式互转',
    en_description: 'Convert between various image formats including png, jpg, jpeg, webp, bmp, gif, and ico',
    icon: '🔄',
    category: '图片加工',
    isFavorite: false
  },
  {
    id: 'img-ocr',
    name: '图片OCR识别',
    zh_name: '图片OCR识别',
    en_name: 'Image OCR',
    description: '支持识别中文、英语、俄语、德语、法语、日语、韩语等多语言文字',
    zh_description: '支持识别中文、英语、俄语、德语、法语、日语、韩语等多语言文字',
    en_description: 'Recognize text in multiple languages including Chinese, English, Russian, German, French, Japanese, and Korean',
    icon: '👁️',
    category: '图片加工',
    isFavorite: false
  },
  {
    id: 'img-slice',
    name: '多格切图',
    zh_name: '多格切图',
    en_name: 'Image Slicer',
    description: '将一张图片切割成多张小图，支持自定义行列数',
    zh_description: '将一张图片切割成多张小图，支持自定义行列数',
    en_description: 'Slice a single image into multiple smaller images with customizable rows and columns',
    icon: '✂️',
    category: '图片加工',
    isFavorite: false
  },
  // 视频音频工具
  {
    id: 'audio-converter',
    name: '音频格式转换',
    zh_name: '音频格式转换',
    en_name: 'Audio Format Converter',
    description: '支持MP3、WAV、AAC、FLAC、OGG等音频格式互转，保持音质',
    zh_description: '支持MP3、WAV、AAC、FLAC、OGG等音频格式互转，保持音质',
    en_description: 'Convert between audio formats such as MP3, WAV, AAC, FLAC, and OGG while preserving audio quality',
    icon: '🎵',
    category: '视频音频',
    isFavorite: false
  },
  {
    id: 'video-converter',
    name: '视频格式转换',
    zh_name: '视频格式转换',
    en_name: 'Video Format Converter',
    description: '支持MP4、AVI、MOV、MKV、WMV等视频格式互转，可调整分辨率和码率',
    zh_description: '支持MP4、AVI、MOV、MKV、WMV等视频格式互转，可调整分辨率和码率',
    en_description: 'Convert between video formats such as MP4, AVI, MOV, MKV, and WMV with adjustable resolution and bitrate',
    icon: '🎬',
    category: '视频音频',
    isFavorite: false
  },
  {
    id: 'video-to-audio',
    name: '视频提取音频',
    zh_name: '视频提取音频',
    en_name: 'Extract Audio from Video',
    description: '从视频文件中提取音频轨道，支持多种输出格式',
    zh_description: '从视频文件中提取音频轨道，支持多种输出格式',
    en_description: 'Extract audio tracks from video files, supporting various output formats',
    icon: '🎧',
    category: '视频音频',
    isFavorite: false
  },
  {
    id: 'video-frame',
    name: '视频帧截图',
    zh_name: '视频帧截图',
    en_name: 'Video Frame Capture',
    description: '从视频中截取指定时间点的画面，支持批量截取',
    zh_description: '从视频中截取指定时间点的画面，支持批量截取',
    en_description: 'Capture frames from specific timestamps in videos, with batch capture support',
    icon: '📸',
    category: '视频音频',
    isFavorite: false
  },
  {
    id: 'video-to-gif',
    name: '视频转GIF',
    zh_name: '视频转GIF',
    en_name: 'Video to GIF',
    description: '将视频片段转换为GIF动图，可自定义帧率和尺寸',
    zh_description: '将视频片段转换为GIF动图，可自定义帧率和尺寸',
    en_description: 'Convert video clips to GIF animations with customizable frame rate and dimensions',
    icon: '🎞️',
    category: '视频音频',
    isFavorite: false
  },
  // 开发工具
  {
    id: 'base-converter',
    name: '进制转换',
    zh_name: '进制转换',
    en_name: 'Number Base Converter',
    description: '支持二进制、八进制、十进制、十六进制等之间的互相转换',
    zh_description: '支持二进制、八进制、十进制、十六进制等之间的互相转换',
    en_description: 'Convert between binary, octal, decimal, hexadecimal, and other number bases',
    icon: '🔢',
    category: '开发工具',
    isFavorite: false
  },
  {
    id: 'git-cheatsheet',
    name: 'Git指令速查',
    zh_name: 'Git指令速查',
    en_name: 'Git Command Cheatsheet',
    description: '常用Git命令速查表，包含详细的命令说明和使用场景',
    zh_description: '常用Git命令速查表，包含详细的命令说明和使用场景',
    en_description: 'Quick reference for common Git commands with detailed explanations and usage scenarios',
    icon: '📘',
    category: '开发工具',
    isFavorite: false
  },
  {
    id: 'cidr-calculator',
    name: 'CIDR计算',
    zh_name: 'CIDR计算',
    en_name: 'CIDR Calculator',
    description: '计算CIDR地址范围、子网掩码、可用IP数等信息',
    zh_description: '计算CIDR地址范围、子网掩码、可用IP数等信息',
    en_description: 'Calculate CIDR address ranges, subnet masks, available IP counts, and other network information',
    icon: '🧮',
    category: '开发工具',
    isFavorite: false
  },
  {
    id: 'cidr-aggregator',
    name: 'CIDR聚合工具',
    zh_name: 'CIDR聚合工具',
    en_name: 'CIDR Aggregator',
    description: '在线聚合IP/CIDR，将多个IP地址段合并为最优CIDR表示',
    zh_description: '在线聚合IP/CIDR，将多个IP地址段合并为最优CIDR表示',
    en_description: 'Aggregate IP/CIDR ranges online, combining multiple IP address ranges into optimal CIDR notation',
    icon: '🔄',
    category: '开发工具',
    isFavorite: false
  },
  {
    id: 'file-type-detector',
    name: '文件格式识别',
    zh_name: '文件格式识别',
    en_name: 'File Format Detector',
    description: '根据文件头识别文件格式，支持常见的文档、图片、音视频等格式',
    zh_description: '根据文件头识别文件格式，支持常见的文档、图片、音视频等格式',
    en_description: 'Identify file formats using file headers, supporting common document, image, audio/video formats',
    icon: '🔍',
    category: '开发工具',
    isFavorite: false
  },
  // 设计工具
  {
    id: 'color-converter',
    name: '颜色格式转换',
    zh_name: '颜色格式转换',
    en_name: 'Color Format Converter',
    description: '在RGB、HEX、HSL等不同颜色表示格式之间转换',
    zh_description: '在RGB、HEX、HSL等不同颜色表示格式之间转换',
    en_description: 'Convert between different color formats including RGB, HEX, HSL, and more',
    icon: '🎨',
    category: '设计工具',
    isFavorite: false
  },
  {
    id: 'excalidraw-board',
    name: '手绘白板',
    zh_name: '手绘白板',
    en_name: 'Sketch Whiteboard',
    description: '基于Excalidraw的技术文章配图工具，支持流程图、统计图、原型图等',
    zh_description: '基于Excalidraw的技术文章配图工具，支持流程图、统计图、原型图等',
    en_description: 'Technical article illustration tool based on Excalidraw, supporting flowcharts, statistical graphs, and wireframes',
    icon: '✏️',
    category: '设计工具',
    isFavorite: false
  },
  {
    id: 'color-picker',
    name: '颜色吸取器',
    zh_name: '颜色吸取器',
    en_name: 'Color Picker',
    description: '在线颜色吸取器，快速生成十种常用颜色的代码',
    zh_description: '在线颜色吸取器，快速生成十种常用颜色的代码',
    en_description: 'Online color picker tool that quickly generates codes for ten commonly used colors',
    icon: '🎯',
    category: '设计工具',
    isFavorite: false
  },
  {
    id: 'nightingale-rose',
    name: '南丁格尔玫瑰图',
    zh_name: '南丁格尔玫瑰图',
    en_name: 'Nightingale Rose Chart',
    description: '在线绘制南丁格尔玫瑰图，支持数据导入和样式自定义',
    zh_description: '在线绘制南丁格尔玫瑰图，支持数据导入和样式自定义',
    en_description: 'Create Nightingale Rose Charts online with data import and style customization',
    icon: '🌹',
    category: '设计工具',
    isFavorite: false
  },
  {
    id: 'radar-chart',
    name: '雷达图',
    zh_name: '雷达图',
    en_name: 'Radar Chart',
    description: '在线绘制雷达图，支持多维数据展示和样式配置',
    zh_description: '在线绘制雷达图，支持多维数据展示和样式配置',
    en_description: 'Create radar charts online with multi-dimensional data display and style configuration',
    icon: '📊',
    category: '设计工具',
    isFavorite: false
  },
  {
    id: 'line-chart',
    name: '折线图',
    zh_name: '折线图',
    en_name: 'Line Chart',
    description: '在线绘制折线图，支持多条数据线和坐标轴配置',
    zh_description: '在线绘制折线图，支持多条数据线和坐标轴配置',
    en_description: 'Create line charts online with support for multiple data lines and axis configuration',
    icon: '📈',
    category: '设计工具',
    isFavorite: false
  },
  // 实用工具
  {
    id: 'random-generator',
    name: '随机数/密码生成',
    zh_name: '随机数/密码生成',
    en_name: 'Random Number/Password Generator',
    description: '在本地生成随机数或密码，支持自定义规则和长度',
    zh_description: '在本地生成随机数或密码，支持自定义规则和长度',
    en_description: 'Generate random numbers or passwords locally with customizable rules and lengths',
    icon: '🎲',
    category: '实用工具',
    isFavorite: false
  },
  {
    id: 'random-ip',
    name: '随机IP生成器',
    zh_name: '随机IP生成器',
    en_name: 'Random IP Generator',
    description: '在本地生成随机IP地址，支持IPv4和IPv6格式',
    zh_description: '在本地生成随机IP地址，支持IPv4和IPv6格式',
    en_description: 'Generate random IP addresses locally in both IPv4 and IPv6 formats',
    icon: '🌐',
    category: '实用工具',
    isFavorite: false
  },
  {
    id: 'random-useragent',
    name: '随机USER-AGENT',
    zh_name: '随机USER-AGENT',
    en_name: 'Random User-Agent Generator',
    description: '生成随机的浏览器User-Agent字符串',
    zh_description: '生成随机的浏览器User-Agent字符串',
    en_description: 'Generate random browser User-Agent strings',
    icon: '🌍',
    category: '实用工具',
    isFavorite: false
  },
  {
    id: 'qrcode-generator',
    name: '二维码生成器',
    zh_name: '二维码生成器',
    en_name: 'QR Code Generator',
    description: '生成自定义二维码，支持Logo添加和样式定制',
    zh_description: '生成自定义二维码，支持Logo添加和样式定制',
    en_description: 'Generate custom QR codes with logo embedding and style customization',
    icon: '📱',
    category: '实用工具',
    isFavorite: false
  },
  {
    id: 'qrcode-parser',
    name: '二维码解析器',
    zh_name: '二维码解析器',
    en_name: 'QR Code Scanner',
    description: '解析二维码图片内容，支持批量处理',
    zh_description: '解析二维码图片内容，支持批量处理',
    en_description: 'Decode QR code image content with batch processing support',
    icon: '🔍',
    category: '实用工具',
    isFavorite: false
  },
  {
    id: 'countdown',
    name: '倒计时',
    zh_name: '倒计时',
    en_name: 'Countdown Timer',
    description: '在线倒计时工具，支持自定义时间和提醒',
    zh_description: '在线倒计时工具，支持自定义时间和提醒',
    en_description: 'Online countdown timer with customizable time settings and reminders',
    icon: '⏲️',
    category: '实用工具',
    isFavorite: false
  },
  {
    id: 'random-email',
    name: '随机邮箱生成',
    zh_name: '随机邮箱生成',
    en_name: 'Random Email Generator',
    description: '生成随机邮箱地址，支持自定义域名',
    zh_description: '生成随机邮箱地址，支持自定义域名',
    en_description: 'Generate random email addresses with customizable domain names',
    icon: '📧',
    category: '实用工具',
    isFavorite: false
  },
  {
    id: 'life-grid',
    name: '人生格子',
    zh_name: '人生格子',
    en_name: 'Life Grid Calendar',
    description: '可视化你的人生进度，以周为单位的时间格子',
    zh_description: '可视化你的人生进度，以周为单位的时间格子',
    en_description: 'Visualize your life progress with a weekly time grid',
    icon: '📅',
    category: '实用工具',
    isFavorite: false
  },
  {
    id: 'food-picker',
    name: '早中晚吃啥呢',
    zh_name: '早中晚吃啥呢',
    en_name: 'Meal Idea Picker',
    description: '干饭人必备，随机推荐美食，支持自定义菜单',
    zh_description: '干饭人必备，随机推荐美食，支持自定义菜单',
    en_description: 'Essential for food lovers, randomly recommends meals with customizable menu options',
    icon: '🍚',
    category: '实用工具',
    isFavorite: false
  }
];

// 最近使用的工具数据
export const recentTools: RecentTool[] = [
  {
    id: '1',
    name: 'AI 图像生成',
    zh_name: 'AI 图像生成',
    en_name: 'AI Image Generation',
    lastUsed: '2小时前',
    en_lastUsed: '2 hours ago',
    zh_lastUsed: '2小时前'
  },
  {
    id: '2',
    name: '文本翻译',
    zh_name: '文本翻译',
    en_name: 'Text Translation',
    lastUsed: '昨天',
    en_lastUsed: 'Yesterday',
    zh_lastUsed: '昨天'
  }
  // 可以添加更多最近使用的工具
];

// 侧边栏菜单数据
export const sidebarMenu: MenuCategory[] = [
  {
    category: '常用工具',
    en_category: 'Common Tools',
    zh_category: '常用工具',
    items: [
      { id: 'all', name: '全部工具', en_name: 'All Tools', zh_name: '全部工具', icon: '📦' },
      { id: 'favorites', name: '收藏夹', en_name: 'Favorites', zh_name: '收藏夹', icon: '⭐' },
      { id: 'recent', name: '最近使用', en_name: 'Recently Used', zh_name: '最近使用', icon: '🕒' }
    ]
  },
  {
    category: 'JSON工具',
    en_category: 'JSON Tools',
    zh_category: 'JSON工具',
    items: [
      { id: 'json-formatter', name: 'JSON格式化', en_name: 'JSON Formatter', zh_name: 'JSON格式化', icon: '📝' },
      { id: 'json-js-convert', name: 'JSON与JS互转', en_name: 'JSON-JS Converter', zh_name: 'JSON与JS互转', icon: '🔄' },
      { id: 'json-to-csv', name: 'JSON转CSV', en_name: 'JSON to CSV', zh_name: 'JSON转CSV', icon: '📊' },
      { id: 'json-to-yaml', name: 'JSON转YAML', en_name: 'JSON to YAML', zh_name: 'JSON转YAML', icon: '📄' }
    ]
  },
  {
    category: 'YAML工具',
    en_category: 'YAML Tools',
    zh_category: 'YAML工具',
    items: [
      { id: 'yaml-to-json', name: 'YAML转JSON', en_name: 'YAML to JSON', zh_name: 'YAML转JSON', icon: '📄' },
      { id: 'yaml-to-typescript', name: 'YAML转TypeScript', en_name: 'YAML to TypeScript', zh_name: 'YAML转TypeScript', icon: '⚡' },
      { id: 'yaml-to-xml', name: 'YAML转XML', en_name: 'YAML to XML', zh_name: 'YAML转XML', icon: '🔄' },
      { id: 'yaml-to-csv', name: 'YAML转CSV', en_name: 'YAML to CSV', zh_name: 'YAML转CSV', icon: '📊' }
    ]
  },
  {
    category: '安全工具',
    en_category: 'Security Tools',
    zh_category: '安全工具',
    items: [
      { id: 'security-all', name: '所有安全工具', en_name: 'All Security Tools', zh_name: '所有安全工具', icon: '🔐' },
      { id: 'hash-tools', name: '哈希计算', en_name: 'Hash Calculation', zh_name: '哈希计算', icon: '🔢' },
      { id: 'encryption-tools', name: '加解密工具', en_name: 'Encryption Tools', zh_name: '加解密工具', icon: '🔒' },
      { id: 'password-tools', name: '密码工具', en_name: 'Password Tools', zh_name: '密码工具', icon: '🔑' },
      { id: 'security-check', name: '安全检测', en_name: 'Security Check', zh_name: '安全检测', icon: '🛡️' }
    ],
    subItems: {
      'hash-tools': [
        { id: 'md5-hash', name: 'MD5哈希计算', en_name: 'MD5 Hash', zh_name: 'MD5哈希计算', icon: '🔢', parentId: 'hash-tools' },
        { id: 'sha-hash', name: 'SHA哈希计算', en_name: 'SHA Hash', zh_name: 'SHA哈希计算', icon: '🔣', parentId: 'hash-tools' },
        { id: 'md5-collision', name: 'MD5在线碰撞', en_name: 'MD5 Collision', zh_name: 'MD5在线碰撞', icon: '💥', parentId: 'hash-tools' }
      ],
      'encryption-tools': [
        { id: 'aes-encryption', name: 'AES加解密', en_name: 'AES Encryption', zh_name: 'AES加解密', icon: '🔐', parentId: 'encryption-tools' },
        { id: 'des-encryption', name: 'DES加解密', en_name: 'DES Encryption', zh_name: 'DES加解密', icon: '🔏', parentId: 'encryption-tools' },
        { id: '3des-encryption', name: '3DES加解密', en_name: '3DES Encryption', zh_name: '3DES加解密', icon: '🔏', parentId: 'encryption-tools' },
        { id: 'rsa-encryption', name: 'RSA加解密', en_name: 'RSA Encryption', zh_name: 'RSA加解密', icon: '🔒', parentId: 'encryption-tools' },
        { id: 'morse-code', name: '摩斯电码转换', en_name: 'Morse Code', zh_name: '摩斯电码转换', icon: '📡', parentId: 'encryption-tools' }
      ],
      'password-tools': [
        { id: 'password-generator-alt', name: '密码生成器', en_name: 'Password Generator', zh_name: '密码生成器', icon: '🎲', parentId: 'password-tools' },
        { id: 'password-strength', name: '密码强度检测', en_name: 'Password Strength', zh_name: '密码强度检测', icon: '💪', parentId: 'password-tools' },
        { id: 'zip-password-crack', name: 'ZIP密码破解', en_name: 'ZIP Password Crack', zh_name: 'ZIP密码破解', icon: '📦', parentId: 'password-tools' }
      ],
      'security-check': [
        { id: 'proxy-check', name: 'IP代理检测', en_name: 'IP Proxy Check', zh_name: 'IP代理检测', icon: '🛡️', parentId: 'security-check' },
        { id: 'ssl-generator', name: 'SSL证书生成', en_name: 'SSL Certificate Generator', zh_name: 'SSL证书生成', icon: '🔒', parentId: 'security-check' },
        { id: 'privacy-check', name: '隐私检测', en_name: 'Privacy Check', zh_name: '隐私检测', icon: '👤', parentId: 'security-check' }
      ]
    }
  },
  {
    category: '域名/IP',
    en_category: 'Domain/IP',
    zh_category: '域名/IP',
    items: [
      { id: 'whois', name: 'Whois查询', en_name: 'Whois Lookup', zh_name: 'Whois查询', icon: '🔍' },
      { id: 'ip-location', name: 'IP定位', en_name: 'IP Geolocation', zh_name: 'IP定位', icon: '📍' },
      { id: 'dns-lookup', name: 'DNS解析', en_name: 'DNS Lookup', zh_name: 'DNS解析', icon: '🌐' },
      { id: 'domain-info', name: '域名信息', en_name: 'Domain Info', zh_name: '域名信息', icon: '🔗' }
    ]
  },
  {
    category: '开发工具',
    en_category: 'Development Tools',
    zh_category: '开发工具',
    items: [
      { id: 'code-compress', name: '代码压缩', en_name: 'Code Minifier', zh_name: '代码压缩', icon: '📦' },
      { id: 'api-test', name: 'API测试', en_name: 'API Tester', zh_name: 'API测试', icon: '🔌' },
      { id: 'regex-test', name: '正则测试', en_name: 'Regex Tester', zh_name: '正则测试', icon: '🔍' },
      { id: 'base-converter', name: '进制转换', en_name: 'Base Converter', zh_name: '进制转换', icon: '🔢' },
      { id: 'git-cheatsheet', name: 'Git指令速查', en_name: 'Git Cheatsheet', zh_name: 'Git指令速查', icon: '📘' },
      { id: 'cidr-calculator', name: 'CIDR计算', en_name: 'CIDR Calculator', zh_name: 'CIDR计算', icon: '🧮' },
      { id: 'cidr-aggregator', name: 'CIDR聚合工具', en_name: 'CIDR Aggregator', zh_name: 'CIDR聚合工具', icon: '🔄' },
      { id: 'file-type-detector', name: '文件格式识别', en_name: 'File Format Detector', zh_name: '文件格式识别', icon: '🔍' }
    ]
  },
  {
    category: '实用工具',
    en_category: 'Utility Tools',
    zh_category: '实用工具',
    items: [
      { id: 'file-convert', name: '文件格式转换', en_name: 'File Format Converter', zh_name: '文件格式转换', icon: '📄' },
      { id: 'text-process', name: '文本处理', en_name: 'Text Processing', zh_name: '文本处理', icon: '📋' },
      { id: 'random-generator', name: '随机数/密码生成', en_name: 'Random Generator', zh_name: '随机数/密码生成', icon: '🎲' },
      { id: 'random-ip', name: '随机IP生成器', en_name: 'Random IP Generator', zh_name: '随机IP生成器', icon: '🌐' },
      { id: 'random-useragent', name: '随机USER-AGENT', en_name: 'Random User-Agent', zh_name: '随机USER-AGENT', icon: '🌍' },
      { id: 'qrcode-generator', name: '二维码生成器', en_name: 'QR Code Generator', zh_name: '二维码生成器', icon: '📱' },
      { id: 'qrcode-parser', name: '二维码解析器', en_name: 'QR Code Parser', zh_name: '二维码解析器', icon: '🔍' },
      { id: 'countdown', name: '倒计时', en_name: 'Countdown', zh_name: '倒计时', icon: '⏲️' },
      { id: 'random-email', name: '随机邮箱生成', en_name: 'Random Email Generator', zh_name: '随机邮箱生成', icon: '📧' },
      { id: 'life-grid', name: '人生格子', en_name: 'Life Grid', zh_name: '人生格子', icon: '📅' },
      { id: 'food-picker', name: '早中晚吃啥呢', en_name: 'Meal Picker', zh_name: '早中晚吃啥呢', icon: '🍚' }
    ]
  },
  {
    category: '设计工具',
    en_category: 'Design Tools',
    zh_category: '设计工具',
    items: [
      { id: 'color-extract', name: '颜色提取', en_name: 'Color Extractor', zh_name: '颜色提取', icon: '🎨' },
      { id: 'color-converter', name: '颜色格式转换', en_name: 'Color Converter', zh_name: '颜色格式转换', icon: '🎨' },
      { id: 'excalidraw-board', name: '手绘白板', en_name: 'Drawing Board', zh_name: '手绘白板', icon: '✏️' },
      { id: 'color-picker', name: '颜色吸取器', en_name: 'Color Picker', zh_name: '颜色吸取器', icon: '🎯' },
      { id: 'nightingale-rose', name: '南丁格尔玫瑰图', en_name: 'Nightingale Rose Chart', zh_name: '南丁格尔玫瑰图', icon: '🌹' },
      { id: 'radar-chart', name: '雷达图', en_name: 'Radar Chart', zh_name: '雷达图', icon: '📊' },
      { id: 'line-chart', name: '折线图', en_name: 'Line Chart', zh_name: '折线图', icon: '📈' }
    ]
  },
  {
    category: '文字处理',
    en_category: 'Text Processing',
    zh_category: '文字处理',
    items: [
      { id: 'word-count', name: '字数统计', en_name: 'Word Count', zh_name: '字数统计', icon: '🔢' },
      { id: 'case-converter', name: '大小写转换', en_name: 'Case Converter', zh_name: '大小写转换', icon: '🔠' },
      { id: 'camel-underscore', name: '下划线驼峰互转', en_name: 'Camel/Snake Case', zh_name: '下划线驼峰互转', icon: '🐫' },
      { id: 'cn-en-spacing', name: '中英文加空格', en_name: 'CN/EN Spacing', zh_name: '中英文加空格', icon: '📝' },
      { id: 'leet-converter', name: 'Leet转换器', en_name: 'Leet Converter', zh_name: 'Leet转换器', icon: '🤖' },
      { id: 'text-diff', name: '文本对比diff', en_name: 'Text Diff', zh_name: '文本对比diff', icon: '📊' },
      { id: 'figlet', name: 'Figlet', en_name: 'Figlet', zh_name: 'Figlet', icon: '🎨' },
      { id: 'add-line-numbers', name: '在线添加行号', en_name: 'Add Line Numbers', zh_name: '在线添加行号', icon: '🔢' },
      { id: 'text-scrambler', name: '乱序文字生成器', en_name: 'Text Scrambler', zh_name: '乱序文字生成器', icon: '🔀' }
    ]
  },
  {
    category: '编解码工具',
    en_category: 'Encoding Tools',
    zh_category: '编解码工具',
    items: [
      { id: 'text-encode', name: '文本编解码', en_name: 'Text Encoding', zh_name: '文本编解码', icon: '📝' },
      { id: 'media-encode', name: '媒体编解码', en_name: 'Media Encoding', zh_name: '媒体编解码', icon: '🖼️' },
      { id: 'dev-encode', name: '开发编解码', en_name: 'Dev Encoding', zh_name: '开发编解码', icon: '💻' },
      { id: 'server-encode', name: '服务器工具', en_name: 'Server Tools', zh_name: '服务器工具', icon: '🖥️' }
    ],
    subItems: {
      'text-encode': [
        { id: 'url-encode', name: 'URL编解码', en_name: 'URL Encoding', zh_name: 'URL编解码', icon: '🔗', parentId: 'text-encode' },
        { id: 'base64-encode', name: 'Base64编解码', en_name: 'Base64 Encoding', zh_name: 'Base64编解码', icon: '📝', parentId: 'text-encode' },
        { id: 'hex-encode', name: 'Hex编解码', en_name: 'Hex Encoding', zh_name: 'Hex编解码', icon: '🔠', parentId: 'text-encode' },
        { id: 'html-entity', name: 'HTML实体编解码', en_name: 'HTML Entity', zh_name: 'HTML实体编解码', icon: '📄', parentId: 'text-encode' },
        { id: 'unicode-converter', name: 'Unicode转换', en_name: 'Unicode Converter', zh_name: 'Unicode转换', icon: '🌐', parentId: 'text-encode' }
      ],
      'media-encode': [
        { id: 'image-to-base64', name: '图片转Base64', en_name: 'Image to Base64', zh_name: '图片转Base64', icon: '🖼️', parentId: 'media-encode' }
      ],
      'dev-encode': [
        { id: 'json-js-convert', name: 'JSON与JS互转', en_name: 'JSON-JS Converter', zh_name: 'JSON与JS互转', icon: '📊', parentId: 'dev-encode' },
        { id: 'jsfuck-encoder', name: 'JSFuck加密', en_name: 'JSFuck Encoder', zh_name: 'JSFuck加密', icon: '🤯', parentId: 'dev-encode' },
        { id: 'jwt-decoder', name: 'JWT解析', en_name: 'JWT Decoder', zh_name: 'JWT解析', icon: '🔍', parentId: 'dev-encode' }
      ],
      'server-encode': [
        { id: 'htpasswd-generator', name: 'Htpasswd生成器', en_name: 'Htpasswd Generator', zh_name: 'Htpasswd生成器', icon: '🔑', parentId: 'server-encode' }
      ]
    }
  },
  {
    category: '图片加工',
    en_category: 'Image Processing',
    zh_category: '图片加工',
    items: [
      { id: 'img-compress', name: '图片压缩', en_name: 'Image Compression', zh_name: '图片压缩', icon: '📉' },
      { id: 'img-resize', name: '图片调整大小', en_name: 'Image Resize', zh_name: '图片调整大小', icon: '🖼️' },
      { id: 'img-to-base64', name: '图片转Base64', en_name: 'Image to Base64', zh_name: '图片转Base64', icon: '🖼️' },
      { id: 'img-watermark', name: '图片添加水印', en_name: 'Add Watermark', zh_name: '图片添加水印', icon: '💧' },
      { id: 'img-rounded', name: '图片添加圆角', en_name: 'Add Rounded Corners', zh_name: '图片添加圆角', icon: '⭕' },
      { id: 'img-grayscale', name: '图片去色', en_name: 'Grayscale Image', zh_name: '图片去色', icon: '⚪' },
      { id: 'img-pixelate', name: '图片像素化', en_name: 'Pixelate Image', zh_name: '图片像素化', icon: '🔳' },
      { id: 'img-converter', name: '图片格式转换', en_name: 'Image Format Converter', zh_name: '图片格式转换', icon: '🔄' },
      { id: 'img-ocr', name: '图片OCR识别', en_name: 'Image OCR', zh_name: '图片OCR识别', icon: '👁️' },
      { id: 'img-slice', name: '多格切图', en_name: 'Image Slicer', zh_name: '多格切图', icon: '✂️' }
    ]
  },
  {
    category: '视频音频',
    en_category: 'Video & Audio',
    zh_category: '视频音频',
    items: [
      { id: 'audio-converter', name: '音频格式转换', en_name: 'Audio Converter', zh_name: '音频格式转换', icon: '🎵' },
      { id: 'video-converter', name: '视频格式转换', en_name: 'Video Converter', zh_name: '视频格式转换', icon: '🎬' },
      { id: 'video-to-audio', name: '视频提取音频', en_name: 'Extract Audio', zh_name: '视频提取音频', icon: '🎧' },
      { id: 'video-frame', name: '视频帧截图', en_name: 'Video Frame Capture', zh_name: '视频帧截图', icon: '📸' },
      { id: 'video-to-gif', name: '视频转GIF', en_name: 'Video to GIF', zh_name: '视频转GIF', icon: '🎞️' }
    ]
  }
]; 