import { Tool } from './types';

// 缓存键常量
const CACHE_KEYS = {
  TOOLS: 'cached_tools',
  FAVORITES: 'favorite_tools',
  RECENT: 'recent_tools',
  LAST_UPDATE: 'tools_last_update',
};

// 缓存过期时间（24小时）
const CACHE_EXPIRY = 24 * 60 * 60 * 1000;

// 检查缓存是否过期
const isCacheExpired = (timestamp: number) => {
  return Date.now() - timestamp > CACHE_EXPIRY;
};

// 获取缓存的工具数据
export const getCachedTools = () => {
  if (typeof window === 'undefined') return null;
  
  try {
    const lastUpdate = localStorage.getItem(CACHE_KEYS.LAST_UPDATE);
    if (!lastUpdate || isCacheExpired(parseInt(lastUpdate))) {
      return null;
    }
    
    const cachedTools = localStorage.getItem(CACHE_KEYS.TOOLS);
    return cachedTools ? JSON.parse(cachedTools) : null;
  } catch (error) {
    console.error('Error reading tools cache:', error);
    return null;
  }
};

// 缓存工具数据
export const cacheTools = (tools: Tool[]) => {
  if (typeof window === 'undefined') return;
  
  try {
    localStorage.setItem(CACHE_KEYS.TOOLS, JSON.stringify(tools));
    localStorage.setItem(CACHE_KEYS.LAST_UPDATE, Date.now().toString());
  } catch (error) {
    console.error('Error caching tools:', error);
  }
};

// 获取收藏的工具
export const getCachedFavorites = (): string[] => {
  if (typeof window === 'undefined') return [];
  
  try {
    const favorites = localStorage.getItem(CACHE_KEYS.FAVORITES);
    return favorites ? JSON.parse(favorites) : [];
  } catch (error) {
    console.error('Error reading favorites cache:', error);
    return [];
  }
};

// 更新收藏的工具
export const updateCachedFavorites = (toolId: string, isFavorite: boolean) => {
  if (typeof window === 'undefined') return;
  
  try {
    const favorites = getCachedFavorites();
    const newFavorites = isFavorite
      ? [...favorites, toolId]
      : favorites.filter(id => id !== toolId);
    
    localStorage.setItem(CACHE_KEYS.FAVORITES, JSON.stringify(newFavorites));
  } catch (error) {
    console.error('Error updating favorites cache:', error);
  }
};

// 获取最近使用的工具
export const getCachedRecentTools = (): string[] => {
  if (typeof window === 'undefined') return [];
  
  try {
    const recentTools = localStorage.getItem(CACHE_KEYS.RECENT);
    return recentTools ? JSON.parse(recentTools) : [];
  } catch (error) {
    console.error('Error reading recent tools cache:', error);
    return [];
  }
};

// 更新最近使用的工具
export const updateCachedRecentTools = (toolId: string) => {
  if (typeof window === 'undefined') return;
  
  try {
    const recentTools = getCachedRecentTools();
    const newRecentTools = [
      toolId,
      ...recentTools.filter(id => id !== toolId)
    ].slice(0, 10); // 只保留最近10个
    
    localStorage.setItem(CACHE_KEYS.RECENT, JSON.stringify(newRecentTools));
  } catch (error) {
    console.error('Error updating recent tools cache:', error);
  }
};

// 清除所有缓存
export const clearToolsCache = () => {
  if (typeof window === 'undefined') return;
  
  try {
    Object.values(CACHE_KEYS).forEach(key => {
      localStorage.removeItem(key);
    });
  } catch (error) {
    console.error('Error clearing tools cache:', error);
  }
}; 