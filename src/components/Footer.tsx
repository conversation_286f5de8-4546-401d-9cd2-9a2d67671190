"use client";

import Link from "next/link";
import { usePathname } from "next/navigation";
import { locales, localeNames } from "@/i18n/routing";
import { Github, Twitter, Linkedin } from "lucide-react";

interface FooterProps {
  footer: {
    copyright: string;
    about: {
      title: string;
      about: string;
      blog: string;
    };
    support: {
      title: string;
      helpCenter: string;
      contactUs: string;
    };
    legal: {
      title: string;
      privacy: string;
      terms: string;
    };
    language: {
      title: string;
      english: string;
      chinese: string;
      japanese: string;
      korean: string;
      french: string;
    };
    social: {
      title: string;
      github: string;
      twitter: string;
      linkedin: string;
    };
  };
}

export default function Footer({ footer }: FooterProps) {
  const pathname = usePathname();
  const currentLocale = pathname.split('/')[1];

  const switchLocale = (newLocale: string) => {
    const newPathname = pathname.replace(`/${currentLocale}`, `/${newLocale}`);
    window.location.href = newPathname;
  };

  return (
    <footer className="bg-background border-t border-border">
      <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
        <div className="grid gap-8 py-12 md:grid-cols-2 lg:grid-cols-4">
          {/* About Section */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium">{footer.about.title}</h3>
            <ul className="space-y-2">
              <li>
                <Link href={`/${currentLocale}/about`} className="text-sm text-muted-foreground hover:text-foreground transition-colors">
                  {footer.about.about}
                </Link>
              </li>
              <li>
                <Link href={`/${currentLocale}/blog`} className="text-sm text-muted-foreground hover:text-foreground transition-colors">
                  {footer.about.blog}
                </Link>
              </li>
            </ul>
          </div>

          {/* Support Section */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium">{footer.support.title}</h3>
            <ul className="space-y-2">
              <li>
                <Link href={`/${currentLocale}/help`} className="text-sm text-muted-foreground hover:text-foreground transition-colors">
                  {footer.support.helpCenter}
                </Link>
              </li>
              <li>
                <Link href={`/${currentLocale}/contact`} className="text-sm text-muted-foreground hover:text-foreground transition-colors">
                  {footer.support.contactUs}
                </Link>
              </li>
            </ul>
          </div>

          {/* Legal Section */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium">{footer.legal.title}</h3>
            <ul className="space-y-2">
              <li>
                <Link href={`/${currentLocale}/privacy`} className="text-sm text-muted-foreground hover:text-foreground transition-colors">
                  {footer.legal.privacy}
                </Link>
              </li>
              <li>
                <Link href={`/${currentLocale}/terms`} className="text-sm text-muted-foreground hover:text-foreground transition-colors">
                  {footer.legal.terms}
                </Link>
              </li>
            </ul>
          </div>

          {/* Language & Social Section */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium">{footer.language.title}</h3>
            <ul className="space-y-2">
              {locales.map((locale) => (
                <li key={locale}>
                  <button
                    onClick={() => switchLocale(locale)}
                    className={`text-sm hover:text-foreground transition-colors ${
                      locale === currentLocale ? 'font-bold text-foreground' : 'text-muted-foreground'
                    }`}
                  >
                    {localeNames[locale]}
                  </button>
                </li>
              ))}
            </ul>

            {/* <div className="pt-4">
              <h3 className="text-lg font-medium mb-2">{footer.social.title}</h3>
              <div className="flex space-x-4">
                <a
                  href="https://github.com"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-muted-foreground hover:text-foreground transition-colors"
                >
                  <Github size={20} />
                </a>
                <a
                  href="https://twitter.com"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-muted-foreground hover:text-foreground transition-colors"
                >
                  <Twitter size={20} />
                </a>
                <a
                  href="https://linkedin.com"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-muted-foreground hover:text-foreground transition-colors"
                >
                  <Linkedin size={20} />
                </a>
              </div>
            </div> */}
          </div>
        </div>

        <div className="border-t border-border py-6">
          <div className="flex flex-col items-center justify-between gap-4 md:flex-row">
            <p className="text-sm text-muted-foreground">
              &copy; {new Date().getFullYear()} {footer.copyright}
            </p>
          </div>
        </div>
      </div>
    </footer>
  );
}