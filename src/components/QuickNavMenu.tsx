import React from 'react';
import { Tool } from '@/lib/tools';
import { getLocalizedToolName, getLocalizedCategoryName } from '@/lib/tools';
import { sidebarMenu } from '@/lib/tools';

interface QuickNavMenuProps {
  isVisible: boolean;
  onToggle: () => void;
  categories: string[];
  tools: Tool[];
  currentLocale: string;
  filteredToolsByCategory: Record<string, Tool[]>;
  onCategoryClick: (category: string) => void;
  onToolClick: (toolId: string, event: React.MouseEvent) => void;
  hoveredCategory: string | null;
  setHoveredCategory: React.Dispatch<React.SetStateAction<string | null>>;
}

const QuickNavMenu: React.FC<QuickNavMenuProps> = ({
  isVisible,
  onToggle,
  categories,
  tools,
  currentLocale,
  filteredToolsByCategory,
  onCategoryClick,
  onToolClick,
  hoveredCategory,
  setHoveredCategory
}) => {
  return (
    <div className="fixed right-4 bottom-24 z-50">
      <div className="relative group">
        <button 
          className="w-12 h-12 rounded-full bg-primary text-primary-foreground flex items-center justify-center shadow-lg hover:shadow-xl transition-shadow group-hover:scale-110 transition-transform"
          onClick={onToggle}
          aria-label={currentLocale === 'en' ? 'Quick Navigation' : '快速导航'}
        >
          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
            <path d="m3 9 9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"></path>
            <polyline points="9 22 9 12 15 12 15 22"></polyline>
          </svg>
        </button>
        
        <div className={`${isVisible ? '' : 'hidden'} absolute bottom-full right-0 mb-2 p-2 bg-card shadow-lg rounded-lg border border-border w-56 max-h-[70vh] overflow-y-auto`} id="quick-nav-menu">
          <h3 className="text-sm font-medium mb-2 px-2">{currentLocale === 'en' ? 'Jump to Category' : '跳转到分类'}</h3>
          <div className="space-y-1">
            {categories.map((category) => {
              const menuCategory = sidebarMenu.find(c => c.category === category);
              const categoryName = menuCategory 
                ? getLocalizedCategoryName(menuCategory, currentLocale)
                : category;
              
              return (
                <div key={category} className="relative group">
                  <button
                    onMouseEnter={() => setHoveredCategory(category)}
                    onMouseLeave={() => setTimeout(() => setHoveredCategory(null), 100)}
                    onClick={() => onCategoryClick(category)}
                    className="w-full text-left text-sm px-3 py-1.5 rounded hover:bg-accent transition-colors flex items-center gap-2"
                  >
                    <span className="w-4 h-4 flex-shrink-0 flex items-center justify-center text-xs bg-muted rounded-full">🔍</span>
                    <span>{categoryName}</span>
                    <span className="ml-auto text-xs text-muted-foreground">{filteredToolsByCategory[category].length}</span>
                  </button>
                </div>
              );
            })}
          </div>
          <div className="my-2 border-t border-border"></div>
          <h3 className="text-sm font-medium mb-2 px-2">{currentLocale === 'en' ? 'Popular Tools' : '热门工具'}</h3>
          <div className="space-y-1">
            {Array.from(new Set([
              ...tools.filter(tool => tool.isFavorite).map(tool => tool.id),
              ...tools.map(tool => tool.id)
            ]))
              .slice(0, 7)
              .map(toolId => tools.find(tool => tool.id === toolId))
              .filter((tool): tool is Tool => tool !== undefined)
              .map((tool) => (
                <button
                  key={`hot-tool-${tool.id}`}
                  onClick={(e) => onToolClick(tool.id, e)}
                  className="w-full text-left text-sm px-3 py-1.5 rounded hover:bg-accent transition-colors flex items-center gap-2"
                >
                  <span className="w-5 h-5 flex-shrink-0 flex items-center justify-center bg-accent/60 rounded-md">{tool.icon}</span>
                  <span>{getLocalizedToolName(tool, currentLocale)}</span>
                </button>
              ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default QuickNavMenu; 