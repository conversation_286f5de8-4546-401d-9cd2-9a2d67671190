/* 基础样式重置 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: "SF Pro Text", -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>, "Helvetica Neue", Arial, sans-serif;
    color: #333;
    background-color: #f8f9fa;
    line-height: 1.6;
}

.container {
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

a {
    text-decoration: none;
    color: #3366ff;
    transition: all 0.3s ease;
}

a:hover {
    color: #1a4ad9;
}

ul {
    list-style: none;
}

button {
    cursor: pointer;
    border: none;
    outline: none;
    background: none;
}

/* 顶部导航栏 */
header {
    background-color: #fff;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    position: sticky;
    top: 0;
    z-index: 100;
}

header .container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 70px;
}

.logo {
    display: flex;
    align-items: center;
}

.logo i {
    font-size: 24px;
    color: #3366ff;
    margin-right: 10px;
}

.logo h1 {
    font-size: 22px;
    font-weight: 600;
    color: #333;
}

nav ul {
    display: flex;
    gap: 30px;
}

nav ul li a {
    color: #555;
    font-weight: 500;
    font-size: 15px;
    padding: 8px 0;
    position: relative;
}

nav ul li a:hover {
    color: #3366ff;
}

nav ul li a.active {
    color: #3366ff;
}

nav ul li a.active:after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 2px;
    background-color: #3366ff;
}

.user-actions {
    display: flex;
    align-items: center;
    gap: 20px;
}

.search-box {
    position: relative;
    width: 200px;
}

.search-box input {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #e0e0e0;
    border-radius: 5px;
    font-size: 14px;
    transition: all 0.3s ease;
}

.search-box input:focus {
    border-color: #3366ff;
    box-shadow: 0 0 0 2px rgba(51, 102, 255, 0.1);
    outline: none;
}

.search-box button {
    position: absolute;
    right: 10px;
    top: 50%;
    transform: translateY(-50%);
    color: #999;
    font-size: 14px;
}

.theme-toggle i,
.user-profile i {
    font-size: 20px;
    color: #555;
    cursor: pointer;
    transition: all 0.3s ease;
}

.theme-toggle i:hover,
.user-profile i:hover {
    color: #3366ff;
}

/* 英雄区域 */
.hero {
    background: linear-gradient(135deg, #3366ff 0%, #6699ff 100%);
    color: #fff;
    padding: 80px 0;
    text-align: center;
}

.hero-content h2 {
    font-size: 42px;
    font-weight: 700;
    margin-bottom: 20px;
}

.hero-content p {
    font-size: 18px;
    margin-bottom: 30px;
    opacity: 0.9;
}

.search-box-large {
    position: relative;
    width: 100%;
    max-width: 600px;
    margin: 0 auto 20px;
}

.search-box-large input {
    width: 100%;
    padding: 16px 20px;
    border: none;
    border-radius: 8px;
    font-size: 16px;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
}

.search-box-large button {
    position: absolute;
    right: 5px;
    top: 50%;
    transform: translateY(-50%);
    background-color: #3366ff;
    color: #fff;
    font-size: 16px;
    font-weight: 500;
    padding: 12px 20px;
    border-radius: 6px;
    transition: all 0.3s ease;
}

.search-box-large button:hover {
    background-color: #1a4ad9;
}

.popular-tags {
    color: #fff;
    font-size: 14px;
}

.popular-tags span {
    margin-right: 10px;
    opacity: 0.8;
}

.popular-tags a {
    color: #fff;
    margin: 0 8px;
    border: 1px solid rgba(255, 255, 255, 0.3);
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 13px;
    transition: all 0.3s ease;
}

.popular-tags a:hover {
    background-color: rgba(255, 255, 255, 0.2);
    border-color: transparent;
}

/* 工具分类区域 */
.tool-categories {
    padding: 60px 0;
}

.section-title {
    font-size: 28px;
    font-weight: 600;
    text-align: center;
    margin-bottom: 40px;
    color: #333;
    position: relative;
}

.section-title:after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 3px;
    background-color: #3366ff;
}

.category-tabs {
    display: flex;
    justify-content: center;
    gap: 15px;
    margin-bottom: 30px;
}

.tab {
    padding: 10px 20px;
    border-radius: 6px;
    font-size: 15px;
    font-weight: 500;
    color: #666;
    background-color: #f0f2f5;
    transition: all 0.3s ease;
}

.tab:hover {
    background-color: #e0e4ec;
}

.tab.active {
    background-color: #3366ff;
    color: #fff;
}

.tools-grid {
    display: none;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 25px;
    margin-top: 30px;
}

.tools-grid.active {
    display: grid;
}

.tool-card {
    background-color: #fff;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
    padding: 25px;
    text-align: center;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.tool-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
}

.tool-icon {
    background-color: #f0f7ff;
    width: 70px;
    height: 70px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 20px;
}

.tool-icon i {
    font-size: 28px;
    color: #3366ff;
}

.tool-card h3 {
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 12px;
    color: #333;
}

.tool-card p {
    font-size: 14px;
    color: #666;
    margin-bottom: 20px;
    line-height: 1.5;
}

.use-tool {
    display: inline-block;
    padding: 8px 20px;
    background-color: #3366ff;
    color: #fff;
    border-radius: 5px;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.use-tool:hover {
    background-color: #1a4ad9;
    color: #fff;
}

/* 最近使用 */
.recent-tools {
    background-color: #f0f7ff;
    padding: 60px 0;
}

.recent-tools-grid {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
}

.recent-tool {
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.05);
    padding: 15px;
    display: flex;
    align-items: center;
    gap: 15px;
    flex: 1;
    min-width: 200px;
    transition: transform 0.3s ease;
}

.recent-tool:hover {
    transform: translateY(-3px);
}

.recent-tool i {
    font-size: 20px;
    color: #3366ff;
}

.recent-tool span {
    font-size: 15px;
    font-weight: 500;
    color: #333;
}

.recent-tool small {
    margin-left: auto;
    color: #999;
    font-size: 12px;
}

/* 工具模态框 */
.tool-modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 1000;
    overflow-y: auto;
}

.tool-modal-content {
    position: relative;
    background-color: #fff;
    margin: 60px auto;
    width: 90%;
    max-width: 800px;
    border-radius: 10px;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.tool-modal-header {
    padding: 20px 25px;
    border-bottom: 1px solid #eee;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.tool-modal-header h2 {
    font-size: 22px;
    font-weight: 600;
}

.tool-modal-header h2 i {
    color: #3366ff;
    margin-right: 10px;
}

.close-modal {
    font-size: 28px;
    color: #999;
    cursor: pointer;
    transition: all 0.3s ease;
}

.close-modal:hover {
    color: #333;
}

.tool-modal-body {
    padding: 25px;
}

.tool-input {
    margin-bottom: 25px;
}

.tool-input label {
    display: block;
    font-size: 15px;
    font-weight: 500;
    margin-bottom: 10px;
    color: #555;
}

.input-group {
    display: flex;
    gap: 10px;
}

.input-group input {
    flex: 1;
    padding: 12px 15px;
    border: 1px solid #ddd;
    border-radius: 6px;
    font-size: 15px;
    transition: all 0.3s ease;
}

.input-group input:focus {
    border-color: #3366ff;
    box-shadow: 0 0 0 2px rgba(51, 102, 255, 0.1);
    outline: none;
}

.primary-button {
    background-color: #3366ff;
    color: #fff;
    padding: 12px 20px;
    border-radius: 6px;
    font-size: 15px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.primary-button:hover {
    background-color: #1a4ad9;
}

.secondary-button {
    background-color: #f0f2f5;
    color: #555;
    padding: 10px 15px;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.secondary-button:hover {
    background-color: #e0e4ec;
}

.tool-output {
    background-color: #f8f9fa;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 25px;
}

.tool-output h3 {
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 15px;
    color: #333;
}

.result-container {
    border: 1px solid #eee;
    border-radius: 6px;
    background-color: #fff;
    overflow: hidden;
}

.result-item {
    padding: 12px 15px;
    display: flex;
    border-bottom: 1px solid #eee;
}

.result-item:last-child {
    border-bottom: none;
}

.result-label {
    flex: 0 0 120px;
    font-weight: 500;
    color: #666;
}

.result-value {
    flex: 1;
    color: #333;
}

.tool-actions {
    display: flex;
    gap: 10px;
    justify-content: flex-end;
}

/* 页脚 */
footer {
    background-color: #2a3b55;
    color: #fff;
    padding: 60px 0 20px;
}

.footer-content {
    display: flex;
    flex-wrap: wrap;
    gap: 40px;
    margin-bottom: 40px;
}

.footer-logo {
    flex: 1;
    min-width: 250px;
}

.footer-logo i {
    font-size: 28px;
    color: #6699ff;
    margin-bottom: 15px;
}

.footer-logo h2 {
    font-size: 24px;
    font-weight: 600;
    margin-bottom: 10px;
}

.footer-logo p {
    font-size: 14px;
    opacity: 0.8;
}

.footer-links {
    display: flex;
    flex-wrap: wrap;
    gap: 40px;
    flex: 2;
}

.link-group {
    flex: 1;
    min-width: 150px;
}

.link-group h3 {
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 20px;
    color: #6699ff;
}

.link-group ul li {
    margin-bottom: 12px;
}

.link-group ul li a {
    color: rgba(255, 255, 255, 0.7);
    font-size: 14px;
    transition: all 0.3s ease;
}

.link-group ul li a:hover {
    color: #fff;
}

.footer-bottom {
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    padding-top: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 20px;
}

.footer-bottom p {
    font-size: 14px;
    opacity: 0.7;
}

.social-links {
    display: flex;
    gap: 15px;
}

.social-links a {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    background-color: rgba(255, 255, 255, 0.1);
    display: flex;
    align-items: center;
    justify-content: center;
    color: #fff;
    font-size: 16px;
    transition: all 0.3s ease;
}

.social-links a:hover {
    background-color: #6699ff;
    transform: translateY(-3px);
}

/* 响应式设计 */
@media (max-width: 992px) {
    .tools-grid {
        grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    }
    
    .hero-content h2 {
        font-size: 36px;
    }
    
    .hero-content p {
        font-size: 16px;
    }
}

@media (max-width: 768px) {
    header .container {
        height: auto;
        flex-direction: column;
        padding: 15px 20px;
        gap: 15px;
    }
    
    nav ul {
        gap: 20px;
    }
    
    .hero {
        padding: 60px 0;
    }
    
    .hero-content h2 {
        font-size: 32px;
    }
    
    .tool-modal-content {
        margin: 30px auto;
        width: 95%;
    }
    
    .footer-content {
        flex-direction: column;
        gap: 30px;
    }
}

@media (max-width: 576px) {
    nav ul {
        gap: 15px;
    }
    
    .user-actions {
        width: 100%;
        justify-content: center;
    }
    
    .search-box {
        width: 100%;
    }
    
    .hero-content h2 {
        font-size: 28px;
    }
    
    .search-box-large button {
        position: static;
        width: 100%;
        margin-top: 10px;
        transform: none;
    }
    
    .search-box-large input {
        padding: 14px 16px;
    }
    
    .category-tabs {
        flex-wrap: wrap;
    }
    
    .tab {
        padding: 8px 15px;
        font-size: 14px;
    }
    
    .tools-grid {
        grid-template-columns: 1fr;
    }
    
    .recent-tools-grid {
        flex-direction: column;
    }
    
    .input-group {
        flex-direction: column;
    }
    
    .tool-actions {
        flex-direction: column;
    }
    
    .footer-bottom {
        flex-direction: column;
        text-align: center;
    }
}

/* 深色模式 */
.dark-mode {
    background-color: #121212;
    color: #e0e0e0;
}

.dark-mode header,
.dark-mode .tool-card,
.dark-mode .recent-tool,
.dark-mode .tool-modal-content,
.dark-mode .result-container {
    background-color: #1e1e1e;
    color: #e0e0e0;
}

.dark-mode header {
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
}

.dark-mode .logo h1,
.dark-mode nav ul li a,
.dark-mode .theme-toggle i,
.dark-mode .user-profile i,
.dark-mode .tool-card h3,
.dark-mode .recent-tool span,
.dark-mode .tool-modal-header h2,
.dark-mode .tool-output h3 {
    color: #e0e0e0;
}

.dark-mode .search-box input,
.dark-mode .input-group input {
    background-color: #2a2a2a;
    border-color: #333;
    color: #e0e0e0;
}

.dark-mode .tab {
    background-color: #2a2a2a;
    color: #bbb;
}

.dark-mode .tab:hover {
    background-color: #333;
}

.dark-mode .tool-icon {
    background-color: #2a2a2a;
}

.dark-mode .tool-card p,
.dark-mode .result-label {
    color: #bbb;
}

.dark-mode .tool-output {
    background-color: #2a2a2a;
}

.dark-mode .result-item {
    border-bottom-color: #333;
}

.dark-mode .secondary-button {
    background-color: #2a2a2a;
    color: #bbb;
}

.dark-mode .secondary-button:hover {
    background-color: #333;
}

.dark-mode footer {
    background-color: #1a1a1a;
}

.dark-mode .footer-bottom {
    border-top-color: #333;
}
