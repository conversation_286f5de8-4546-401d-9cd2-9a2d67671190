<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ToolHub - 一站式工具聚合平台</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        :root {
            /* 主色调更新为更现代的色彩 */
            --primary-color: #4361ee; /* 更鲜明的蓝色 */
            --primary-light: #4895ef; /* 主色轻色调 */
            --primary-dark: #3a0ca3;  /* 主色深色调 */
            --secondary-color: #f8f9fa;
            --accent-color: #f72585; /* 醒目的强调色 */
            
            /* 中性色调系列 */
            --neutral-100: #ffffff;
            --neutral-200: #f8f9fa;
            --neutral-300: #e9ecef;
            --neutral-400: #dee2e6;
            --neutral-500: #adb5bd;
            --neutral-600: #6c757d;
            --neutral-700: #495057;
            --neutral-800: #343a40;
            --neutral-900: #212529;
            
            /* 功能色 */
            --success-color: #38b000;
            --warning-color: #ffaa00;
            --error-color: #d00000;
            
            /* 排版 */
            --font-family: 'PingFang SC', 'Microsoft YaHei', -apple-system, BlinkMacSystemFont, sans-serif;
            --heading-line-height: 1.3;
            --body-line-height: 1.6;
            
            /* 间距系统 */
            --spacing-xs: 4px;
            --spacing-sm: 8px;
            --spacing-md: 16px;
            --spacing-lg: 24px;
            --spacing-xl: 32px;
            --spacing-xxl: 48px;
            
            /* 圆角 */
            --radius-sm: 4px;
            --radius-md: 8px;
            --radius-lg: 12px;
            --radius-full: 9999px;
            
            /* 阴影 */
            --shadow-sm: 0 1px 3px rgba(0,0,0,0.1);
            --shadow-md: 0 4px 6px rgba(0,0,0,0.1);
            --shadow-lg: 0 10px 15px -3px rgba(0,0,0,0.1);
            
            /* 过渡 */
            --transition-fast: 0.2s ease;
            --transition-normal: 0.3s ease;
        }
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Segoe UI', 'PingFang SC', 'Microsoft YaHei', sans-serif;
        }
        
        body {
            background-color: #f5f7f9;
            color: var(--text-color);
            line-height: 1.6;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }
        
        /* 导航栏样式 */
        header {
            background-color: var(--neutral-100);
            box-shadow: var(--shadow-sm);
            position: sticky;
            top: 0;
            z-index: 100;
            padding: var(--spacing-sm) 0;
        }
        
        nav {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: var(--spacing-md) 0;
        }
        
        .logo {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--primary-color);
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
            text-decoration: none;
        }
        
        .logo i {
            margin-right: 10px;
        }
        
        .search-bar {
            flex-grow: 1;
            margin: 0 var(--spacing-lg);
            position: relative;
            max-width: 500px;
        }
        
        .search-bar input {
            width: 100%;
            padding: 10px var(--spacing-lg) 10px var(--spacing-xl);
            border-radius: var(--radius-full);
            border: 1px solid var(--neutral-400);
            background-color: var(--neutral-200);
            outline: none;
            font-size: 0.95rem;
            transition: all var(--transition-fast);
        }
        
        .search-bar input:focus {
            background-color: var(--neutral-100);
            border-color: var(--primary-light);
            box-shadow: 0 0 0 3px rgba(67, 97, 238, 0.15);
        }
        
        .search-bar i {
            position: absolute;
            left: var(--spacing-md);
            top: 50%;
            transform: translateY(-50%);
            color: var(--neutral-600);
        }
        
        .nav-links {
            display: flex;
            gap: var(--spacing-lg);
        }
        
        .nav-links a {
            text-decoration: none;
            color: var(--neutral-700);
            font-weight: 500;
            position: relative;
            padding: var(--spacing-sm) var(--spacing-sm);
            transition: color var(--transition-fast);
        }
        
        .nav-links a:hover {
            color: var(--primary-color);
        }
        
        .nav-links a::after {
            content: '';
            position: absolute;
            width: 0;
            height: 2px;
            bottom: 0;
            left: 50%;
            background-color: var(--primary-color);
            transition: all var(--transition-normal);
        }
        
        .nav-links a:hover::after {
            width: 100%;
            left: 0;
        }
        
        .user-actions {
            display: flex;
            align-items: center;
            gap: var(--spacing-md);
        }
        
        .user-actions button {
            background: none;
            border: none;
            cursor: pointer;
            padding: var(--spacing-sm);
            border-radius: var(--radius-md);
            color: var(--neutral-700);
            transition: all var(--transition-fast);
        }
        
        .user-actions button:hover {
            background-color: var(--neutral-200);
            color: var(--primary-color);
        }
        
        /* 英雄区域 */
        .hero {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
            color: white;
            padding: var(--spacing-xxl) 0;
            margin-bottom: var(--spacing-xl);
            position: relative;
            overflow: hidden;
        }
        
        .hero::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" width="100" height="100" viewBox="0 0 100 100"><rect width="100" height="100" fill="none"/><path d="M0,0 L100,100" stroke="rgba(255,255,255,0.1)" stroke-width="1" /><path d="M100,0 L0,100" stroke="rgba(255,255,255,0.1)" stroke-width="1" /></svg>');
            opacity: 0.3;
        }
        
        .hero-content {
            position: relative;
            z-index: 1;
            max-width: 700px;
            margin: 0 auto;
            text-align: center;
        }
        
        .hero h1 {
            font-size: 2.75rem;
            font-weight: 800;
            margin-bottom: var(--spacing-lg);
            line-height: var(--heading-line-height);
        }
        
        .hero p {
            font-size: 1.25rem;
            margin: 0 auto var(--spacing-xl);
            opacity: 0.9;
            line-height: var(--body-line-height);
        }
        
        .hero-actions {
            display: flex;
            gap: var(--spacing-md);
            justify-content: center;
        }
        
        .btn-hero-primary {
            background-color: white;
            color: var(--primary-dark);
            padding: var(--spacing-md) var(--spacing-xl);
            border-radius: var(--radius-md);
            font-weight: 600;
            transition: all var(--transition-normal);
            text-decoration: none;
        }
        
        .btn-hero-primary:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-md);
        }
        
        .btn-hero-secondary {
            background-color: rgba(255, 255, 255, 0.2);
            color: white;
            padding: var(--spacing-md) var(--spacing-xl);
            border-radius: var(--radius-md);
            font-weight: 600;
            transition: all var(--transition-normal);
            text-decoration: none;
        }
        
        .btn-hero-secondary:hover {
            background-color: rgba(255, 255, 255, 0.3);
        }
        
        /* 分类导航 */
        .categories {
            background-color: var(--neutral-100);
            border-radius: var(--radius-lg);
            box-shadow: var(--shadow-sm);
            padding: var(--spacing-xl);
            margin-bottom: var(--spacing-xl);
        }
        
        .section-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: var(--spacing-lg);
        }
        
        .section-header h2 {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--neutral-800);
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
        }
        
        .section-header h2 i {
            color: var(--primary-color);
        }
        
        .category-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(105px, 1fr));
            gap: var(--spacing-md);
        }
        
        .category-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            text-align: center;
            padding: var(--spacing-lg) var(--spacing-md);
            border-radius: var(--radius-md);
            transition: all var(--transition-normal);
            cursor: pointer;
            position: relative;
            overflow: hidden;
        }
        
        .category-item::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: var(--primary-color);
            opacity: 0;
            transition: opacity var(--transition-normal);
            z-index: 0;
        }
        
        .category-item:hover::before {
            opacity: 0.05;
        }
        
        .category-item i {
            font-size: 2.25rem;
            margin-bottom: var(--spacing-md);
            position: relative;
            z-index: 1;
            transition: all var(--transition-normal);
        }
        
        .category-item:hover i {
            transform: translateY(-5px);
            color: var(--primary-color);
        }
        
        .category-item span {
            font-weight: 500;
            position: relative;
            z-index: 1;
            transition: color var(--transition-normal);
        }
        
        .category-item:hover span {
            color: var(--primary-color);
        }
        
        /* 热门工具 */
        .popular-tools {
            margin-bottom: 40px;
        }
        
        .view-all {
            color: var(--primary-color);
            text-decoration: none;
            font-weight: 500;
            display: flex;
            align-items: center;
        }
        
        .view-all i {
            margin-left: 5px;
        }
        
        .tools-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
            gap: 20px;
        }
        
        .tool-card {
            background-color: var(--neutral-100);
            border-radius: var(--radius-lg);
            box-shadow: var(--shadow-sm);
            overflow: hidden;
            transition: transform var(--transition-normal), box-shadow var(--transition-normal);
            height: 100%;
            display: flex;
            flex-direction: column;
            border: 1px solid var(--neutral-300);
        }
        
        .tool-card:hover {
            transform: translateY(-4px);
            box-shadow: var(--shadow-lg);
        }
        
        .tool-image {
            position: relative;
            height: 180px;
            overflow: hidden;
        }
        
        .tool-image img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: transform var(--transition-normal);
        }
        
        .tool-card:hover .tool-image img {
            transform: scale(1.05);
        }
        
        .tool-category-badge {
            position: absolute;
            top: var(--spacing-md);
            left: var(--spacing-md);
            background-color: rgba(0, 0, 0, 0.6);
            color: white;
            padding: 4px var(--spacing-sm);
            border-radius: var(--radius-full);
            font-size: 0.75rem;
            font-weight: 600;
            z-index: 1;
        }
        
        .tool-content {
            padding: var(--spacing-lg);
            display: flex;
            flex-direction: column;
            flex-grow: 1;
        }
        
        .tool-title {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: var(--spacing-sm);
        }
        
        .tool-title h3 {
            margin: 0;
            font-size: 1.25rem;
            font-weight: 600;
            color: var(--neutral-800);
        }
        
        .favorite-btn {
            background: none;
            border: none;
            color: var(--neutral-500);
            cursor: pointer;
            font-size: 1.2rem;
            padding: var(--spacing-xs);
            margin: -8px;
            border-radius: var(--radius-full);
            transition: all var(--transition-fast);
        }
        
        .favorite-btn:hover {
            background-color: var(--neutral-200);
            color: var(--accent-color);
        }
        
        .favorite-btn.active {
            color: var(--accent-color);
        }
        
        .tool-description {
            color: var(--neutral-600);
            margin-bottom: var(--spacing-md);
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
            line-height: var(--body-line-height);
            flex-grow: 1;
        }
        
        .tool-meta {
            display: flex;
            justify-content: space-between;
            color: var(--neutral-600);
            font-size: 0.9rem;
            margin-bottom: var(--spacing-md);
        }
        
        .tool-rating {
            display: flex;
            align-items: center;
            gap: var(--spacing-xs);
        }
        
        .tool-rating i {
            color: var(--warning-color);
        }
        
        .tool-actions {
            display: flex;
            gap: var(--spacing-sm);
            margin-top: auto;
        }
        
        .btn {
            padding: var(--spacing-sm) var(--spacing-md);
            border-radius: var(--radius-md);
            text-decoration: none;
            font-weight: 500;
            cursor: pointer;
            border: none;
            transition: all var(--transition-fast);
            display: inline-flex;
            align-items: center;
            justify-content: center;
            gap: var(--spacing-xs);
        }
        
        .btn-primary {
            background-color: var(--primary-color);
            color: white;
            flex: 1;
        }
        
        .btn-primary:hover {
            background-color: var(--primary-dark);
        }
        
        .btn-outline {
            border: 1px solid var(--neutral-400);
            color: var(--neutral-700);
            background-color: transparent;
        }
        
        .btn-outline:hover {
            background-color: var(--neutral-200);
            border-color: var(--neutral-500);
        }
        
        /* 新上线工具 */
        .new-tools {
            margin-bottom: 40px;
        }
        
        /* 推荐工具集 */
        .tool-collections {
            margin-bottom: 40px;
        }
        
        .collection-card {
            background-color: white;
            border-radius: 10px;
            box-shadow: var(--card-shadow);
            padding: 20px;
            display: flex;
            align-items: center;
            margin-bottom: 20px;
            transition: transform 0.3s;
        }
        
        .collection-card:hover {
            transform: translateX(5px);
        }
        
        .collection-icon {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background-color: var(--secondary-color);
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 20px;
            flex-shrink: 0;
        }
        
        .collection-icon i {
            font-size: 1.5rem;
            color: var(--primary-color);
        }
        
        .collection-content {
            flex-grow: 1;
        }
        
        .collection-content h3 {
            margin-bottom: 5px;
        }
        
        .collection-content p {
            color: #666;
            margin-bottom: 10px;
        }
        
        .collection-meta {
            display: flex;
            align-items: center;
            gap: 15px;
            color: #777;
            font-size: 0.9rem;
        }
        
        .collection-meta span {
            display: flex;
            align-items: center;
        }
        
        .collection-meta i {
            margin-right: 5px;
        }
        
        /* 页脚 */
        footer {
            background-color: white;
            padding: 40px 0;
            border-top: 1px solid var(--border-color);
        }
        
        .footer-content {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 40px;
        }
        
        .footer-logo {
            font-size: 24px;
            font-weight: bold;
            color: var(--primary-color);
            margin-bottom: 15px;
            display: flex;
            align-items: center;
        }
        
        .footer-logo i {
            margin-right: 10px;
        }
        
        .footer-description {
            color: #666;
            margin-bottom: 20px;
        }
        
        .social-links {
            display: flex;
            gap: 15px;
        }
        
        .social-links a {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background-color: var(--secondary-color);
            color: var(--primary-color);
            display: flex;
            align-items: center;
            justify-content: center;
            text-decoration: none;
            transition: all 0.3s;
        }
        
        .social-links a:hover {
            background-color: var(--primary-color);
            color: white;
        }
        
        .footer-links h3 {
            margin-bottom: 20px;
            font-size: 1.1rem;
        }
        
        .footer-links ul {
            list-style: none;
        }
        
        .footer-links li {
            margin-bottom: 10px;
        }
        
        .footer-links a {
            text-decoration: none;
            color: #666;
            transition: color 0.3s;
        }
        
        .footer-links a:hover {
            color: var(--primary-color);
        }
        
        .copyright {
            text-align: center;
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid var(--border-color);
            color: #666;
        }
        
        /* 响应式设计 */
        @media (max-width: 992px) {
            .nav-toggle {
                display: block;
            }
            
            .nav-links {
                display: none;
                position: absolute;
                top: 100%;
                left: 0;
                right: 0;
                background-color: var(--neutral-100);
                padding: var(--spacing-md);
                flex-direction: column;
                box-shadow: var(--shadow-md);
            }
            
            .nav-links.active {
                display: flex;
            }
            
            .search-bar {
                max-width: unset;
            }
        }
        
        @media (max-width: 768px) {
            .hero h1 {
                font-size: 2.25rem;
            }
            
            .hero p {
                font-size: 1.1rem;
            }
            
            .tools-grid {
                grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
            }
            
            .category-grid {
                grid-template-columns: repeat(auto-fill, minmax(90px, 1fr));
            }
            
            .collection-card {
                padding: var(--spacing-md);
            }
        }
        
        @media (max-width: 576px) {
            .hero h1 {
                font-size: 1.75rem;
            }
            
            .hero p {
                font-size: 1rem;
            }
            
            .hero-actions {
                flex-direction: column;
                gap: var(--spacing-sm);
            }
            
            .tools-grid {
                grid-template-columns: 1fr;
            }
            
            .category-grid {
                grid-template-columns: repeat(auto-fill, minmax(80px, 1fr));
            }
            
            .collection-card {
                flex-direction: column;
                text-align: center;
            }
            
            .collection-icon {
                margin-bottom: var(--spacing-md);
            }
            
            .collection-meta {
                justify-content: center;
            }
        }
        
        .skeleton {
            background: linear-gradient(90deg, var(--neutral-200) 25%, var(--neutral-300) 37%, var(--neutral-200) 63%);
            background-size: 400% 100%;
            animation: skeleton-loading 1.4s ease infinite;
            border-radius: var(--radius-md);
        }
        
        @keyframes skeleton-loading {
            0% {
                background-position: 100% 50%;
            }
            100% {
                background-position: 0 50%;
            }
        }
    </style>
</head>
<body>
    <!-- 导航栏 -->
    <header>
        <div class="container">
            <nav>
                <a href="#" class="logo">
                    <i class="fas fa-tools"></i>
                    ToolHub
                </a>
                <div class="search-bar">
                    <input type="text" placeholder="搜索工具、分类或关键词...">
                    <i class="fas fa-search"></i>
                </div>
                <div class="nav-links">
                    <a href="#">首页</a>
                    <a href="#">分类</a>
                    <a href="#">排行榜</a>
                    <a href="#">工具集</a>
                    <a href="#">新上线</a>
                </div>
                <div class="user-actions">
                    <button><i class="far fa-bookmark"></i></button>
                    <button><i class="far fa-bell"></i></button>
                    <button><i class="far fa-user-circle"></i></button>
                </div>
            </nav>
        </div>
    </header>

    <!-- 主要内容 -->
    <main>
        <!-- 英雄区域 -->
        <section class="hero">
            <div class="container">
                <div class="hero-content">
                    <h1>一站式发现和使用优质在线工具</h1>
                    <p>ToolHub汇集上千种实用工具，让您的工作和生活更轻松高效。从设计、开发到办公、学习，总有一款工具适合您。</p>
                    <div class="hero-actions">
                        <a href="#popular" class="btn-hero-primary">
                            <i class="fas fa-fire"></i> 探索热门工具
                        </a>
                        <a href="#categories" class="btn-hero-secondary">
                            <i class="fas fa-th-large"></i> 浏览分类
                        </a>
                    </div>
                </div>
            </div>
        </section>

        <div class="container">
            <!-- 分类导航 -->
            <section class="categories">
                <h2><i class="fas fa-th-large"></i> 工具分类</h2>
                <div class="category-grid">
                    <div class="category-item">
                        <i class="fas fa-code"></i>
                        <span>开发</span>
                    </div>
                    <div class="category-item">
                        <i class="fas fa-paint-brush"></i>
                        <span>设计</span>
                    </div>
                    <div class="category-item">
                        <i class="fas fa-file-alt"></i>
                        <span>文档</span>
                    </div>
                    <div class="category-item">
                        <i class="fas fa-chart-line"></i>
                        <span>数据</span>
                    </div>
                    <div class="category-item">
                        <i class="fas fa-image"></i>
                        <span>图像</span>
                    </div>
                    <div class="category-item">
                        <i class="fas fa-video"></i>
                        <span>视频</span>
                    </div>
                    <div class="category-item">
                        <i class="fas fa-volume-up"></i>
                        <span>音频</span>
                    </div>
                    <div class="category-item">
                        <i class="fas fa-file-pdf"></i>
                        <span>PDF</span>
                    </div>
                    <div class="category-item">
                        <i class="fas fa-tasks"></i>
                        <span>效率</span>
                    </div>
                    <div class="category-item">
                        <i class="fas fa-magic"></i>
                        <span>AI工具</span>
                    </div>
                    <div class="category-item">
                        <i class="fas fa-shield-alt"></i>
                        <span>安全</span>
                    </div>
                    <div class="category-item">
                        <i class="fas fa-ellipsis-h"></i>
                        <span>更多</span>
                    </div>
                </div>
            </section>

            <!-- 热门工具 -->
            <section class="popular-tools">
                <div class="section-header">
                    <h2><i class="fas fa-fire"></i> 热门工具</h2>
                    <a href="#" class="view-all">查看全部 <i class="fas fa-angle-right"></i></a>
                </div>
                <div class="tools-grid">
                    <div class="tool-card">
                        <div class="tool-image">
                            <span class="tool-category-badge">设计</span>
                            <img src="https://via.placeholder.com/300x180?text=Canva" alt="Canva设计工具预览">
                        </div>
                        <div class="tool-content">
                            <div class="tool-title">
                                <h3>Canva设计</h3>
                                <button class="favorite-btn" aria-label="收藏此工具">
                                    <i class="far fa-heart"></i>
                                </button>
                            </div>
                            <p class="tool-description">免费在线设计工具，提供海量模板，轻松创建专业级设计作品。</p>
                            <div class="tool-meta">
                                <div class="tool-rating">
                                    <i class="fas fa-star"></i>
                                    <span>4.8</span>
                                    <span class="rating-count">(2.5k评价)</span>
                                </div>
                                <div class="tool-usage">
                                    <i class="fas fa-users"></i>
                                    <span>25.6k+用户</span>
                                </div>
                            </div>
                            <div class="tool-actions">
                                <a href="#" class="btn btn-primary">
                                    <i class="fas fa-external-link-alt"></i> 访问
                                </a>
                                <button class="btn btn-outline" aria-label="保存到收藏夹">
                                    <i class="far fa-bookmark"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                    
                    <div class="tool-card">
                        <div class="tool-image">
                            <img src="https://via.placeholder.com/300x160?text=Tool+2" alt="工具预览图">
                        </div>
                        <div class="tool-content">
                            <div class="tool-title">
                                <h3>ChatGPT</h3>
                                <button class="favorite-btn"><i class="far fa-heart"></i></button>
                            </div>
                            <p class="tool-description">强大的AI对话模型，可以回答问题、创作内容、提供建议和解决问题。</p>
                            <div class="tool-meta">
                                <div class="tool-rating">
                                    <i class="fas fa-star"></i>
                                    <span>4.9 (5.1k评价)</span>
                                </div>
                                <div class="tool-category">AI工具</div>
                            </div>
                            <div class="tool-actions">
                                <a href="#" class="btn btn-primary"><i class="fas fa-external-link-alt"></i> 访问</a>
                                <button class="btn btn-outline"><i class="far fa-bookmark"></i></button>
                            </div>
                        </div>
                    </div>
                    
                    <div class="tool-card">
                        <div class="tool-image">
                            <img src="https://via.placeholder.com/300x160?text=Tool+3" alt="工具预览图">
                        </div>
                        <div class="tool-content">
                            <div class="tool-title">
                                <h3>Notion</h3>
                                <button class="favorite-btn active"><i class="fas fa-heart"></i></button>
                            </div>
                            <p class="tool-description">一站式协作平台，集笔记、知识库、数据库和项目管理于一体。</p>
                            <div class="tool-meta">
                                <div class="tool-rating">
                                    <i class="fas fa-star"></i>
                                    <span>4.7 (3.2k评价)</span>
                                </div>
                                <div class="tool-category">效率</div>
                            </div>
                            <div class="tool-actions">
                                <a href="#" class="btn btn-primary"><i class="fas fa-external-link-alt"></i> 访问</a>
                                <button class="btn btn-outline"><i class="far fa-bookmark"></i></button>
                            </div>
                        </div>
                    </div>
                    
                    <div class="tool-card">
                        <div class="tool-image">
                            <img src="https://via.placeholder.com/300x160?text=Tool+4" alt="工具预览图">
                        </div>
                        <div class="tool-content">
                            <div class="tool-title">
                                <h3>Figma</h3>
                                <button class="favorite-btn"><i class="far fa-heart"></i></button>
                            </div>
                            <p class="tool-description">专业的在线界面设计工具，支持实时协作，是设计师的首选工具。</p>
                            <div class="tool-meta">
                                <div class="tool-rating">
                                    <i class="fas fa-star"></i>
                                    <span>4.8 (2.9k评价)</span>
                                </div>
                                <div class="tool-category">设计</div>
                            </div>
                            <div class="tool-actions">
                                <a href="#" class="btn btn-primary"><i class="fas fa-external-link-alt"></i> 访问</a>
                                <button class="btn btn-outline"><i class="far fa-bookmark"></i></button>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- 新上线工具 -->
            <section class="new-tools">
                <div class="section-header">
                    <h2><i class="fas fa-bolt"></i> 新上线工具</h2>
                    <a href="#" class="view-all">查看全部 <i class="fas fa-angle-right"></i></a>
                </div>
                <div class="tools-grid">
                    <div class="tool-card">
                        <div class="tool-image">
                            <img src="https://via.placeholder.com/300x160?text=New+Tool+1" alt="工具预览图">
                        </div>
                        <div class="tool-content">
                            <div class="tool-title">
                                <h3>Midjourney</h3>
                                <button class="favorite-btn"><i class="far fa-heart"></i></button>
                            </div>
                            <p class="tool-description">强大的AI图像生成工具，通过文字描述创建惊艳的艺术作品。</p>
                            <div class="tool-meta">
                                <div class="tool-rating">
                                    <i class="fas fa-star"></i>
                                    <span>4.9 (1.2k评价)</span>
                                </div>
                                <div class="tool-category">AI工具</div>
                            </div>
                            <div class="tool-actions">
                                <a href="#" class="btn btn-primary"><i class="fas fa-external-link-alt"></i> 访问</a>
                                <button class="btn btn-outline"><i class="far fa-bookmark"></i></button>
                            </div>
                        </div>
                    </div>
                    
                    <div class="tool-card">
                        <div class="tool-image">
                            <img src="https://via.placeholder.com/300x160?text=New+Tool+2" alt="工具预览图">
                        </div>
                        <div class="tool-content">
                            <div class="tool-title">
                                <h3>Loom</h3>
                                <button class="favorite-btn"><i class="far fa-heart"></i></button>
                            </div>
                            <p class="tool-description">简单高效的屏幕录制工具，一键分享视频，提升远程协作效率。</p>
                            <div class="tool-meta">
                                <div class="tool-rating">
                                    <i class="fas fa-star"></i>
                                    <span>4.6 (825评价)</span>
                                </div>
                                <div class="tool-category">视频</div>
                            </div>
                            <div class="tool-actions">
                                <a href="#" class="btn btn-primary"><i class="fas fa-external-link-alt"></i> 访问</a>
                                <button class="btn btn-outline"><i class="far fa-bookmark"></i></button>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- 推荐工具集 -->
            <section class="tool-collections">
                <div class="section-header">
                    <h2><i class="fas fa-layer-group"></i> 推荐工具集</h2>
                    <a href="#" class="view-all">查看全部 <i class="fas fa-angle-right"></i></a>
                </div>
                
                <div class="collection-card">
                    <div class="collection-icon">
                        <i class="fas fa-laptop-code"></i>
                    </div>
                    <div class="collection-content">
                        <h3>前端开发者必备工具</h3>
                        <p>收集了前端开发中最常用的20个优质工具，帮助提升开发效率和代码质量。</p>
                        <div class="collection-meta">
                            <span><i class="fas fa-tools"></i> 20个工具</span>
                            <span><i class="fas fa-eye"></i> 5.2k浏览</span>
                            <span><i class="fas fa-bookmark"></i> 1.3k收藏</span>
                        </div>
                    </div>
                </div>
                
                <div class="collection-card">
                    <div class="collection-icon">
                        <i class="fas fa-image"></i>
                    </div>
                    <div class="collection-content">
                        <h3>内容创作者图像工具箱</h3>
                        <p>15个强大的图像处理工具，满足内容创作者从编辑、优化到分享的全流程需求。</p>
                        <div class="collection-meta">
                            <span><i class="fas fa-tools"></i> 15个工具</span>
                            <span><i class="fas fa-eye"></i> 3.8k浏览</span>
                            <span><i class="fas fa-bookmark"></i> 950收藏</span>
                        </div>
                    </div>
                </div>
            </section>
        </div>
    </main>

    <!-- 页脚 -->
    <footer>
        <div class="container">
            <div class="footer-content">
                <div class="footer-about">
                    <div class="footer-logo">
                        <i class="fas fa-tools"></i>
                        ToolHub
                    </div>
                    <p class="footer-description">
                        ToolHub致力于汇集和推荐互联网上最优质的在线工具，帮助用户高效完成工作和创作。
                    </p>
                    <div class="social-links">
                        <a href="#"><i class="fab fa-weixin"></i></a>
                        <a href="#"><i class="fab fa-weibo"></i></a>
                        <a href="#"><i class="fab fa-github"></i></a>
                        <a href="#"><i class="fab fa-zhihu"></i></a>
                    </div>
                </div>
                
                <div class="footer-links">
                    <h3>功能导航</h3>
                    <ul>
                        <li><a href="#">工具分类</a></li>
                        <li><a href="#">热门工具</a></li>
                        <li><a href="#">新上线</a></li>
                        <li><a href="#">工具集</a></li>
                        <li><a href="#">排行榜</a></li>
                    </ul>
                </div>
                
                <div class="footer-links">
                    <h3>帮助中心</h3>
                    <ul>
                        <li><a href="#">关于我们</a></li>
                        <li><a href="#">使用指南</a></li>
                        <li><a href="#">提交工具</a></li>
                        <li><a href="#">反馈建议</a></li>
                        <li><a href="#">联系我们</a></li>
                    </ul>
                </div>
                
                <div class="footer-links">
                    <h3>法律信息</h3>
                    <ul>
                        <li><a href="#">隐私政策</a></li>
                        <li><a href="#">用户协议</a></li>
                        <li><a href="#">版权声明</a></li>
                    </ul>
                </div>
            </div>
            
            <div class="copyright">
                <p>© 2023 ToolHub. 保留所有权利。</p>
            </div>
        </div>
    </footer>
</body>
</html>

