document.addEventListener('DOMContentLoaded', function() {
    // 工具类别切换
    const categoryTabs = document.querySelectorAll('.tab');
    const toolsGrids = document.querySelectorAll('.tools-grid');
    
    categoryTabs.forEach(tab => {
        tab.addEventListener('click', function() {
            // 移除所有 tab 的激活状态
            categoryTabs.forEach(t => t.classList.remove('active'));
            
            // 添加当前 tab 的激活状态
            this.classList.add('active');
            
            // 获取目标分类
            const targetCategory = this.getAttribute('data-category');
            
            // 隐藏所有工具网格
            toolsGrids.forEach(grid => grid.classList.remove('active'));
            
            // 显示对应的工具网格
            document.getElementById(`${targetCategory}-tools`).classList.add('active');
        });
    });
    
    // 工具卡点击打开模态框
    const toolCards = document.querySelectorAll('.tool-card');
    const toolModal = document.getElementById('tool-modal');
    const closeModal = document.querySelector('.close-modal');
    
    toolCards.forEach(card => {
        card.querySelector('.use-tool').addEventListener('click', function(e) {
            e.preventDefault();
            
            // 获取工具卡信息
            const toolName = card.querySelector('h3').textContent;
            const toolIcon = card.querySelector('.tool-icon i').className;
            
            // 更新模态框内容
            document.querySelector('.tool-modal-header h2').innerHTML = `<i class="${toolIcon}"></i> ${toolName}`;
            
            // 显示模态框
            toolModal.style.display = 'block';
            document.body.style.overflow = 'hidden'; // 防止背景滚动
        });
    });
    
    // 关闭模态框
    closeModal.addEventListener('click', function() {
        toolModal.style.display = 'none';
        document.body.style.overflow = 'auto';
    });
    
    // 点击模态框外部关闭
    window.addEventListener('click', function(e) {
        if (e.target === toolModal) {
            toolModal.style.display = 'none';
            document.body.style.overflow = 'auto';
        }
    });
    
    // 深色模式切换
    const themeToggle = document.querySelector('.theme-toggle');
    
    themeToggle.addEventListener('click', function() {
        document.body.classList.toggle('dark-mode');
        
        // 切换图标
        const icon = this.querySelector('i');
        if (icon.classList.contains('fa-moon')) {
            icon.classList.remove('fa-moon');
            icon.classList.add('fa-sun');
        } else {
            icon.classList.remove('fa-sun');
            icon.classList.add('fa-moon');
        }
    });
    
    // 搜索功能
    const searchBoxes = document.querySelectorAll('.search-box input, .search-box-large input');
    const toolTitles = Array.from(toolCards).map(card => card.querySelector('h3').textContent.toLowerCase());
    
    searchBoxes.forEach(searchBox => {
        searchBox.addEventListener('keyup', function(e) {
            if (e.key === 'Enter') {
                const searchTerm = this.value.toLowerCase().trim();
                
                if (searchTerm) {
                    // 简单搜索实现
                    const foundTools = toolTitles.filter(title => title.includes(searchTerm));
                    
                    if (foundTools.length > 0) {
                        // 找到匹配的工具，可以高亮显示或打开第一个匹配的工具
                        highlightTools(foundTools);
                    } else {
                        // 未找到匹配的工具
                        alert('未找到匹配的工具，请尝试其他关键词');
                    }
                }
            }
        });
    });
    
    // 高亮显示匹配的工具
    function highlightTools(matchedTools) {
        toolCards.forEach(card => {
            const title = card.querySelector('h3').textContent.toLowerCase();
            
            // 重置所有卡片样式
            card.style.transform = '';
            card.style.boxShadow = '';
            
            // 高亮匹配的卡片
            if (matchedTools.includes(title)) {
                // 滚动到第一个匹配的工具卡
                if (title === matchedTools[0]) {
                    card.scrollIntoView({ behavior: 'smooth', block: 'center' });
                }
                
                // 添加高亮效果
                card.style.transform = 'translateY(-5px)';
                card.style.boxShadow = '0 8px 25px rgba(51, 102, 255, 0.3)';
                
                // 动画效果
                setTimeout(() => {
                    card.style.transform = '';
                    card.style.boxShadow = '';
                }, 2000);
            }
        });
    }
    
    // 记录最近使用的工具（简化版，实际应用中可使用localStorage）
    const recentTools = document.querySelectorAll('.recent-tool');
    
    toolCards.forEach((card, index) => {
        card.querySelector('.use-tool').addEventListener('click', function() {
            if (index < recentTools.length) {
                // 更新最近使用的工具
                const toolName = card.querySelector('h3').textContent;
                const toolIcon = card.querySelector('.tool-icon i').className;
                
                recentTools[0].querySelector('i').className = toolIcon;
                recentTools[0].querySelector('span').textContent = toolName;
                recentTools[0].querySelector('small').textContent = '刚刚';
                
                // 更新其他最近工具的时间
                const times = ['2分钟前', '5分钟前', '昨天'];
                for (let i = 1; i < recentTools.length; i++) {
                    recentTools[i].querySelector('small').textContent = times[i-1];
                }
            }
        });
    });
});
