<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ToolHub - 专业工具聚合平台</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        :root {
            --primary-color: #4361ee;
            --primary-light: #4895ef;
            --secondary-color: #3f37c9;
            --accent-color: #4cc9f0;
            --text-primary: #333;
            --text-secondary: #666;
            --text-light: #999;
            --bg-primary: #fff;
            --bg-secondary: #f8f9fa;
            --bg-tertiary: #edf2fb;
            --card-bg: #fff;
            --border-color: #e0e0e0;
            --border-radius-sm: 6px;
            --border-radius: 10px;
            --border-radius-lg: 16px;
            --shadow-sm: 0 2px 8px rgba(0, 0, 0, 0.05);
            --shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
            --shadow-lg: 0 8px 24px rgba(0, 0, 0, 0.12);
            --transition: all 0.3s ease;
            --header-height: 64px;
            --sidebar-width: 260px;
            --font-sans: 'SF Pro Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
            --font-mono: 'SF Mono', SFMono-Regular, Consolas, 'Liberation Mono', Menlo, Courier, monospace;
        }
        
        .dark-mode {
            --primary-color: #4cc9f0;
            --primary-light: #4895ef;
            --secondary-color: #3f37c9;
            --accent-color: #7209b7;
            --text-primary: #f1f1f1;
            --text-secondary: #c5c5c5;
            --text-light: #888;
            --bg-primary: #121212;
            --bg-secondary: #1e1e1e;
            --bg-tertiary: #2a2a2a;
            --card-bg: #2d2d2d;
            --border-color: #444;
            --shadow-sm: 0 2px 8px rgba(0, 0, 0, 0.2);
            --shadow: 0 4px 12px rgba(0, 0, 0, 0.25);
            --shadow-lg: 0 8px 24px rgba(0, 0, 0, 0.35);
        }
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: var(--font-sans);
        }
        
        body {
            background-color: var(--bg-primary);
            color: var(--text-primary);
            transition: var(--transition);
            overflow: hidden;
            line-height: 1.5;
            font-size: 16px;
        }
        
        .container {
            display: flex;
            height: 100vh;
            overflow: hidden;
        }
        
        /* 顶部导航栏 */
        .header {
            position: fixed;
            top: 0;
            left: var(--sidebar-width);
            right: 0;
            height: var(--header-height);
            background-color: var(--bg-primary);
            border-bottom: 1px solid var(--border-color);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 24px;
            z-index: 100;
            transition: var(--transition);
            backdrop-filter: blur(8px);
            -webkit-backdrop-filter: blur(8px);
            background-color: rgba(var(--bg-primary-rgb), 0.85);
        }
        
        .search-container {
            flex: 1;
            max-width: 600px;
            position: relative;
            margin-right: 24px;
        }
        
        .search-bar {
            width: 100%;
            height: 44px;
            padding: 0 16px 0 44px;
            border-radius: var(--border-radius);
            border: 1px solid var(--border-color);
            background-color: var(--bg-secondary);
            color: var(--text-primary);
            font-size: 15px;
            transition: var(--transition);
        }
        
        .search-bar:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(var(--primary-color-rgb), 0.15);
        }
        
        .search-icon {
            position: absolute;
            left: 14px;
            top: 50%;
            transform: translateY(-50%);
            color: var(--text-secondary);
            font-size: 18px;
        }
        
        .user-nav {
            display: flex;
            align-items: center;
            gap: 16px;
        }
        
        .nav-button {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background-color: var(--bg-secondary);
            color: var(--text-primary);
            display: flex;
            align-items: center;
            justify-content: center;
            border: none;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
        }
        
        .nav-button:hover {
            background-color: var(--bg-tertiary);
        }
        
        .badge {
            position: absolute;
            top: -4px;
            right: -4px;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background-color: var(--primary-color);
            color: white;
            font-size: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
        }
        
        .user-info {
            display: flex;
            align-items: center;
            gap: 8px;
            cursor: pointer;
            padding: 4px;
            border-radius: var(--border-radius);
            transition: var(--transition);
        }
        
        .user-info:hover {
            background-color: var(--bg-secondary);
        }
        
        .avatar {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background-color: var(--primary-light);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            font-size: 16px;
        }
        
        .user-name {
            font-size: 14px;
            font-weight: 500;
            color: var(--text-primary);
        }
        
        /* 侧边栏样式 */
        .sidebar {
            width: var(--sidebar-width);
            height: 100vh;
            background-color: var(--bg-primary);
            border-right: 1px solid var(--border-color);
            padding: 16px 0;
            overflow-y: auto;
            transition: var(--transition);
            position: fixed;
            left: 0;
            top: 0;
            z-index: 200;
        }
        
        .sidebar-header {
            padding: 0 20px 16px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            border-bottom: 1px solid var(--border-color);
            margin-bottom: 16px;
        }
        
        .logo {
            display: flex;
            align-items: center;
            text-decoration: none;
        }
        
        .logo-icon {
            width: 32px;
            height: 32px;
            border-radius: var(--border-radius-sm);
            background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 18px;
        }
        
        .logo-text {
            font-size: 20px;
            font-weight: 700;
            margin-left: 10px;
            background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
            -webkit-background-clip: text;
            background-clip: text;
            -webkit-text-fill-color: transparent;
            letter-spacing: -0.5px;
        }
        
        .sidebar-toggle {
            width: 32px;
            height: 32px;
            border-radius: var(--border-radius-sm);
            background-color: var(--bg-secondary);
            border: none;
            color: var(--text-secondary);
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        
        .sidebar-toggle:hover {
            background-color: var(--bg-tertiary);
            color: var(--text-primary);
        }
        
        .sidebar-section {
            margin-bottom: 16px;
            padding: 0 12px;
        }
        
        .section-title {
            padding: 0 8px;
            margin-bottom: 8px;
            color: var(--text-light);
            font-size: 12px;
            text-transform: uppercase;
            letter-spacing: 1px;
            font-weight: 600;
        }
        
        .sidebar-menu {
            list-style: none;
        }
        
        .sidebar-menu li {
            margin-bottom: 2px;
        }
        
        .sidebar-menu a {
            display: flex;
            align-items: center;
            padding: 10px 12px;
            color: var(--text-primary);
            text-decoration: none;
            transition: var(--transition);
            border-radius: var(--border-radius-sm);
            font-weight: 500;
        }
        
        .sidebar-menu a:hover {
            background-color: var(--bg-secondary);
        }
        
        .sidebar-menu a.active {
            background-color: rgba(var(--primary-color-rgb), 0.1);
            color: var(--primary-color);
        }
        
        .sidebar-menu i {
            margin-right: 12px;
            font-size: 18px;
            width: 24px;
            text-align: center;
            color: var(--text-secondary);
        }
        
        .sidebar-menu a:hover i {
            color: var(--text-primary);
        }
        
        .sidebar-menu a.active i {
            color: var(--primary-color);
        }
        
        .submenu-toggle {
            margin-left: auto;
            color: var(--text-light);
            transition: var(--transition);
        }
        
        .sidebar-menu a:hover .submenu-toggle {
            color: var(--text-primary);
        }
        
        /* 主内容区域样式 */
        .main-content {
            flex: 1;
            padding: 24px;
            margin-left: var(--sidebar-width);
            margin-top: var(--header-height);
            overflow-y: auto;
            height: calc(100vh - var(--header-height));
            transition: var(--transition);
        }
        
        .page-title {
            font-size: 24px;
            font-weight: 700;
            margin-bottom: 24px;
            color: var(--text-primary);
        }
        
        .section {
            margin-bottom: 32px;
        }
        
        .section-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 16px;
        }
        
        .section-title {
            font-size: 18px;
            font-weight: 600;
            color: var(--text-primary);
        }
        
        .section-action {
            font-size: 14px;
            color: var(--primary-color);
            text-decoration: none;
            display: flex;
            align-items: center;
        }
        
        .section-action i {
            margin-left: 6px;
            font-size: 12px;
        }
        
        /* 快捷工具 */
        .quick-tools {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
            gap: 16px;
            margin-bottom: 32px;
        }
        
        .quick-tool {
            background-color: var(--card-bg);
            border-radius: var(--border-radius);
            padding: 20px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            text-align: center;
            cursor: pointer;
            transition: var(--transition);
            border: 1px solid var(--border-color);
        }
        
        .quick-tool:hover {
            transform: translateY(-3px);
            box-shadow: var(--shadow);
            border-color: var(--primary-light);
        }
        
        .quick-tool-icon {
            width: 48px;
            height: 48px;
            border-radius: var(--border-radius-sm);
            background-color: rgba(var(--primary-color-rgb), 0.1);
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 12px;
        }
        
        .quick-tool-icon i {
            font-size: 24px;
            color: var(--primary-color);
        }
        
        .quick-tool-title {
            font-size: 15px;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 8px;
        }
        
        .quick-tool-description {
            font-size: 13px;
            color: var(--text-secondary);
        }
        
        /* 最近使用 */
        .recent-tools {
            display: flex;
            gap: 16px;
            overflow-x: auto;
            padding-bottom: 12px;
            margin-bottom: 32px;
            scrollbar-width: thin;
        }
        
        .recent-tools::-webkit-scrollbar {
            height: 6px;
        }
        
        .recent-tools::-webkit-scrollbar-track {
            background: var(--bg-secondary);
            border-radius: 10px;
        }
        
        .recent-tools::-webkit-scrollbar-thumb {
            background: var(--border-color);
            border-radius: 10px;
        }
        
        .recent-tools::-webkit-scrollbar-thumb:hover {
            background: var(--text-light);
        }
        
        .recent-tool {
            flex: 0 0 280px;
            background-color: var(--card-bg);
            border-radius: var(--border-radius);
            border: 1px solid var(--border-color);
            padding: 16px;
            transition: var(--transition);
            cursor: pointer;
        }
        
        .recent-tool:hover {
            transform: translateY(-3px);
            box-shadow: var(--shadow);
            border-color: var(--primary-light);
        }
        
        .recent-tool-header {
            display: flex;
            align-items: center;
            margin-bottom: 12px;
        }
        
        .recent-tool-icon {
            width: 36px;
            height: 36px;
            border-radius: var(--border-radius-sm);
            background-color: rgba(var(--primary-color-rgb), 0.1);
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 12px;
        }
        
        .recent-tool-icon i {
            font-size: 18px;
            color: var(--primary-color);
        }
        
        .recent-tool-info {
            flex: 1;
        }
        
        .recent-tool-title {
            font-size: 15px;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 4px;
        }
        
        .recent-tool-time {
            font-size: 12px;
            color: var(--text-light);
        }
        
        .recent-tool-status {
            margin-left: auto;
            font-size: 20px;
            color: var(--text-light);
            cursor: pointer;
            transition: var(--transition);
        }
        
        .recent-tool-status:hover {
            color: var(--primary-color);
        }
        
        .recent-tool-progress {
            height: 4px;
            width: 100%;
            background-color: var(--bg-secondary);
            border-radius: 2px;
            overflow: hidden;
            margin-top: 12px;
        }
        
        .progress-bar {
            height: 100%;
            background: linear-gradient(90deg, var(--primary-color), var(--accent-color));
            border-radius: 2px;
        }
        
        /* 工具卡片网格 */
        .tools-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
            gap: 20px;
            margin-bottom: 40px;
        }
        
        .tool-card {
            background-color: var(--card-bg);
            border-radius: var(--border-radius);
            border: 1px solid var(--border-color);
            padding: 20px;
            transition: var(--transition);
            cursor: pointer;
            position: relative;
            overflow: hidden;
            display: flex;
            flex-direction: column;
            height: 100%;
        }
        
        .tool-card:hover {
            transform: translateY(-4px);
            box-shadow: var(--shadow);
            border-color: var(--primary-light);
        }
        
        .tool-card:before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 4px;
            height: 100%;
            background: linear-gradient(to bottom, var(--primary-color), var(--accent-color));
            opacity: 0;
            transition: var(--transition);
        }
        
        .tool-card:hover:before {
            opacity: 1;
        }
        
        .tool-header {
            display: flex;
            justify-content: space-between;
            margin-bottom: 16px;
        }
        
        .tool-icon {
            width: 48px;
            height: 48px;
            border-radius: var(--border-radius-sm);
            background-color: rgba(var(--primary-color-rgb), 0.1);
            display: flex;
            align-items: center;
            justify-content: center;
            transition: var(--transition);
        }
        
        .tool-card:hover .tool-icon {
            background-color: rgba(var(--primary-color-rgb), 0.2);
        }
        
        .tool-icon i {
            font-size: 24px;
            color: var(--primary-color);
        }
        
        .tool-favorite {
            background: none;
            border: none;
            color: var(--text-light);
            cursor: pointer;
            transition: var(--transition);
            width: 32px;
            height: 32px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
        }
        
        .tool-favorite:hover {
            background-color: var(--bg-secondary);
            color: #ff9e3d;
        }
        
        .tool-favorite.active {
            color: #ff9e3d;
        }
        
        .tool-title {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--text-primary);
        }
        
        .tool-description {
            font-size: 14px;
            color: var(--text-secondary);
            margin-bottom: 16px;
            flex-grow: 1;
            line-height: 1.6;
        }
        
        .tool-tags {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
            margin-top: auto;
        }
        
        .tool-tag {
            font-size: 12px;
            padding: 4px 12px;
            border-radius: 16px;
            background-color: var(--bg-secondary);
            color: var(--text-secondary);
            transition: var(--transition);
        }
        
        .tool-card:hover .tool-tag {
            background-color: rgba(var(--primary-color-rgb), 0.1);
            color: var(--primary-color);
        }
        
        /* 工具详情页 */
        .tool-detail {
            display: none;
            background-color: var(--bg-primary);
            border-radius: var(--border-radius-lg);
            border: 1px solid var(--border-color);
            box-shadow: var(--shadow-lg);
            margin-top: 20px;
            overflow: hidden;
            animation: slideUp 0.3s ease;
        }
        
        @keyframes slideUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        .tool-detail-header {
            padding: 20px 24px;
            border-bottom: 1px solid var(--border-color);
            display: flex;
            align-items: center;
            justify-content: space-between;
            background-color: var(--bg-secondary);
        }
        
        .tool-detail-title {
            display: flex;
            align-items: center;
        }
        
        .tool-detail-title-icon {
            width: 40px;
            height: 40px;
            border-radius: var(--border-radius-sm);
            background-color: rgba(var(--primary-color-rgb), 0.1);
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 16px;
        }
        
        .tool-detail-title-icon i {
            font-size: 20px;
            color: var(--primary-color);
        }
        
        .tool-detail-title-text h2 {
            font-size: 20px;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 4px;
        }
        
        .tool-detail-title-text p {
            font-size: 13px;
            color: var(--text-secondary);
        }
        
        .tool-actions {
            display: flex;
            gap: 12px;
        }
        
        .tool-action-btn {
            height: 36px;
            padding: 0 16px;
            border-radius: var(--border-radius-sm);
            background-color: var(--bg-primary);
            border: 1px solid var(--border-color);
            display: flex;
            align-items: center;
            color: var(--text-primary);
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: var(--transition);
        }
        
        .tool-action-btn:hover {
            background-color: var(--bg-tertiary);
        }
        
        .tool-action-btn i {
            margin-right: 8px;
            font-size: 16px;
        }
        
        .tool-detail-content {
            padding: 24px;
        }
        
        .tool-interface {
            background-color: var(--bg-secondary);
            border-radius: var(--border-radius);
            padding: 24px;
            margin-bottom: 24px;
        }
        
        .tool-interface-title {
            font-size: 16px;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 16px;
            display: flex;
            align-items: center;
        }
        
        .tool-interface-title i {
            margin-right: 8px;
            color: var(--primary-color);
        }
        
        .tool-interface-inputs {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 16px;
            margin-bottom: 24px;
        }
        
        .input-group {
            display: flex;
            flex-direction: column;
        }
        
        .input-group label {
            margin-bottom: 8px;
            font-size: 14px;
            color: var(--text-secondary);
            font-weight: 500;
        }
        
        .input-group input, 
        .input-group select, 
        .input-group textarea {
            padding: 12px 16px;
            border: 1px solid var(--border-color);
            border-radius: var(--border-radius-sm);
            background-color: var(--card-bg);
            color: var(--text-primary);
            font-size: 15px;
            transition: var(--transition);
        }
        
        .input-group input:focus, 
        .input-group select:focus, 
        .input-group textarea:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(var(--primary-color-rgb), 0.1);
        }
        
        .input-group textarea {
            resize: vertical;
            min-height: 120px;
        }
        
        .input-hint {
            margin-top: 6px;
            font-size: 12px;
            color: var(--text-light);
        }
        
        .tool-interface-buttons {
            display: flex;
            gap: 12px;
        }
        
        .btn {
            height: 40px;
            padding: 0 20px;
            border-radius: var(--border-radius-sm);
            font-size: 15px;
            font-weight: 500;
            cursor: pointer;
            transition: var(--transition);
            display: flex;
            align-items: center;
            justify-content: center;
            border: none;
        }
        
        .btn i {
            margin-right: 8px;
        }
        
        .btn-primary {
            background-color: var(--primary-color);
            color: white;
        }
        
        .btn-primary:hover {
            background-color: var(--secondary-color);
        }
        
        .btn-secondary {
            background-color: var(--bg-tertiary);
            color: var(--text-primary);
            border: 1px solid var(--border-color);
        }
        
        .btn-secondary:hover {
            background-color: var(--bg-secondary);
        }
        
        .tool-result {
            background-color: var(--card-bg);
            border-radius: var(--border-radius);
            padding: 0;
            overflow: hidden;
            border: 1px solid var(--border-color);
        }
        
        .tool-result-header {
            padding: 12px 16px;
            background-color: var(--bg-secondary);
            border-bottom: 1px solid var(--border-color);
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
        
        .tool-result-title {
            font-size: 15px;
            font-weight: 600;
            color: var(--text-primary);
            display: flex;
            align-items: center;
        }
        
        .tool-result-title i {
            margin-right: 8px;
            color: var(--primary-color);
        }
        
        .tool-result-actions {
            display: flex;
            gap: 8px;
        }
        
        .result-action {
            width: 32px;
            height: 32px;
            border-radius: var(--border-radius-sm);
            display: flex;
            align-items: center;
            justify-content: center;
            background-color: var(--bg-primary);
            color: var(--text-secondary);
            border: 1px solid var(--border-color);
            cursor: pointer;
            transition: var(--transition);
        }
        
        .result-action:hover {
            background-color: var(--bg-tertiary);
            color: var(--primary-color);
        }
        
        .tool-result-content {
            padding: 16px;
            font-family: var(--font-mono);
            font-size: 14px;
            line-height: 1.6;
            color: var(--text-primary);
            max-height: 400px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
        
        .tool-help {
            margin-top: 24px;
            background-color: var(--bg-secondary);
            border-radius: var(--border-radius);
            padding: 20px;
            border-left: 4px solid var(--primary-color);
        }
        
        .tool-help-title {
            font-size: 16px;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 12px;
            display: flex;
            align-items: center;
        }
        
        .tool-help-title i {
            margin-right: 8px;
            color: var(--primary-color);
        }
        
        .tool-help-content {
            font-size: 14px;
            color: var(--text-secondary);
            line-height: 1.6;
        }
        
        .tool-help-content p {
            margin-bottom: 12px;
        }
        
        .tool-help-content p:last-child {
            margin-bottom: 0;
        }
        
        /* 提示和通知 */
        .notifications {
            position: fixed;
            top: 80px;
            right: 24px;
            z-index: 1000;
            display: flex;
            flex-direction: column;
            gap: 12px;
            max-width: 320px;
        }
        
        .notification {
            background-color: var(--card-bg);
            border-radius: var(--border-radius);
            box-shadow: var(--shadow-lg);
            padding: 16px;
            display: flex;
            align-items: flex-start;
            transform: translateX(120%);
            animation: slideIn 0.3s forwards;
            border-left: 4px solid var(--primary-color);
        }
        
        @keyframes slideIn {
            to {
                transform: translateX(0);
            }
        }
        
        .notification-icon {
            width: 24px;
            height: 24px;
            border-radius: 50%;
            background-color: rgba(var(--primary-color-rgb), 0.1);
            color: var(--primary-color);
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 12px;
            flex-shrink: 0;
        }
        
        .notification-content {
            flex: 1;
        }
        
        .notification-title {
            font-size: 14px;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 4px;
        }
        
        .notification-message {
            font-size: 13px;
            color: var(--text-secondary);
        }
        
        .notification-close {
            color: var(--text-light);
            cursor: pointer;
            margin-left: 12px;
            transition: var(--transition);
        }
        
        .notification-close:hover {
            color: var(--text-primary);
        }
        
        /* 动态添加CSS变量 */
        :root {
            --primary-color-rgb: 67, 97, 238;
            --success-color: #2ecc71;
            --warning-color: #f39c12;
            --error-color: #e74c3c;
            --info-color: #3498db;
        }
        
        .dark-mode {
            --primary-color-rgb: 76, 201, 240;
        }
        
        /* 响应式设计 */
        @media (max-width: 1200px) {
            .quick-tools {
                grid-template-columns: repeat(auto-fill, minmax(160px, 1fr));
            }
        }
        
        @media (max-width: 992px) {
            .sidebar {
                transform: translateX(-100%);
            }
            
            .sidebar.active {
                transform: translateX(0);
            }
            
            .main-content, .header {
                margin-left: 0;
            }
            
            .header {
                left: 0;
            }
            
            .mobile-menu {
                display: block;
            }
        }
        
        @media (max-width: 768px) {
            .tools-grid {
                grid-template-columns: 1fr;
            }
            
            .quick-tools {
                grid-template-columns: repeat(auto-fill, minmax(140px, 1fr));
            }
            
            .recent-tool {
                flex: 0 0 240px;
            }
            
            .tool-actions {
                display: none;
            }
            
            .tool-detail-header {
                flex-direction: column;
                align-items: flex-start;
            }
            
            .tool-actions-mobile {
                display: flex;
                margin-top: 16px;
                width: 100%;
                justify-content: space-between;
            }
            
            .tool-interface-inputs {
                grid-template-columns: 1fr;
            }
            
            .main-content {
                padding: 16px;
            }
        }
        
        @media (max-width: 576px) {
            .header {
                padding: 0 16px;
            }
            
            .user-name {
                display: none;
            }
            
            .search-container {
                max-width: none;
            }
            
            .section-header {
                flex-direction: column;
                align-items: flex-start;
            }
            
            .section-action {
                margin-top: 8px;
            }
            
            .quick-tools {
                grid-template-columns: repeat(2, 1fr);
            }
            
            .recent-tool {
                flex: 0 0 220px;
            }
            
            .tool-interface {
                padding: 16px;
            }
            
            .tool-result-content {
                max-height: 300px;
            }
        }
        
        /* 加载动画 */
        .loading {
            display: none;
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(var(--bg-primary-rgb), 0.8);
            backdrop-filter: blur(4px);
            -webkit-backdrop-filter: blur(4px);
            z-index: 10;
            display: flex;
            align-items: center;
            justify-content: center;
            flex-direction: column;
            border-radius: var(--border-radius);
        }
        
        .loading-spinner {
            width: 40px;
            height: 40px;
            border: 3px solid rgba(var(--primary-color-rgb), 0.3);
            border-radius: 50%;
            border-top-color: var(--primary-color);
            animation: spin 1s linear infinite;
            margin-bottom: 12px;
        }
        
        .loading-text {
            font-size: 14px;
            color: var(--text-primary);
            font-weight: 500;
        }
        
        @keyframes spin {
            to {
                transform: rotate(360deg);
            }
        }
        
        /* 滚动条样式 */
        ::-webkit-scrollbar {
            width: 8px;
        }
        
        ::-webkit-scrollbar-track {
            background: var(--bg-secondary);
        }
        
        ::-webkit-scrollbar-thumb {
            background: var(--border-color);
            border-radius: 4px;
        }
        
        ::-webkit-scrollbar-thumb:hover {
            background: var(--text-light);
        }
        
        .mobile-menu {
            display: none;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background-color: var(--bg-secondary);
            border: none;
            color: var(--text-primary);
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
            margin-right: 16px;
        }
        
        .mobile-menu:hover {
            background-color: var(--bg-tertiary);
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 侧边栏 -->
        <div class="sidebar">
            <div class="sidebar-header">
                <a href="#" class="logo">
                    <div class="logo-icon">
                        <i class="fas fa-tools"></i>
                    </div>
                    <div class="logo-text">ToolHub</div>
                </a>
                <button class="sidebar-toggle" id="sidebarToggle">
                    <i class="fas fa-chevron-left"></i>
                </button>
            </div>
            
            <div class="sidebar-section">
                <h3 class="section-title">域名/IP</h3>
                <ul class="sidebar-menu">
                    <li><a href="#" class="active"><i class="fas fa-search"></i> Whois查询</a></li>
                    <li><a href="#"><i class="fas fa-map-marker-alt"></i> IP定位</a></li>
                    <li><a href="#"><i class="fas fa-network-wired"></i> DNS解析</a></li>
                    <li><a href="#"><i class="fas fa-globe"></i> 域名信息</a></li>
                </ul>
            </div>
            
            <div class="sidebar-section">
                <h3 class="section-title">开发工具</h3>
                <ul class="sidebar-menu">
                    <li><a href="#"><i class="fas fa-code"></i> JSON格式化</a></li>
                    <li><a href="#"><i class="fas fa-compress-alt"></i> 代码压缩</a></li>
                    <li><a href="#"><i class="fas fa-exchange-alt"></i> API测试</a></li>
                    <li><a href="#"><i class="fas fa-terminal"></i> 正则测试</a></li>
                </ul>
            </div>
            
            <div class="sidebar-section">
                <h3 class="section-title">安全工具</h3>
                <ul class="sidebar-menu">
                    <li><a href="#"><i class="fas fa-shield-alt"></i> IP代理检测</a></li>
                    <li><a href="#"><i class="fas fa-lock"></i> SSL证书生成</a></li>
                    <li><a href="#"><i class="fas fa-key"></i> 加密工具</a></li>
                    <li><a href="#"><i class="fas fa-user-secret"></i> 隐私检测</a></li>
                </ul>
            </div>
            
            <div class="sidebar-section">
                <h3 class="section-title">实用工具</h3>
                <ul class="sidebar-menu">
                    <li><a href="#"><i class="fas fa-file-alt"></i> 文件格式转换</a></li>
                    <li><a href="#"><i class="fas fa-font"></i> 文本处理</a></li>
                    <li><a href="#"><i class="fas fa-random"></i> IP地址生成器</a></li>
                    <li><a href="#"><i class="fas fa-image"></i> 图片处理</a></li>
                </ul>
            </div>
            
            <div class="sidebar-section">
                <h3 class="section-title">个人中心</h3>
                <ul class="sidebar-menu">
                    <li><a href="#"><i class="fas fa-star"></i> 我的收藏</a></li>
                    <li><a href="#"><i class="fas fa-history"></i> 使用记录</a></li>
                    <li><a href="#"><i class="fas fa-cog"></i> 设置</a></li>
                    <li><a href="#"><i class="fas fa-question-circle"></i> 帮助中心</a></li>
                </ul>
            </div>
        </div>
        
        <!-- 顶部导航栏 -->
        <div class="header">
            <button class="mobile-menu" id="mobileMenu">
                <i class="fas fa-bars"></i>
            </button>
            
            <div class="search-container">
                <i class="fas fa-search search-icon"></i>
                <input type="text" class="search-bar" placeholder="搜索工具、关键词...">
            </div>
            
            <div class="user-nav">
                <button class="nav-button" title="通知">
                    <i class="far fa-bell"></i>
                    <span class="badge">3</span>
                </button>
                
                <button class="nav-button" id="themeToggle" title="切换主题">
                    <i class="fas fa-moon"></i>
                </button>
                
                <div class="user-info">
                    <div class="avatar">U</div>
                    <div class="user-name">用户名</div>
                </div>
            </div>
        </div>
        
        <!-- 主内容区域 -->
        <div class="main-content">
            <h1 class="page-title">欢迎使用 ToolHub</h1>
            
            <!-- 快捷工具 -->
            <div class="section">
                <div class="section-header">
                    <h2 class="section-title">快捷工具</h2>
                    <a href="#" class="section-action">查看全部 <i class="fas fa-chevron-right"></i></a>
                </div>
                
                <div class="quick-tools">
                    <div class="quick-tool">
                        <div class="quick-tool-icon">
                            <i class="fas fa-search"></i>
                        </div>
                        <div class="quick-tool-title">Whois查询</div>
                        <div class="quick-tool-description">查询域名注册信息</div>
                    </div>
                    
                    <div class="quick-tool">
                        <div class="quick-tool-icon">
                            <i class="fas fa-code"></i>
                        </div>
                        <div class="quick-tool-title">JSON格式化</div>
                        <div class="quick-tool-description">格式化JSON数据</div>
                    </div>
                    
                    <div class="quick-tool">
                        <div class="quick-tool-icon">
                            <i class="fas fa-lock"></i>
                        </div>
                        <div class="quick-tool-title">SSL证书生成</div>
                        <div class="quick-tool-description">生成SSL证书</div>
                    </div>
                    
                    <div class="quick-tool">
                        <div class="quick-tool-icon">
                            <i class="fas fa-file-alt"></i>
                        </div>
                        <div class="quick-tool-title">文件转换</div>
                        <div class="quick-tool-description">转换文件格式</div>
                    </div>
                    
                    <div class="quick-tool">
                        <div class="quick-tool-icon">
                            <i class="fas fa-map-marker-alt"></i>
                        </div>
                        <div class="quick-tool-title">IP定位</div>
                        <div class="quick-tool-description">查询IP地理位置</div>
                    </div>
                    
                    <div class="quick-tool">
                        <div class="quick-tool-icon">
                            <i class="fas fa-font"></i>
                        </div>
                        <div class="quick-tool-title">文本处理</div>
                        <div class="quick-tool-description">高级文本处理</div>
                    </div>
                </div>
            </div>
            
            <!-- 最近使用 -->
            <div class="section">
                <div class="section-header">
                    <h2 class="section-title">最近使用</h2>
                    <a href="#" class="section-action">查看全部 <i class="fas fa-chevron-right"></i></a>
                </div>
                
                <div class="recent-tools">
                    <div class="recent-tool">
                        <div class="recent-tool-header">
                            <div class="recent-tool-icon">
                                <i class="fas fa-search"></i>
                            </div>
                            <div class="recent-tool-info">
                                <div class="recent-tool-title">Whois查询</div>
                                <div class="recent-tool-time">2小时前</div>
                            </div>
                            <div class="recent-tool-status">
                                <i class="fas fa-ellipsis-v"></i>
                            </div>
                        </div>
                        <div class="recent-tool-progress">
                            <div class="progress-bar" style="width: 100%;"></div>
                        </div>
                    </div>
                    
                    <div class="recent-tool">
                        <div class="recent-tool-header">
                            <div class="recent-tool-icon">
                                <i class="fas fa-code"></i>
                            </div>
                            <div class="recent-tool-info">
                                <div class="recent-tool-title">JSON格式化</div>
                                <div class="recent-tool-time">昨天</div>
                            </div>
                            <div class="recent-tool-status">
                                <i class="fas fa-ellipsis-v"></i>
                            </div>
                        </div>
                        <div class="recent-tool-progress">
                            <div class="progress-bar" style="width: 75%;"></div>
                        </div>
                    </div>
                    
                    <div class="recent-tool">
                        <div class="recent-tool-header">
                            <div class="recent-tool-icon">
                                <i class="fas fa-lock"></i>
                            </div>
                            <div class="recent-tool-info">
                                <div class="recent-tool-title">SSL证书生成</div>
                                <div class="recent-tool-time">3天前</div>
                            </div>
                            <div class="recent-tool-status">
                                <i class="fas fa-ellipsis-v"></i>
                            </div>
                        </div>
                        <div class="recent-tool-progress">
                            <div class="progress-bar" style="width: 60%;"></div>
                        </div>
                    </div>
                    
                    <div class="recent-tool">
                        <div class="recent-tool-header">
                            <div class="recent-tool-icon">
                                <i class="fas fa-font"></i>
                            </div>
                            <div class="recent-tool-info">
                                <div class="recent-tool-title">文本处理</div>
                                <div class="recent-tool-time">一周前</div>
                            </div>
                            <div class="recent-tool-status">
                                <i class="fas fa-ellipsis-v"></i>
                            </div>
                        </div>
                        <div class="recent-tool-progress">
                            <div class="progress-bar" style="width: 90%;"></div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 域名/IP工具 -->
            <div class="section">
                <div class="section-header">
                    <h2 class="section-title">域名/IP工具</h2>
                    <a href="#" class="section-action">查看全部 <i class="fas fa-chevron-right"></i></a>
                </div>
                
                <div class="tools-grid">
                    <div class="tool-card" data-tool="whois">
                        <div class="tool-header">
                            <div class="tool-icon">
                                <i class="fas fa-search"></i>
                            </div>
                            <button class="tool-favorite">
                                <i class="far fa-heart"></i>
                            </button>
                        </div>
                        <div class="tool-title">Whois查询</div>
                        <div class="tool-description">查询域名注册信息，包括所有者、注册商、注册日期和到期日期等详细信息。</div>
                        <div class="tool-tags">
                            <span class="tool-tag">域名</span>
                            <span class="tool-tag">查询</span>
                        </div>
                    </div>
                    
                    <div class="tool-card" data-tool="ip-location">
                        <div class="tool-header">
                            <div class="tool-icon">
                                <i class="fas fa-map-marker-alt"></i>
                            </div>
                            <button class="tool-favorite">
                                <i class="far fa-heart"></i>
                            </button>
                        </div>
                        <div class="tool-title">IP定位</div>
                        <div class="tool-description">通过IP地址查询地理位置，包括国家、城市、经纬度和ISP提供商信息。</div>
                        <div class="tool-tags">
                            <span class="tool-tag">IP</span>
                            <span class="tool-tag">地理位置</span>
                        </div>
                    </div>
                    
                    <div class="tool-card" data-tool="dns-lookup">
                        <div class="tool-header">
                            <div class="tool-icon">
                                <i class="fas fa-network-wired"></i>
                            </div>
                            <button class="tool-favorite active">
                                <i class="fas fa-heart"></i>
                            </button>
                        </div>
                        <div class="tool-title">DNS解析</div>
                        <div class="tool-description">查询域名的DNS记录，包括A、AAAA、MX、TXT、NS等类型记录，帮助诊断域名解析问题。</div>
                        <div class="tool-tags">
                            <span class="tool-tag">域名</span>
                            <span class="tool-tag">DNS</span>
                        </div>
                    </div>
                    
                    <div class="tool-card" data-tool="domain-info">
                        <div class="tool-header">
                            <div class="tool-icon">
                                <i class="fas fa-globe"></i>
                            </div>
                            <button class="tool-favorite">
                                <i class="far fa-heart"></i>
                            </button>
                        </div>
                        <div class="tool-title">域名信息</div>
                        <div class="tool-description">全面分析域名信息，包括服务器类型、HTTP头、SSL证书详情和网站技术栈等。</div>
                        <div class="tool-tags">
                            <span class="tool-tag">域名</span>
                            <span class="tool-tag">分析</span>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 工具详情页 -->
            <div class="tool-detail" id="whoisDetail">
                <div class="tool-detail-header">
                    <div class="tool-detail-title">
                        <div class="tool-detail-title-icon">
                            <i class="fas fa-search"></i>
                        </div>
                        <div class="tool-detail-title-text">
                            <h2>Whois查询</h2>
                            <p>查询域名注册信息和所有权详情</p>
                        </div>
                    </div>
                    
                    <div class="tool-actions">
                        <button class="tool-action-btn">
                            <i class="fas fa-question-circle"></i>
                            使用说明
                        </button>
                        <button class="tool-action-btn">
                            <i class="far fa-star"></i>
                            收藏工具
                        </button>
                        <button class="tool-action-btn">
                            <i class="fas fa-history"></i>
                            历史记录
                        </button>
                        <button class="tool-action-btn">
                            <i class="fas fa-share-alt"></i>
                            分享
                        </button>
                    </div>
                </div>
                
                <div class="tool-detail-content">
                    <div class="tool-interface">
                        <div class="tool-interface-title">
                            <i class="fas fa-cog"></i> 查询参数
                        </div>
                        
                        <div class="tool-interface-inputs">
                            <div class="input-group">
                                <label for="domain">域名</label>
                                <input type="text" id="domain" placeholder="输入域名，如 wenhaofree.com">
                                <div class="input-hint">不需要输入 http:// 或 https://</div>
                            </div>
                            
                            <div class="input-group">
                                <label for="server">Whois服务器 (可选)</label>
                                <input type="text" id="server" placeholder="指定Whois服务器">
                                <div class="input-hint">留空将使用默认服务器</div>
                            </div>
                        </div>
                        
                        <div class="tool-interface-buttons">
                            <button class="btn btn-primary" id="queryBtn">
                                <i class="fas fa-search"></i> 开始查询
                            </button>
                            <button class="btn btn-secondary" id="resetBtn">
                                <i class="fas fa-redo-alt"></i> 重置
                            </button>
                        </div>
                    </div>
                    
                    <div class="tool-result">
                        <div class="tool-result-header">
                            <div class="tool-result-title">
                                <i class="fas fa-file-alt"></i> 查询结果
                            </div>
                            <div class="tool-result-actions">
                                <button class="result-action" title="复制结果">
                                    <i class="fas fa-copy"></i>
                                </button>
                                <button class="result-action" title="导出为文本">
                                    <i class="fas fa-download"></i>
                                </button>
                                <button class="result-action" title="清空结果">
                                    <i class="fas fa-trash-alt"></i>
                                </button>
                            </div>
                        </div>
                        
                        <div class="tool-result-content" id="whoisResult">
Domain Name: wenhaofree.com
Registry Domain ID: 2336799_DOMAIN_COM-VRSN
Registrar WHOIS Server: whois.iana.org
Registrar URL: http://www.iana.org
Updated Date: 2023-08-14T07:02:37Z
Creation Date: 1995-08-14T04:00:00Z
Registry Expiry Date: 2024-08-13T04:00:00Z
Registrar: ICANN
Registrar IANA ID: 376
Registrar Abuse Contact Email: <EMAIL>
Registrar Abuse Contact Phone: *************
Domain Status: clientDeleteProhibited https://icann.org/epp#clientDeleteProhibited
Domain Status: clientTransferProhibited https://icann.org/epp#clientTransferProhibited
Domain Status: clientUpdateProhibited https://icann.org/epp#clientUpdateProhibited
Name Server: A.IANA-SERVERS.NET
Name Server: B.IANA-SERVERS.NET
DNSSEC: signedDelegation
DNSSEC DS Data: 31589 8 1 3490A6806D47F17A34C29E2CE80E8A999FFBE4BE
DNSSEC DS Data: 31589 8 2 CDE0D742D6998AA554A92D890F8184C698CFAC8A26FA59875A990C03E576343C
URL of the ICANN Whois Inaccuracy Complaint Form: https://www.icann.org/wicf/
</div>
                    </div>
                    
                    <div class="tool-help">
                        <div class="tool-help-title">
                            <i class="fas fa-lightbulb"></i> 使用提示
                        </div>
                        <div class="tool-help-content">
                            <p>Whois查询可以帮助您获取域名的注册信息，包括域名所有者、注册商、创建和到期日期等详细资料。此工具常用于域名所有权验证、技术联系人查找和域名状态检查。</p>
                            <p>使用方法：输入任何有效的域名（不含http://或https://前缀），然后点击"开始查询"按钮。如果您需要查询特定的Whois服务器，可以在第二个输入框中指定。</p>
                            <p>请注意，某些域名可能启用了隐私保护服务，这会限制您获取的信息，特别是个人联系信息通常会被隐藏。</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 通知区域 -->
    <div class="notifications" id="notifications">
        <!-- 通知内容会动态添加 -->
    </div>
    
    <script>
        // DOM元素引用
        const body = document.body;
        const themeToggle = document.getElementById('themeToggle');
        const sidebarToggle = document.getElementById('sidebarToggle');
        const sidebar = document.querySelector('.sidebar');
        const mobileMenu = document.getElementById('mobileMenu');
        const toolCards = document.querySelectorAll('.tool-card');
        const favoriteButtons = document.querySelectorAll('.tool-favorite');
        const queryBtn = document.getElementById('queryBtn');
        const resetBtn = document.getElementById('resetBtn');
        const domainInput = document.getElementById('domain');
        const whoisResult = document.getElementById('whoisResult');
        const resultActions = document.querySelectorAll('.result-action');
        const whoisDetail = document.getElementById('whoisDetail');
        const notificationsContainer = document.getElementById('notifications');
        
        // 工具内容展示状态
        let toolDetailVisible = false;
        
        // 深色/浅色模式切换
        themeToggle.addEventListener('click', function() {
            body.classList.toggle('dark-mode');
            const icon = this.querySelector('i');
            
            if (body.classList.contains('dark-mode')) {
                icon.classList.remove('fa-moon');
                icon.classList.add('fa-sun');
                showNotification('主题已切换', '已切换到深色模式', 'info');
            } else {
                icon.classList.remove('fa-sun');
                icon.classList.add('fa-moon');
                showNotification('主题已切换', '已切换到浅色模式', 'info');
            }
        });
        
        // 侧边栏折叠/展开
        sidebarToggle.addEventListener('click', function() {
            sidebar.classList.toggle('collapsed');
            const icon = this.querySelector('i');
            
            if (sidebar.classList.contains('collapsed')) {
                icon.classList.remove('fa-chevron-left');
                icon.classList.add('fa-chevron-right');
                sidebar.style.width = '70px';
                document.documentElement.style.setProperty('--sidebar-width', '70px');
            } else {
                icon.classList.remove('fa-chevron-right');
                icon.classList.add('fa-chevron-left');
                sidebar.style.width = '260px';
                document.documentElement.style.setProperty('--sidebar-width', '260px');
            }
        });
        
        // 移动端菜单
        mobileMenu.addEventListener('click', function() {
            sidebar.classList.toggle('active');
        });
        
        // 工具卡片点击事件
        toolCards.forEach(card => {
            card.addEventListener('click', function() {
                const toolId = this.getAttribute('data-tool');
                
                // 滚动到工具详情
                if (!toolDetailVisible) {
                    whoisDetail.style.display = 'block';
                    toolDetailVisible = true;
                    whoisDetail.scrollIntoView({ behavior: 'smooth' });
                    showNotification('工具已加载', `${this.querySelector('.tool-title').textContent} 工具已准备就绪`, 'success');
                }
            });
        });
        
        // 收藏工具
        favoriteButtons.forEach(button => {
            button.addEventListener('click', function(e) {
                e.stopPropagation();
                this.classList.toggle('active');
                const icon = this.querySelector('i');
                const toolName = this.closest('.tool-card').querySelector('.tool-title').textContent;
                
                if (this.classList.contains('active')) {
                    icon.classList.remove('far');
                    icon.classList.add('fas');
                    showNotification('添加收藏', `已将 ${toolName} 添加到收藏`, 'success');
                } else {
                    icon.classList.remove('fas');
                    icon.classList.add('far');
                    showNotification('取消收藏', `已将 ${toolName} 从收藏中移除`, 'info');
                }
            });
        });
        
        // 查询按钮点击事件
        queryBtn.addEventListener('click', function() {
            const domain = domainInput.value.trim();
            
            if (!domain) {
                showNotification('输入错误', '请输入域名进行查询', 'error');
                domainInput.focus();
                return;
            }
            
            // 模拟查询过程
            const loadingElement = document.createElement('div');
            loadingElement.className = 'loading';
            loadingElement.innerHTML = `
                <div class="loading-spinner"></div>
                <div class="loading-text">正在查询 ${domain} 的Whois信息...</div>
            `;
            
            document.querySelector('.tool-result').appendChild(loadingElement);
            whoisResult.style.opacity = '0.3';
            
            // 模拟查询延迟
            setTimeout(() => {
                loadingElement.remove();
                whoisResult.style.opacity = '1';
                
                // 更新结果内容
                whoisResult.innerHTML = `Domain Name: ${domain.toUpperCase()}
Registry Domain ID: 2336799_DOMAIN_COM-VRSN
Registrar WHOIS Server: whois.iana.org
Registrar URL: http://www.iana.org
Updated Date: 2023-08-14T07:02:37Z
Creation Date: 2022-08-14T04:00:00Z
Registry Expiry Date: 2024-08-13T04:00:00Z
Registrar: ICANN
Registrar IANA ID: 376
Registrar Abuse Contact Email: <EMAIL>
Registrar Abuse Contact Phone: *************
Domain Status: clientDeleteProhibited https://icann.org/epp#clientDeleteProhibited
Domain Status: clientTransferProhibited https://icann.org/epp#clientTransferProhibited
Domain Status: clientUpdateProhibited https://icann.org/epp#clientUpdateProhibited
Name Server: NS1.EXAMPLE-REGISTRAR.COM
Name Server: NS2.EXAMPLE-REGISTRAR.COM
DNSSEC: signedDelegation
DNSSEC DS Data: 31589 8 1 3490A6806D47F17A34C29E2CE80E8A999FFBE4BE
DNSSEC DS Data: 31589 8 2 CDE0D742D6998AA554A92D890F8184C698CFAC8A26FA59875A990C03E576343C
URL of the ICANN Whois Inaccuracy Complaint Form: https://www.icann.org/wicf/
`;
                
                showNotification('查询完成', `${domain} 的Whois信息已获取`, 'success');
                
                // 添加到最近使用
                const recentTools = document.querySelector('.recent-tools');
                const newRecentTool = document.createElement('div');
                newRecentTool.className = 'recent-tool';
                newRecentTool.innerHTML = `
                    <div class="recent-tool-header">
                        <div class="recent-tool-icon">
                            <i class="fas fa-search"></i>
                        </div>
                        <div class="recent-tool-info">
                            <div class="recent-tool-title">Whois: ${domain}</div>
                            <div class="recent-tool-time">刚刚</div>
                        </div>
                        <div class="recent-tool-status">
                            <i class="fas fa-ellipsis-v"></i>
                        </div>
                    </div>
                    <div class="recent-tool-progress">
                        <div class="progress-bar" style="width: 100%;"></div>
                    </div>
                `;
                
                recentTools.insertBefore(newRecentTool, recentTools.firstChild);
            }, 1500);
        });
        
        // 重置按钮点击事件
        resetBtn.addEventListener('click', function() {
            domainInput.value = '';
            document.getElementById('server').value = '';
            domainInput.focus();
        });
        
        // 结果操作按钮点击事件
        resultActions.forEach(button => {
            button.addEventListener('click', function() {
                const action = this.getAttribute('title');
                
                if (action === '复制结果') {
                    copyToClipboard(whoisResult.textContent);
                    showNotification('已复制', 'Whois查询结果已复制到剪贴板', 'success');
                } else if (action === '导出为文本') {
                    exportAsText(whoisResult.textContent, 'whois-result.txt');
                    showNotification('已导出', 'Whois查询结果已导出为文本文件', 'success');
                } else if (action === '清空结果') {
                    whoisResult.textContent = '';
                    showNotification('已清空', '查询结果已清空', 'info');
                }
            });
        });
        
        // 侧边栏菜单项点击事件
        const menuItems = document.querySelectorAll('.sidebar-menu a');
        menuItems.forEach(item => {
            item.addEventListener('click', function(e) {
                e.preventDefault();
                menuItems.forEach(i => i.classList.remove('active'));
                this.classList.add('active');
                
                // 移动端自动关闭侧边栏
                if (window.innerWidth < 992) {
                    sidebar.classList.remove('active');
                }
                
                // 如果是Whois查询菜单项
                if (this.textContent.trim() === 'Whois查询') {
                    if (!toolDetailVisible) {
                        whoisDetail.style.display = 'block';
                        toolDetailVisible = true;
                        whoisDetail.scrollIntoView({ behavior: 'smooth' });
                    }
                } else {
                    showNotification('功能提示', `${this.textContent.trim()} 功能正在开发中`, 'info');
                }
            });
        });
        
        // 快捷工具点击事件
        const quickTools = document.querySelectorAll('.quick-tool');
        quickTools.forEach(tool => {
            tool.addEventListener('click', function() {
                const toolName = this.querySelector('.quick-tool-title').textContent;
                
                // 如果是Whois查询工具
                if (toolName === 'Whois查询') {
                    if (!toolDetailVisible) {
                        whoisDetail.style.display = 'block';
                        toolDetailVisible = true;
                        whoisDetail.scrollIntoView({ behavior: 'smooth' });
                    }
                } else {
                    showNotification('功能提示', `${toolName} 功能正在开发中`, 'info');
                }
            });
        });
        
        // 最近使用工具点击事件
        const recentTools = document.querySelectorAll('.recent-tool');
        recentTools.forEach(tool => {
            tool.addEventListener('click', function() {
                const toolName = this.querySelector('.recent-tool-title').textContent;
                
                // 如果是Whois查询工具
                if (toolName.includes('Whois')) {
                    if (!toolDetailVisible) {
                        whoisDetail.style.display = 'block';
                        toolDetailVisible = true;
                        whoisDetail.scrollIntoView({ behavior: 'smooth' });
                    }
                } else {
                    showNotification('功能提示', `${toolName} 功能正在开发中`, 'info');
                }
            });
        });
        
        // 工具函数：复制到剪贴板
        function copyToClipboard(text) {
            const textarea = document.createElement('textarea');
            textarea.value = text;
            document.body.appendChild(textarea);
            textarea.select();
            document.execCommand('copy');
            document.body.removeChild(textarea);
        }
        
        // 工具函数：导出为文本文件
        function exportAsText(content, filename) {
            const blob = new Blob([content], { type: 'text/plain' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = filename;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
        }
        
        // 工具函数：显示通知
        function showNotification(title, message, type = 'info') {
            const notification = document.createElement('div');
            notification.className = 'notification';
            
            // 根据类型设置图标和颜色
            let iconClass = 'fas fa-info-circle';
            let iconColor = 'var(--info-color)';
            
            if (type === 'success') {
                iconClass = 'fas fa-check-circle';
                iconColor = 'var(--success-color)';
                notification.style.borderLeftColor = 'var(--success-color)';
            } else if (type === 'error') {
                iconClass = 'fas fa-exclamation-circle';
                iconColor = 'var(--error-color)';
                notification.style.borderLeftColor = 'var(--error-color)';
            } else if (type === 'warning') {
                iconClass = 'fas fa-exclamation-triangle';
                iconColor = 'var(--warning-color)';
                notification.style.borderLeftColor = 'var(--warning-color)';
            } else {
                notification.style.borderLeftColor = 'var(--info-color)';
            }
            
            notification.innerHTML = `
                <div class="notification-icon" style="color: ${iconColor}">
                    <i class="${iconClass}"></i>
                </div>
                <div class="notification-content">
                    <div class="notification-title">${title}</div>
                    <div class="notification-message">${message}</div>
                </div>
                <div class="notification-close">
                    <i class="fas fa-times"></i>
                </div>
            `;
            
            notificationsContainer.appendChild(notification);
            
            // 添加关闭按钮事件
            notification.querySelector('.notification-close').addEventListener('click', function() {
                notification.style.transform = 'translateX(120%)';
                setTimeout(() => {
                    notificationsContainer.removeChild(notification);
                }, 300);
            });
            
            // 自动关闭通知
            setTimeout(() => {
                if (notification.parentNode === notificationsContainer) {
                    notification.style.transform = 'translateX(120%)';
                    setTimeout(() => {
                        if (notification.parentNode === notificationsContainer) {
                            notificationsContainer.removeChild(notification);
                        }
                    }, 300);
                }
            }, 5000);
        }
        
        // 页面加载完成后自动显示一条欢迎通知
        window.addEventListener('load', function() {
            setTimeout(() => {
                showNotification('欢迎使用 ToolHub', '专业工具聚合平台已准备就绪', 'success');
            }, 500);
        });
    </script>
</body>
</html>
