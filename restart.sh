#!/bin/bash

# 设置变量
PORT=8091
LOG_DIR="./logs"
LOG_FILE="$LOG_DIR/app-$(date +%Y%m%d-%H%M%S).log"
ERROR_LOG="$LOG_DIR/error-$(date +%Y%m%d-%H%M%S).log"

# 创建日志目录
mkdir -p $LOG_DIR

echo "===== 重启脚本开始执行 $(date) =====" | tee -a $LOG_FILE

# 拉取最新代码
echo "步骤1: 正在拉取最新代码..." | tee -a $LOG_FILE
if git pull >> $LOG_FILE 2>> $ERROR_LOG; then
    echo "  ✓ 代码拉取成功" | tee -a $LOG_FILE
else
    echo "  ✗ 代码拉取失败，请检查 $ERROR_LOG" | tee -a $LOG_FILE
    exit 1
fi

# 安装依赖
echo "步骤2: 正在安装依赖..." | tee -a $LOG_FILE
if pnpm install >> $LOG_FILE 2>> $ERROR_LOG; then
    echo "  ✓ 依赖安装成功" | tee -a $LOG_FILE
else
    echo "  ✗ 依赖安装失败，请检查 $ERROR_LOG" | tee -a $LOG_FILE
    exit 1
fi

# 构建项目
echo "步骤3: 正在构建项目..." | tee -a $LOG_FILE
if pnpm run build >> $LOG_FILE 2>> $ERROR_LOG; then
    echo "  ✓ 项目构建成功" | tee -a $LOG_FILE
else
    echo "  ✗ 项目构建失败，请检查 $ERROR_LOG" | tee -a $LOG_FILE
    exit 1
fi

# 检查端口
echo "步骤4: 检查端口${PORT}是否被占用..." | tee -a $LOG_FILE
# 使用 ss 命令检查端口并提取 PID
PORT_INFO=$(ss -tulnp | grep ":$PORT ")
if [ -n "$PORT_INFO" ]; then
    # 从 ss 输出中提取 PID
    PID=$(echo "$PORT_INFO" | sed -n 's/.*pid=\([0-9]*\).*/\1/p')
    echo "  ! 端口${PORT}被进程${PID}占用，正在结束该进程..." | tee -a $LOG_FILE
    if kill -9 $PID >> $LOG_FILE 2>> $ERROR_LOG; then
        echo "  ✓ 进程${PID}已成功终止" | tee -a $LOG_FILE
    else
        echo "  ✗ 终止进程失败，请检查 $ERROR_LOG" | tee -a $LOG_FILE
        exit 1
    fi
else
    echo "  ✓ 端口${PORT}未被占用" | tee -a $LOG_FILE
fi

# 设置环境变量
export NEXTAUTH_SECRET="bxqCiRgQmcJaSDas5WdHjVGPj1RaTzy+MdNwJzlATlI="
export AUTH_SECRET="bxqCiRgQmcJaSDas5WdHjVGPj1RaTzy+MdNwJzlATlI="
export NODE_ENV="production"
export SITE_URL="https://bili-tool.com"

# 后台启动服务
echo "步骤5: 正在后台启动应用，端口：${PORT}..." | tee -a $LOG_FILE
PORT=$PORT nohup pnpm run start >> $LOG_FILE 2>> $ERROR_LOG &

# 获取新启动的进程PID - 等待应用启动
sleep 2
# 使用 ss 命令获取新启动的进程信息
PORT_INFO=$(ss -tulnp | grep ":$PORT ")
if [ -n "$PORT_INFO" ]; then
    # 从 ss 输出中提取 PID
    NEW_PID=$(echo "$PORT_INFO" | sed -n 's/.*pid=\([0-9]*\).*/\1/p')
    echo "  ✓ 应用启动成功！" | tee -a $LOG_FILE
    echo "  ✓ 服务信息：PID=${NEW_PID}, 端口=${PORT}" | tee -a $LOG_FILE
    echo "  ✓ 端口详情：$(ss -tulnp | grep ":$PORT ")" | tee -a $LOG_FILE
    echo "  ✓ 查看日志：tail -f $LOG_FILE" | tee -a $LOG_FILE
else
    echo "  ✗ 应用启动失败或未找到进程，请检查 $ERROR_LOG" | tee -a $LOG_FILE
    exit 1
fi

echo "===== 重启脚本执行完成 $(date) =====" | tee -a $LOG_FILE
echo "服务已在后台启动 - PID: ${NEW_PID}, 端口: ${PORT}" 
echo "日志文件: $LOG_FILE"